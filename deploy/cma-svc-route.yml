---
kind: Template
apiVersion: v1
metadata:
  name: ${APP_NAME}-route
  namespace: ${GROUP_NAME}-${STAGE}
labels:
  template: ${APP_NAME}-route
  app: ${APP_NAME}
objects:
- apiVersion: route.openshift.io/v1
  kind: Route
  metadata:
    name: ${APP_NAME}
    namespace: ${GROUP_NAME}-${STAGE}
  spec:
    path: /
    to:
      kind: Service
      name: ${APP_NAME}
    port:
      targetPort: 8080
    tls:
      termination: edge
      insecureEdgeTerminationPolicy: None
- apiVersion: route.openshift.io/v1
  kind: Route
  metadata:
    name: ${APP_NAME}-mon
    namespace: ${GROUP_NAME}-${STAGE}
  spec:
    path: /
    to:
      kind: Service
      name: ${APP_NAME}
    port:
      targetPort: 8081
    tls:
      termination: edge
      insecureEdgeTerminationPolicy: None
parameters:
  - name: APP_NAME
  - name: GROUP_NAME 
  - name: STAGE
