<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1666164254922">
        <createTable tableName="MsEmployeeHierarchy">
            <column name="NIK" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_MsEmployeeHierarchy_NIK"/>
            </column>
            <column name="FullName" type="varchar(200)"/>
            <column name="DTJoin" type="datetime"/>
            <column name="DTPermanent" type="varchar(25)"/>
            <column name="OccupationDesc" type="varchar(100)"/>
            <column name="Organization" type="varchar(100)"/>
            <column name="Location" type="varchar(100)"/>
            <column name="DirectSupervisorNIK" type="varchar(15)"/>
            <column name="DirectSupervisorName" type="varchar(255)"/>
            <column name="DirectSupervisorOccupation" type="varchar(100)"/>
            <column name="DirectSupervisorOrganization" type="varchar(100)"/>
            <column name="DirectSupervisor2NIK" type="varchar(15)"/>
            <column name="DirectSupervisor2Name" type="varchar(200)"/>
            <column name="DirectSupervisor2Occupation" type="varchar(100)"/>
            <column name="DirectSupervisor2Organization" type="varchar(100)"/>
            <column name="DTTermination" type="varchar(25)"/>
            <column name="DTPopulateSource" type="datetime"/>
            <column name="FieldChecksum" type="varchar(100)"/>
            <column name="DTPopulate" type="datetime"/>
            <column name="SrcSystem" type="varchar(50)"/>
            <column name="SysPopulate" type="varchar(50)"/>
        </createTable>
        
        <createTable tableName="MsBranchEmployee">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsBranchEmployee_Id"/>
            </column>
            <column name="BranchId" type="varchar(20)"/>
            <column name="BranchName" type="varchar(max)"/>
            <column name="Location" type="varchar(max)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>