<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="add-column-MsOfficerNR">
        <addColumn tableName="MsOfficerNR" >
            <column name="MenuID"
                    type="varchar(5)"/>
            <column name="DTStartProfile"
                    type="date"/>
            <column name="DTEndProfile"
                    type="date"/>
            <column name="DTValidityPassword"
                    type="date"/>
            <column name="KFOID"
                    type="int"/>
            <column name="KFOCode"
                    type="varchar(200)"/>
            <column name="KFOName"
                    type="varchar(200)"/>
            <column name="KCSID"
                    type="int"/>
            <column name="KCSCode"
                    type="varchar(1100)"/>
            <column name="KCSName"
                    type="varchar(255)"/>
            <column name="DTJoin"
                    type="date"/>
            <column name="DepartmentName"
                    type="varchar(100)"/>
        </addColumn>
    </changeSet>
    
</databaseChangeLog>