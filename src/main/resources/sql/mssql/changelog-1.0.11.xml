<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1658146907410">
       <sql>
       delete from MsOfficer where MMSCode ='K0014';
       delete from MsOfficer where OfficerId = -2925;
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-9432,NULL,2470,38,'K0014','RPA014','2022-03-02 17:07:07.913','dp.dbo','RPA0014',NULL,'RPA0014','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-02-15 10:20:10.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-9116,NULL,2470,38,'K0014','SJP007','2022-05-13 18:51:36.880','dp.dbo','DIANA ERMAWATI','SJP007011121','SJP007011121','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-05-13 09:24:50.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-7796,NULL,2470,38,'K0014','245633','2022-05-13 18:51:36.880','dp.dbo','SUGIANTO HARJONO H','098210013','098210013','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-05-13 08:52:51.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-7608,NULL,2470,38,'K0014','145789','2022-06-27 11:28:52.507','dp.dbo','EMMA KHUSNA MELASARI','13035831','13035831a','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-02-18 02:44:26.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-3851,NULL,2470,38,'K0014','10846','2022-06-27 11:28:52.507','dp.dbo','BAQIYATUS SHALIHAH','09010846','09010846','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-04-01 01:39:17.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-3044,NULL,2470,39,'K0014','98803','2022-06-03 10:24:01.050','dp.dbo','DAESY TRIYANI DEWI','82098803','82098803','','SCS/BOM - FD',1,'Active',0.0000,'2022-06-03 03:14:27.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-3038,32373,2470,38,'K0014','503268','2022-05-24 09:58:17.127','dp.dbo','IWAN SETIAWAN','21503268','21503268','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-05-24 02:57:01.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-3037,32372,2470,38,'K0014','19563','2022-06-27 11:28:52.507','dp.dbo','ERWIN PRAWIRASATYA HADIE ','11019563','11019563','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-04-01 01:33:01.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-2925,NULL,2470,39,'K0014','117773','2022-06-23 07:09:04.673','dp.dbo','YUSTI DWI RAHAYU','12024956','12024956a','','SCS/BOM - FD',1,'Active',0.0000,'2022-06-22 23:55:48.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(-2621,33304,2470,38,'K0014','170757','2022-05-13 18:51:36.880','dp.dbo','RISA NOVITA','098170757','098170757','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-05-13 07:23:48.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(7510,32371,2470,38,'K0014','W0605H','2022-05-17 09:17:40.083','dp.dbo','MARISA RAMADONA SARAGIH','12029348','12029348','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-05-17 02:03:02.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(14720,NULL,2470,38,'K0014','84886','2022-06-27 11:28:52.507','dp.dbo','CISSY ROCHYANA','79084886','79084886','','SCS/BOM - FD',1,'Active',0.0000,'2022-05-11 07:02:22.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(15320,NULL,2470,27,'K0014','W0262P','2022-05-24 09:58:17.127','dp.dbo','EMMA KHUSNA MELASARI','13035831','13035831','','CS KC/ KFO (Teller)',1,'Active',0.0000,'2022-05-24 02:53:22.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(18600,NULL,2470,38,'K0014','W0745K','2022-05-13 18:51:36.880','dp.dbo','ASTIN MULYANI','13042993','W0745K','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-05-13 09:59:04.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(18974,NULL,2470,38,'K0014','W0274Q','2022-05-13 18:51:36.880','dp.dbo','Fitri Syawalani','13034320','W0274Q','','CS KC/ KFO (BO - FD)',1,'Active',0.0000,'2022-05-13 09:30:09.000');
       insert into MsOfficer(OfficerId,MprosperaOfficerID,MMSID,RoleID,MMSCode,OfficerCode,DTPopulate,SysPopulate,OfficerName,NIK,LoginName,EmailName,RoleName,OfficerStatusCode,OfficerStatusDesc,AmtApprovalLimit,DTKafka) values(29723,NULL,2470,39,'K0014','W0276W','2022-05-13 18:51:36.880','dp.dbo','NOVIYANTI','11023140','W0276W','','SCS/BOM - FD',1,'Active',0.0000,'2022-05-13 09:37:47.000');
       </sql>
    </changeSet>
</databaseChangeLog>