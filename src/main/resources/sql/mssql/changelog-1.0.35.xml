<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1685957674431">
        <sql>
            delete from MsRoleMenu;
            delete from MsSystemParamDetail;
            delete from MsUserAdmin;
        </sql>
        <loadData file="MsRoleMenu230605.csv"
                  relativeToChangelogFile="true"
                  tableName="MsRoleMenu">
            <column header="id"
                    name="Id"/>
            <column header="roleId"
                    name="RoleId"/>
            <column header="menuDesc"
                    name="MenuDesc"/>
        </loadData>
        <loadData file="MsSystemParamDetail230605.csv"
                  relativeToChangelogFile="true"
                  tableName="MsSystemParamDetail">
            <column header="paramId"
                    name="ParamId"/>
            <column header="paramDetailId"
                    name="ParamDetailId"/>
            <column header="paramDetailDesc"
                    name="ParamDetailDesc"/>
        </loadData>
        <loadData file="MsUserAdmin230605.csv"
                  relativeToChangelogFile="true"
                  tableName="MsUserAdmin">
            <column header="nik"
                    name="NIK"/>
            <column header="name"
                    name="Name"/>
            <column header="role"
                    name="Role"/>
            <column header="activeFlag"
                    name="ActiveFlag"/>
            <column header="createDateTime"
                    name="CreateDateTime"/>
            <column header="updateDateTime"
                    name="UpdateDateTime"/>
        </loadData>
    </changeSet>
</databaseChangeLog>
