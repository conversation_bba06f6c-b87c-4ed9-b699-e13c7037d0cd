<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
<changeSet author="abdillah" id="1648035394349">
    <createTable tableName="MsOfficer">
        <column name="OfficerID" type="int">
            <constraints primaryKey="true" primaryKeyName="PK_MsOfficer_OfficerID"/>
        </column>
        <column name="MprosperaOfficerID" type="decimal(19,0)"/>
        <column name="MMSID" type="int"/>
        <column name="RoleID" type="int"/>
        <column name="MMSCode" type="varchar(5)"/>
        <column name="OfficerCode" type="varchar(6)"/>
        <column name="DTPopulate" type="datetime"/>
        <column name="SysPopulate" type="varchar(50)"/>
        <column name="OfficerName" type="varchar(200)"/>
        <column name="NIK" type="varchar(200)"/>
        <column name="LoginName" type="varchar(200)"/>
        <column name="EmailName" type="varchar(255)"/>
        <column name="RoleName" type="varchar(150)"/>
        <column name="OfficerStatusCode" type="int"/>
        <column name="OfficerStatusDesc" type="varchar(100)"/>
        <column name="AmtApprovalLimit" type="decimal(22,4)"/>
        <column name="DTKafka" type="datetime"/>
    </createTable>
    <createIndex indexName="IDX_MsOfficer_OfficerCode" tableName="MsOfficer">
        <column name="OfficerCode"/>
    </createIndex>
    <createIndex indexName="IDX_MsOfficer_MMSCode" tableName="MsOfficer">
        <column name="MMSCode"/>
    </createIndex>
    <createIndex indexName="IDX_MsOfficer_RoleName" tableName="MsOfficer">
        <column name="RoleName"/>
    </createIndex>
    <createIndex indexName="IDX_MsOfficer_NIK" tableName="MsOfficer">
        <column name="NIK"/></createIndex>
</changeSet>
</databaseChangeLog>