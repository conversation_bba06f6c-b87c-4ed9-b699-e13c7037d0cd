<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="1725335423474" author="Dian">
        <createTable tableName="TrxOverlimitHTBalance">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxOverlimitHTBalance_Id"/>
            </column>
            <column name="BranchId" type="varchar(20)"/>
            <column name="Period" type="date"/>
            <column name="TransactionId" type="varchar(20)"/>
            <column name="TotalBalance" type="numeric(18,2)"/>
            <column name="CreateDateTime" type="datetime"/>
        </createTable>
    </changeSet>

    <changeSet id="1725335431164" author="Dian">
        <addColumn tableName="MsCabang" >
            <column name="TotalBalanceHT"
                    type="numeric(18,2)"/>
        </addColumn>
    </changeSet>
    
    <changeSet id="1725337070007" author="Dian">
        <sql>
            update MsCabang set TotalBalanceHT = TotalBalance * 30/100
        </sql>
    </changeSet>
</databaseChangeLog>