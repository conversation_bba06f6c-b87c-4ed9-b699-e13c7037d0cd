<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1683007056479">
        <createTable tableName="MsOfficerNonProspera">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsOfficerNonProspera_Id"/>
            </column>
            <column name="NIK" type="varchar(20)"/>
            <column name="Name" type="varchar(max)"/>
            <column name="BranchId" type="varchar(20)"/>
            <column name="RoleId" type="varchar(20)"/>
            <column name="RoleName" type="varchar(20)"/>
            <column name="ActiveFlag" type="varchar(20)"/>
            <column name="CreateDateTime" type="datetime"/>
            <column name="UpdateDateTime" type="datetime"/>
        </createTable>
    </changeSet>
</databaseChangeLog>