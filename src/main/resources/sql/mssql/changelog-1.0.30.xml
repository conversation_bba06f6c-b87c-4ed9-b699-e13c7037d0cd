<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1678744296205">
        <addColumn tableName="TrxKasBesar" >
            <column name="InputerName" type="varchar(200)"/>
            <column name="CheckerName" type="varchar(200)"/>
        </addColumn>
        <addColumn tableName="TrxHeadTeller" >
            <column name="InputerName" type="varchar(200)"/>
            <column name="TellerName" type="varchar(200)"/>
        </addColumn>
        <addColumn tableName="TrxCashOpname" >
            <column name="NameTeller" type="varchar(200)"/>
            <column name="NameBOM" type="varchar(200)"/>
            <column name="NameBOS" type="varchar(200)"/>
            <column name="NameBM" type="varchar(200)"/>
            <column name="NameNOM" type="varchar(200)"/>
            <column name="NameQA" type="varchar(200)"/>
            <column name="NameODH" type="varchar(200)"/>
            <column name="NameAltTeller" type="varchar(200)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>