<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1659655148739">
       <createTable tableName="TrxTellerExchange">
          <column name="Id" type="BIGINT" autoIncrement="true">
             <constraints primaryKey="true" primaryKeyName="PK_TrxTellerExchange_Id"/>
          </column>
          <column name="RequestId" type="varchar(40)"/>
          <column name="TransactionId" type="varchar(20)"/>
          <column name="BranchId" type="varchar(10)"/>
          <column name="Period" type="date"/>
          <column name="Type" type="varchar(20)"/>
          <column name="FromName" type="varchar(50)"/>
          <column name="ToName" type="varchar(50)"/>
          <column name="RequestFlag" type="boolean"/>
          <column name="DepositFlag" type="boolean"/>
          <column name="Status" type="varchar(15)"/>
          <column name="InputerNik" type="varchar(25)"/>
          <column name="VerificationNik" type="varchar(25)"/>
          <column name="TotalAmount" type="numeric(18,2)"/>
          <column name="TotalAmountSpelled" type="varchar(500)"/>
          <column name="CreateDatetime" type="datetime2" defaultValueComputed="current_timestamp"/>
          <column name="UpdateDateTime" type="datetime2" defaultValueComputed="current_timestamp"/>
          <column name="AmountDetail" type="varchar(max)"/>

       </createTable>
       <createIndex tableName="TrxTellerExchange" indexName="IDX_TrxTellerExchange_TransactionId">
          <column name="TransactionId"/>
       </createIndex>

       <createTable tableName="TrxTellerExchangeVault">
          <column name="Id" type="BIGINT" autoIncrement="true">
             <constraints primaryKey="true" primaryKeyName="PK_TrxTellerExchangeVault_Id"/>
          </column>
          <column name="RequestId" type="varchar(40)"/>
          <column name="TransactionId" type="varchar(20)"/>
          <column name="BranchId" type="varchar(10)"/>
          <column name="Period" type="date"/>
          <column name="Type" type="varchar(20)"/>
          <column name="FromName" type="varchar(50)"/>
          <column name="ToName" type="varchar(50)"/>
          <column name="RequestFlag" type="boolean"/>
          <column name="DepositFlag" type="boolean"/>
          <column name="Status" type="varchar(15)"/>
          <column name="InputerNik" type="varchar(25)"/>
          <column name="VerificationNik" type="varchar(25)"/>
          <column name="TotalAmount" type="numeric(18,2)"/>
          <column name="TotalAmountSpelled" type="varchar(500)"/>
          <column name="CreateDatetime" type="datetime2" defaultValueComputed="current_timestamp"/>
          <column name="UpdateDateTime" type="datetime2" defaultValueComputed="current_timestamp"/>
          <column name="AmountDetail" type="varchar(max)"/>

       </createTable>
       <createIndex tableName="TrxTellerExchangeVault" indexName="IDX_TrxTellerExchangeVault_TransactionId">
          <column name="TransactionId"/>
       </createIndex>
    </changeSet>
</databaseChangeLog>