<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1669942411198">
    <sql>
        delete from MsCabang;
    </sql>
        <loadData file="MsCabang_221214.csv"
                  relativeToChangelogFile="true"
                  tableName="MSCABANG">
            <column header="cabangId"
                    name="CabangId"/>
            <column header="cabangDesc"
                    name="CabangDesc"/>
            <column header="totalBalance"
                    name="TotalBalance"/>
            <column header="email"
                    name="Email"/>
        </loadData>
</changeSet>
</databaseChangeLog>
