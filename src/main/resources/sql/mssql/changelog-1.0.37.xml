<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1689220229553">
        <createTable tableName="MsOfficerNR">
            <column name="MsOfficerNRID" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsOfficerNR_Id"/>
            </column>
            <column name="OfficerSource" type="varchar(100)"/>
            <column name="OfficerID" type="bigint"/>
            <column name="OfficerCode" type="varchar(1100)"/>
            <column name="SrcSystem" type="varchar(100)"/>
            <column name="DTPopulate" type="datetime"/>
            <column name="SysPopulate" type="varchar(50)"/>
            <column name="MprosperaOfficerID" type="decimal(19,0)"/>
            <column name="MMSID" type="int"/>
            <column name="RoleID" type="int"/>
            <column name="MMSCode" type="varchar(1100)"/>
            <column name="FieldChecksum" type="varchar(100)"/>
            <column name="OfficerName" type="varchar(100)"/>
            <column name="NIK" type="varchar(200)"/>
            <column name="LoginName" type="varchar(200)"/>
            <column name="EmailName" type="varchar(255)"/>
            <column name="RoleName" type="varchar(255)"/>
            <column name="OfficerStatusCode" type="int"/>
            <column name="OfficerStatusDesc" type="varchar(100)"/>
            <column name="AmtApprovalLimit" type="decimal(22,4)"/>
            <column name="DTKafka" type="datetimeoffset"/>
            <column name="MMSName" type="varchar(255)"/>
            <column name="DTCreated" type="date"/>
            <column name="DepartmentCode" type="varchar(20)"/>
            <column name="FirstName" type="varchar(100)"/>
            <column name="LastName" type="varchar(100)"/>
            <column name="DTLastLogon" type="datetime"/>
            <column name="AccountEnabled" type="int"/>
        </createTable>
    </changeSet>
</databaseChangeLog>