<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="wawan" id="1658143346356">
    <sql>
        delete from MsCabang;
        delete from MsRoleMenu;
        delete from MsSystemParam;
        delete from MsSystemParamDetail;
    </sql>
    <loadData file="MsCabang.csv"
              relativeToChangelogFile="true"
              tableName="MSCABANG">
        <column header="Kode Cabang"
                name="CabangId"/>
        <column header="Nama Kantor"
                name="CabangDesc"/>
    </loadData>
    <loadData file="MsRoleMenu.csv"
              relativeToChangelogFile="true"
              tableName="MsRoleMenu">
        <column header="id"
                name="Id"/>
        <column header="roleId"
                name="RoleId"/>
        <column header="menuDesc"
                name="MenuDesc"/>
    </loadData>
    <loadData file="MsSystemParam.csv"
              relativeToChangelogFile="true"
              tableName="MsSystemParam">
        <column header="paramId"
                name="ParamId"/>
        <column header="paramDesc"
                name="ParamDesc"/>
    </loadData>
    <loadData file="MsSystemParamDetail.csv"
              relativeToChangelogFile="true"
              tableName="MsSystemParamDetail">
        <column header="paramId"
                name="ParamId"/>
        <column header="paramDetailId"
                name="ParamDetailId"/>
        <column header="paramDetailDesc"
                name="ParamDetailDesc"/>
    </loadData>
</changeSet>
</databaseChangeLog>
