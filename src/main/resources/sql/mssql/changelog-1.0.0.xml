<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="Wawan" id="1648035394348">
        <createTable tableName="MsSystemParam">
            <column name="ParamId" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_MsSystemParam_ParamId"/>
            </column>
            <column name="ParamDesc" type="varchar(max)"/>
        </createTable>
        <createTable tableName="MsSystemParamDetail">
            <column name="ParamId" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_MsSystemParamDetail_ParamId"/>
            </column>
            <column name="ParamDetailId" type="varchar(50)">
                <constraints primaryKey="true" primaryKeyName="PK_MsSystemParamDetail_ParamId"/>
            </column>
            <column name="ParamDetailDesc" type="varchar(max)"/>
        </createTable>
        <createTable tableName="MsCabang">
            <column name="CabangId" type="varchar(25)">
                <constraints primaryKey="true" primaryKeyName="PK_MsCabang_CabangId"/>
            </column>
            <column name="CabangDesc" type="varchar(max)"/>
        </createTable>



        <createTable tableName="TrxKasBesar">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxKasBesar_Id"/>
            </column>
            <column name="TransactionId" type="varchar(20)"/>
            <column name="BranchId" type="varchar(20)"/>
            <column name="Period" type="date"/>
            <column name="TrxType" type="varchar(20)"/>
            <column name="Status" type="varchar(15)" defaultValue="Pending"/>
            <column name="InputerNIK" type="varchar(25)"/>
            <column name="CheckerNIK" type="varchar(25)"/>
            <column name="Reason" type="varchar(max)"/>
            <column name="TotalAmount" type="numeric(18,2)"/>
            <column name="BalanceAmount" type="numeric(18,2)"/>
            <column name="AdditionalInfo" type="varchar(max)"/>
            <column name="CreateDatetime" type="datetime2" defaultValueComputed="current_timestamp"/>
            <column name="UpdateDateTime" type="datetime2" defaultValueComputed="current_timestamp"/>
        </createTable>
        <createIndex tableName="TrxKasBesar" indexName="IDX_TrxKasBesar_TransactionId">
            <column name="TransactionId"/>
        </createIndex>
        <createIndex tableName="TrxKasBesar" indexName="IDX_TrxKasBesar_BranchId_Period">
            <column name="Period"/>
            <column name="BranchId"/>
        </createIndex>
        <createIndex tableName="TrxKasBesar" indexName="IDX_TrxKasBesar_Period">
            <column name="Period"/>
        </createIndex>



        <createTable tableName="TrxAmountDetail">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxAmountDetail_Id"/>
            </column>
            <column name="TransactionId" type="varchar(20)"/>
            <column name="CreateDatetime" type="datetime2" defaultValueComputed="current_timestamp"/>
            <column name="C50Count" type="int"/>
            <column name="C50Amount" type="numeric(18,2)"/>
            <column name="C100Count" type="int"/>
            <column name="C100Amount" type="numeric(18,2)"/>
            <column name="C200Count" type="int"/>
            <column name="C200Amount" type="numeric(18,2)"/>
            <column name="C500Count" type="int"/>
            <column name="C500Amount" type="numeric(18,2)"/>
            <column name="C1KCount" type="int"/>
            <column name="C1KAmount" type="numeric(18,2)"/>
            <column name="P1KCount" type="int"/>
            <column name="P1KAmount" type="numeric(18,2)"/>
            <column name="P2KCount" type="int"/>
            <column name="P2KAmount" type="numeric(18,2)"/>
            <column name="P5KCount" type="int"/>
            <column name="P5KAmount" type="numeric(18,2)"/>
            <column name="P10KCount" type="int"/>
            <column name="P10KAmount" type="numeric(18,2)"/>
            <column name="P20KCount" type="int"/>
            <column name="P20KAmount" type="numeric(18,2)"/>
            <column name="P50KCount" type="int"/>
            <column name="P50KAmount" type="numeric(18,2)"/>
            <column name="P75KCount" type="int"/>
            <column name="P75KAmount" type="numeric(18,2)"/>
            <column name="P100KCount" type="int"/>
            <column name="P100KAmount" type="numeric(18,2)"/>
        </createTable>
        <createIndex tableName="TrxAmountDetail" indexName="IDX_TrxAmountDetail_TransactionId">
            <column name="TransactionId"/>
        </createIndex>



        <createTable tableName="MsBranchBalance">
            <column name="BranchId" type="varchar(10)">
                <constraints primaryKey="true" primaryKeyName="PK_MsBranchBalance_BranchId"/>
            </column>
            <column name="CreateDatetime" type="datetime2" defaultValueComputed="current_timestamp"/>
            <column name="UpdateDateTime" type="datetime2" defaultValueComputed="current_timestamp"/>
            <column name="C50Count" type="int"/>
            <column name="C50Amount" type="numeric(18,2)"/>
            <column name="C100Count" type="int"/>
            <column name="C100Amount" type="numeric(18,2)"/>
            <column name="C200Count" type="int"/>
            <column name="C200Amount" type="numeric(18,2)"/>
            <column name="C500Count" type="int"/>
            <column name="C500Amount" type="numeric(18,2)"/>
            <column name="C1KCount" type="int"/>
            <column name="C1KAmount" type="numeric(18,2)"/>
            <column name="P1KCount" type="int"/>
            <column name="P1KAmount" type="numeric(18,2)"/>
            <column name="P2KCount" type="int"/>
            <column name="P2KAmount" type="numeric(18,2)"/>
            <column name="P5KCount" type="int"/>
            <column name="P5KAmount" type="numeric(18,2)"/>
            <column name="P10KCount" type="int"/>
            <column name="P10KAmount" type="numeric(18,2)"/>
            <column name="P20KCount" type="int"/>
            <column name="P20KAmount" type="numeric(18,2)"/>
            <column name="P50KCount" type="int"/>
            <column name="P50KAmount" type="numeric(18,2)"/>
            <column name="P75KCount" type="int"/>
            <column name="P75KAmount" type="numeric(18,2)"/>
            <column name="P100KCount" type="int"/>
            <column name="P100KAmount" type="numeric(18,2)"/>
        </createTable>



        <createTable tableName="MsMMS">
            <column name="MMSID" type="int">
                <constraints primaryKey="true" primaryKeyName="PK__MsMMS__57559C4A605F4B98"/>
            </column>
            <column name="Address" type="varchar(400)"/>
            <column name="CostCenter" type="varchar(20)"/>
            <column name="DistrictDesc" type="varchar(100)"/>
            <column name="KFOCode" type="varchar(50)"/>
            <column name="KFOName" type="varchar(50)"/>
            <column name="MMSCode" type="varchar(10)"/>
            <column name="MMSName" type="varchar(200)"/>
            <column name="MMSStatusCode" type="varchar(300)"/>
            <column name="MMSStatusDesc" type="varchar(100)"/>
            <column name="ProvinceDesc" type="varchar(100)"/>
            <column name="RTRW" type="varchar(200)"/>
            <column name="StateDesc" type="varchar(100)"/>
            <column name="SubDistrictDesc" type="varchar(200)"/>
            <column name="ZipCode" type="varchar(20)"/>
        </createTable>



        <createTable tableName="TrxAudittrail">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxAudittrail_Id"/>
            </column>
            <column name="NIK" type="varchar(40)"/>
            <column name="Action" type="varchar(40)"/>
            <column name="CreateDatetime" type="datetime2" defaultValueComputed="current_timestamp"/>
            <column name="TransactionId" type="varchar(25)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>