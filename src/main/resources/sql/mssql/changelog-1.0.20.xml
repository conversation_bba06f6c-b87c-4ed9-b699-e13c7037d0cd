<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1667794527863">
        <createTable tableName="MsUserAdmin">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsUserAdmin_Id"/>
            </column>
            <column name="NIK" type="varchar(20)"/>
            <column name="Name" type="varchar(max)"/>
            <column name="Role" type="varchar(20)"/>
            <column name="ActiveFlag" type="varchar(20)"/>
            <column name="CreateDateTime" type="datetime"/>
            <column name="UpdateDateTime" type="datetime"/>
        </createTable>
        <createTable tableName="TrxOverLimitBranchBalance">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxOverLimitBalance_Id"/>
            </column>
            <column name="BranchId" type="varchar(20)"/>
            <column name="Period" type="date"/>
            <column name="TransactionId" type="varchar(20)"/>
            <column name="TotalBalance" type="numeric(18,2)"/>
            <column name="CreateDateTime" type="datetime"/>
        </createTable>
        <createTable tableName="MsAlternateRole">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_MsAlternateRole_Id"/>
            </column>
            <column name="NIK" type="varchar(20)"/>
            <column name="AlternateRoleId" type="varchar(20)"/>
            <column name="AlternateRoleName" type="varchar(max)"/>
            <column name="BranchId" type="varchar(20)"/>
            <column name="StartPeriod" type="date"/>
            <column name="EndPeriod" type="date"/>
            <column name="Info" type="varchar(max)"/>
        </createTable>
        <addColumn tableName="MsCabang" >
            <column name="TotalBalance" 
                    type="numeric(18,2)"/>
            <column name="Email"
                    type="varchar(max)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>