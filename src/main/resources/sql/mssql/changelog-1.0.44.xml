<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="Ade Fahri Syany" id="1751527123930">
        <sql>
            insert into MsRoleMenu  (Id,RoleId,MenuDesc) values(55,'SKAI','verifikasi'),(56,'SKAI','report'),(57,'SKAI','home')
        </sql>
        <addColumn tableName="TrxCashOpname" >
            <column name="NikSKAI" type="varchar(25)"/>
            <column name="NameSKAI"
                    type="varchar(200)"/>
            <column name="StatusVerificationSKAI"
                    type="varchar(15)"/>
            <column name="DateVerificationSKAI"
                    type="datetime"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
