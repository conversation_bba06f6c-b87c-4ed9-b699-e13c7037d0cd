<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="Abdillah" id="1653380228767">
        <createTable tableName="TrxHTAmountDetail">
            <column name="Id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" primaryKeyName="PK_TrxHTAmountDetail_Id"/>
            </column>
            <column name="TransactionId" type="varchar(20)"/>
            <column name="CreateDatetime" type="datetime2" defaultValueComputed="current_timestamp"/>
            <column name="C50Count" type="int"/>
            <column name="C50Amount" type="numeric(18,2)"/>
            <column name="C100Count" type="int"/>
            <column name="C100Amount" type="numeric(18,2)"/>
            <column name="C200Count" type="int"/>
            <column name="C200Amount" type="numeric(18,2)"/>
            <column name="C500Count" type="int"/>
            <column name="C500Amount" type="numeric(18,2)"/>
            <column name="C1KCount" type="int"/>
            <column name="C1KAmount" type="numeric(18,2)"/>
            <column name="P1KCount" type="int"/>
            <column name="P1KAmount" type="numeric(18,2)"/>
            <column name="P2KCount" type="int"/>
            <column name="P2KAmount" type="numeric(18,2)"/>
            <column name="P5KCount" type="int"/>
            <column name="P5KAmount" type="numeric(18,2)"/>
            <column name="P10KCount" type="int"/>
            <column name="P10KAmount" type="numeric(18,2)"/>
            <column name="P20KCount" type="int"/>
            <column name="P20KAmount" type="numeric(18,2)"/>
            <column name="P50KCount" type="int"/>
            <column name="P50KAmount" type="numeric(18,2)"/>
            <column name="P75KCount" type="int"/>
            <column name="P75KAmount" type="numeric(18,2)"/>
            <column name="P100KCount" type="int"/>
            <column name="P100KAmount" type="numeric(18,2)"/>
        </createTable>
        <createIndex tableName="TrxHTAmountDetail" indexName="IDX_TrxHTAmountDetail_TransactionId">
            <column name="TransactionId"/>
        </createIndex>

        <addColumn tableName="TrxHeadTeller" >
            <column name="refId"
                    type="varchar(20)"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>