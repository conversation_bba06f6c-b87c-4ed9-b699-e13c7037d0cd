<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="abdillah" id="1665469974693">
       <createTable tableName="MsEmployee">
          <column name="NIK" type="varchar(25)">
             <constraints primaryKey="true" primaryKeyName="PK__MsEmployee__ID"/>
          </column>
          <column name="IdentityNum" type="varchar(25)"/>
          <column name="FullName" type="varchar(200)"/>
          <column name="Occupation" type="varchar(100)"/>
          <column name="OccupationDesc" type="varchar(100)"/>
          <column name="StatusEmployeeDesc" type="varchar(100)"/>
          <column name="BranchCode" type="varchar(10)"/>
          <column name="CostCenterCode" type="varchar(10)"/>
          <column name="DirectSupervisorNIK" type="varchar(15)"/>
          <column name="DirectSupervisorName" type="varchar(15)"/>
          <column name="FieldChecksum" type="varchar(100)"/>
          <column name="SrcSystem" type="varchar(50)"/>
          <column name="DTPopulate" type="datetime"/>
          <column name="SysPopulate" type="varchar(50)"/>
       </createTable>
       <createTable tableName="TrxCashOpname">
          <column name="Id" type="BIGINT" autoIncrement="true">
             <constraints primaryKey="true" primaryKeyName="PK_TrxCashOpname_Id"/>
          </column>
          <column name="RequestId" type="varchar(40)"/>
          <column name="TransactionId" type="varchar(20)"/>
          <column name="BranchId" type="varchar(10)"/>
          <column name="Period" type="date"/>
          <column name="Status" type="varchar(15)"/>
          <column name="TotalBalance" type="numeric(18,2)"/>
          <column name="OldTotalBalance" type="numeric(18,2)"/>
          <column name="CarryBalance" type="numeric(18,2)"/>
          <column name="TotalPaperBalance" type="numeric(18,2)"/>
          <column name="TotalCoinBalance" type="numeric(18,2)"/>
          <column name="NikTeller" type="varchar(25)"/>
          <column name="NikBOS" type="varchar(25)"/>
          <column name="NikBM" type="varchar(25)"/>
          <column name="NikBOM" type="varchar(25)"/>
          <column name="NikQA" type="varchar(25)"/>
          <column name="Reason" type="varchar(max)"/>
          <column name="CreateDatetime" type="datetime2" defaultValueComputed="current_timestamp"/>
          <column name="UpdateDateTime" type="datetime2" defaultValueComputed="current_timestamp"/>
          <column name="AmountDetail" type="varchar(max)"/>
       </createTable>
    </changeSet>
</databaseChangeLog>