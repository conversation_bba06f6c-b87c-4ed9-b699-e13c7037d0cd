# Logging
logging.level=INFO
logging.level.org.hibernate=WARN
logging.level.org.springframework=INFO

# Spring
spring.main.banner-mode=off

management.server.port=8080
management.endpoints.web.exposure.include=health,metrics
management.endpoint.health.show-details=always

server.compression.enabled=true
server.compression.mime-types=application/json

### Datasource
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.maximum-pool-size=100

spring.datasource.url=${DB_URL:***********************************************************}
spring.datasource.username=${DB_USER:cmaw.kckfo.dbo}
spring.datasource.password=${DB_PASS:P@ssw0rd}
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

stage=${STAGE:local}
channel.id=${CHANNEL_ID:6022}
node=${X_NODE:BTPNS}
terminal.id=${TERMINAL_ID:nusa-penida.cma-svc}
#mail.uri=${MAIL_URL:https://mail-int-beta.apps.nww.syariahbtpn.com/}
mail.uri=${MAIL_URL:https://api-beta-v2.apps.btpnsyariah.com/int/notification/2.0.0/}
#mail.uri=https://deelay.me/40000/https://api-dirty-v2.apps.nww.syariahbtpn.com/int/notification/2.0.0/
mail.api.version=${MAIL_API_VERSION:1.3.0}
mail.sender=${MAIL_SENDER:<EMAIL>}
mail.target=${MAIL_TARGET:<EMAIL>}
mail.cc=${MAIL_CC:<EMAIL>}
mail.bcc=${MAIL_BCC: }
mail.enable=${MAIL_ENABLE:true}
mail.cc.enable=${MAIL_CC_ENABLE:false}
terminal.name=${TERMINAL_NAME:cmasvc}
acq.id=${ACQ_ID:547}
orgunit.id=${ORGUNIT_ID:547}
header.api_key=${HEADER_API_KEY:2d0be0fa94e4576d8e703b9fd879bc81}

cma.uri=${CMA_URI:https://cma-web-fin-dirty.apps.nww.syariahbtpn.com/}

officer.database=${OFFICER_DATABASE:officerNR}

#LDAP
ldap.uri=${LDAP_URL:https://accounts-nww.syariahbtpn.com/auth/realms/dirty/protocol/openid-connect/}
ldap.enable=${LDAP_ENABLE:true}
client.id=${CLIENT_ID:cma-web-svc}
client.secret=${CLIENT_SECRET:WToWyWrFNNAsSYt8wRzN24xqKbGp2QrN}
ldap.scope=${LDAP_SCOPE:openid}
grant.type=${GRANT_TYPE:password}

# liquibase
spring.liquibase.change-log=${LIQUIBASE_CHANGE_LOG:classpath:sql/changelog-master.xml}
spring.liquibase.enabled=${LIQUIBASE_ENABLED:false}

# Web Server
btpns-platform.http-server.default.io-threads=${SERVER_IO_THREADS:2}
btpns-platform.http-server.default.worker-threads=${SERVER_WORKER_THREADS:0}