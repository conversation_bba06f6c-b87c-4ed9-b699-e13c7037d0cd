package com.btpns.fin.constant;

public enum TrxType {
    KHT("KHT","Keluar Head Teller"),
    MHT("MHT","Masuk Head Teller"),
    SA("SA","Saldo Awal"),
    SAHT2T("SAHT2T","Saldo HT2T"),
    SAT2HT("SAT2HT","Saldo T2HT"),
    SWHT("SWHT","Saldo Awal Head Teller"),
    SKHT("SKHT","Saldo Akhir Head Teller"),
    APR("APR","Approval"),
    HT2T("HT2T","HT To Teller"),
    T2HT("T2HT","Teller to HT"),
    V2HT("V2HT","Vault to HT"),
    HT2V("HT2V","HT To Vault"),
    Cancel("C","Cancel"),
    SAW("SAW", "Saldo Awal Hari"),
    RVKHT("RV-KHT", "Reversal Keluar Head Teller"),
    RVMHT("RV-MHT", "Reversal Masuk Head Teller"),
    RVSAW("RV-SAW", "Reversal Saldo Awal"),
    RVSAK("RV-SAK", "Reversal Saldo Akhir"),
    RVCC("RV-CC", "Reversal Cash Count"),

    RV("RV", "Reversal"),
    RV_DASH("RV-", "RV Dash"),
    RVV2HT("RV-V2HT", "Reversal Vault to HT"),
    RVHT2V("RV-HT2V","Reversal HT To Vault"),
    SCHT("SCHT","Saldo Cancel Head Teller"),
    SAK("SAK","Saldo Akhir"),
    CC("CC","Cash Count"),
    TEHT2T("TEHT2T","Teller Exchange HT2T"),
    TEV2HT("TEV2HT","Teller Exchange Vault to HT"),
    TET2HT("TET2HT","Teller Exchange T2HT"),
    TEHT2V("TEHT2V","Teller Exchange HT to Vault"),
    CASHOPNAME("COP","Cash Opname"),


            ;

    private String code;
    private String value;

    TrxType(String code,String value){
        this.code = code;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }
}
