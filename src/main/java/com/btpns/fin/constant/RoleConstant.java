package com.btpns.fin.constant;

public enum RoleConstant {
    BM("BM","BRANCH MANAGER"),
    QA("QA","QUALITY ASSURANCE ASSESSMENT OFFICER"),
    BOS("BOS","BRANCH OPERATION SUPERVISOR"),
    VIEWER("<PERSON><PERSON><PERSON><PERSON>","Viewer"),
    ADMIN("ADMIN","Admin"),
    AREAFUND("AREAFUND","AREA FUNDING BUSINESS HEAD"),
    NOM("NOM","NETWORK OPERATION MANAGER"),
    ODH("ODH","OPERATION DISTRIBUTION HEAD"),
    QAD("QAD","QUALITY ASSURANCE DEVELOPMENT MANAGER"),
    QAM("QAM","QUALITY ASSURANCE ASSESSMENT MANAGER"),
    SKAI("SKAI","AUDITOR");

    private String roleId;
    private String value;

    RoleConstant(String roleId, String value){
        this.roleId = roleId;
        this.value = value;
    }

    public String getRoleId() {
        return roleId;
    }

    public String getValue() {
        return value;
    }
    public static RoleConstant getByRoleId(String roleId) {
        for(RoleConstant e : values()) {
            if(e.roleId.equals(roleId)) return e;
        }
        return null;
    }

    public static RoleConstant getRoleByValue(String value) {
        for(RoleConstant e : values()) {
            if(e.value.equals(value)) return e;
        }
        return null;
    }
    public static String getValueByRoleId(String roleId) {
        for(RoleConstant e : values()) {
            if(e.roleId.equals(roleId)) return e.value;
        }
        return null;
    }
    public static String getRoleIdByValue(String value) {
        for(RoleConstant e : values()) {
            if(e.value.equals(value)) return e.roleId;
        }
        return null;
    }
}
