package com.btpns.fin.constant;

public enum  Action {
    INPUT_KHT("input_kht"),
    INPUT_MHT("input_mht"),
    APPROVE_KHT("approve_kht"),
    REJECT_KHT("reject_kht"),
    APPROVE_RVKHT("approve_rvkht"),
    APPROVE_RVMHT("approve_rvmht"),
    APPROVE_RVSAW("approve_rvsaw"),
    APPROVE_RVSAK("approve_rvsak"),
    APPROVE_RVCC("approve_rvcc"),
    REJECT_RVKHT("reject_rvkht"),
    REJECT_RVMHT("reject_rvmht"),
    REJECT_RVSAW("reject_rvsaw"),
    REJECT_RVSAK("reject_rvsak"),
    REJECT_RVCC("reject_rvcc"),
    INPUT_SALDO_AWAL("input_saldo_awal"),

    APPROVE_MHT("approve_mht"),
    REJECT_MHT("reject_mht"),
    
    RELEASE_HT2T("release_ht2t"),
    RELEASE_T2HT("release_t2ht"),
    SUBMIT_HT2V("submit_ht2v"),
    CANCEL_HT2V("cancel_ht2v"),
    REVERSAL_HT("reversal_ht"),
    REVERSAL_KHT("reversal_kht"),
    REVERSAL_MHT("reversal_mht"),
    REVERSAL_CC("reversal_cc"),
    REVERSAL_SAW("reversal_saw"),
    REVERSAL_SAK("reversal_sak"),
    SUBMIT_CASH_COUNT("submit_cash_count"),
    APPROVE_CASH_COUNT("approve_cash_count"),
    REJECT_CASH_COUNT("reject_cash_count"),
    SUBMIT_TELLER_EXCHANGE("submit_teller_exchange"),
    APPROVE_TELLER_EXCHANGE("approve_teller_exchange"),
    REJECT_TELLER_EXCHANGE("reject_teller_exchange"),
    CANCEL_TELLER_EXCHANGE("cancel_teller_exchange"),
    SUBMIT_CASH_OPNAME("submit_cash_opname"),
    APPROVE_CASH_OPNAME("approve_cash_opname"),
    REJECT_CASH_OPNAME("reject_cash_opname"),
    USER_LOGIN("user_login")

    ;

    private String value;

    Action(String value){
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
