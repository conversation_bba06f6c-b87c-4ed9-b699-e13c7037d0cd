package com.btpns.fin.constant;

public enum ResponseStatus {
    
    SUCCESS("Success","00"),
    ALREADY_SUCCESS("Already Success","00"),
    GENERAL_ERROR("General Error","01"),
    PENDING("Pending verification","03"),
    FAILED("Failed","01"),
    AKHIRHARI_BALANCE_HT_NOT_ZERO("Balance HT > 0 ","01"),
    AKHIRHARI_HT2V_PENDING("HT2V Pending ","02"),
    AKHIRHARI_MHT_PENDING("MHT Pending ","03"),
    VALIDATE_BALANCE_HT_NOT_ZERO(" balance HT yesterday > 0 ","01"),
    VALIDATE_BALANCE_HT_ALLOW(" allow CC or KHT ","00"),
    ALLOW_TELLER_EXCHANGE(" allow Teller Exchange ","00"),
    FOUND_TELLER_EXCHANGE_PENDING(" found Pending Transaction ","01"),
    FOUND_TELLER_EXCHANGE_REJECTED(" found Reject Transaction ","02"),
    KOREKSI_MUST_MHT_FIRST("must reversal MHT first","02"),
    SUCCESS_WITH_OVERLIMIT("Success with overlimit","04"),
    COMPARE_HEAD_TELLER_BALANCE_MINUS("Compare Head Teller Balance Minus","05"),
    DATA_NOT_FOUND("DATA NOT FOUND","404"),
    KOREKSI_HEAD_TELLER_FIRST("must correction head teller first","C1"),
    KOREKSI_FOUND_MHT_PENDING("found MHT pending","C2"),
    KOREKSI_HT2V_PENDING("found HT2V Pending","C3"),
    HEAD_TELLER_SUBMIT_BALANCE_INSUFFICIENT ("head teller Balance insufficient","H1"),
    HEAD_TELLER_SUBMIT_PENDING_HT2V ("found HT2V Pending transaction","H2"),
    FAILED_AUTH("Failed Auth","02"),
    ROLE_IS_NOT_BOS("Failed Submit, Role must BOS","A1"),
    TEV2HT_ALREADY_SUBMITTED("Already Submit TEV2HT","A2")

            ;

    private String value;
    
    private String code;

    ResponseStatus(String value, String code){
        this.value = value;
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

}
