package com.btpns.fin.constant;

import java.util.HashSet;
import java.util.Set;

public class CommonConstant {
    public static final String COIN_50 = "c50";
    public static final String COIN_100 = "c100";
    public static final String COIN_200 = "c200";
    public static final String COIN_500 = "c500";
    public static final String COIN_1K = "c1.000";
    public static final String PAPER_1K = "p1.000";
    public static final String PAPER_2K = "p2.000";
    public static final String PAPER_5K = "p5.000";
    public static final String PAPER_10K = "p10.000";
    public static final String PAPER_20K = "p20.000";
    public static final String PAPER_50K = "p50.000";
    public static final String PAPER_75K = "p75.000";
    public static final String PAPER_100K = "p100.000";

    public static final String TYPE_VERIFY = "verify";
    public static final String TYPE_PENDING_TRANSACTION = "pending";

    public static final String RELEASE_HT = "release";

    public static final String ADD_HT = "add";

    public static final String KAS_BESAR = "kasBesar";

    public static final String HEAD_TELLER = "headTeller";

    public static final String HT2V = "ht2v";
    public static final String AWAL_HARI = "awalhari";
    public static final String AKHIR_HARI = "akhirhari";

    public static final String BALANCE_OVERRIDE = "BALANCE_OVERRIDE";
    public static final Integer TELLER_ROLE = 27;
    public static final Integer BOM_ROLE = 39;
    public static final Integer BO_ROLE = 38;
    public static final Integer HO_ROLE = 19;
    public static final Integer PAYMENT_INPUTER = 31;
    public static final Integer PAYMENT_OTORISATOR = 32;
    public static final String BM_ROLE = "BM";
    public static final String BOS_ROLE = "BOS";
    public static final String QA_ROLE = "QA";
    public static final String QAD_ROLE = "QAD";
    public static final String QAM_ROLE = "QAM";
    public static final String NOM_ROLE = "NOM";
    public static final String ODH_ROLE = "ODH";
    public static final String SKAI_ROLE = "SKAI";
    public static final String ALT_TELLER_ROLE = "altTeller";
    public static final String ADMIN_ROLE = "admin";
    public static final String MHT_PREVIOUS = "previous";
    public static final String TELLER_EXCHANGE = "tellerExchange";
    public static final String TELLER_EXCHANGE_VAULT = "tellerExchangeVault";
    public static final String TO_NAME_KHASANAH = "Khasanah";
    public static final String FROM_NAME_HEAD_TELLER = "Head Teller";
    public static final String TELLER_EXCHANGE_SALDO_VAULT = "Saldo Vault";
    public static final String PENDING_HEAD_TELLER = "pendingheadTeller";
    public static final String GET_CASH_OPNAME = "get_cash_opname";
    public static final String GET_CASH_OPNAME_LIST_BA = "get_cash_opname_list_ba";
    public static final String CHECK_MONTHLY_CASH_OPNAME = "check_monthly_cash_opname";
    public static final String FOUND_MONTHLY_TRANSACTION = "found";
    public static final String NOT_FOUND_MONTHLY_TRANSACTION = "not_found";
    public static final String OVERLIMIT_STATUS_BALANCE_OVERLIMIT = "overlimit";
    public static final String OVERLIMIT_STATUS_BALANCE_UNDERLIMIT = "underlimit";
    public static final String GET_BRANCH_BALANCE_STATUS = "get_branch_balance_status";
    public final static String TYPE_MESSAGING_SMS = "0001";
    public final static String TYPE_MESSAGING_EMAIL = "0002";
    public final static String TYPE_MESSAGING_MBANK = "0003";
    public static final String OVERLIMIT_STATUS_BALANCE_OVERLIMIT_DESC = "balance overlimit ";
    public static final String OVERLIMIT_STATUS_BALANCE_UNDERLIMIT_DESC = "balance underlimit";
    public static final String STATUS_BALANCE_OK = "ok";
    public static final String STATUS_BALANCE_OD = "balanceOD";
    public static final String STATUS_BALANCE_OD_DESC = "have balance at end of day";
    public final static String GET_BRANCH_TOTAL_HT_BALANCE = "get_branch_total_ht_balance";
    public static final String GET_KOREKSI_DETAIL = "get_koreksi_detail";
    public static final String TE_SOURCE_OF_FUND_HT_BALANCE = "HT_BALANCE";
    public static final String TE_SOURCE_OF_FUND_BRANCH_BALANCE = "BRANCH_BALANCE";
    public final static String GET_BRANCH_OFFICERS = "get_branch_officers";
    public static final String GET_PENDING_CASH_OPNAME = "get_pending_cash_opname";
    public static final String REQUEST_TYPE_NEW = "new";
    public static final String REQUEST_TYPE_UPDATE = "update";
    public static final String REQUEST_TYPE_DELETE = "delete";
    public static final String GET_LIST_KOREKSI_KAS_BESAR = "get_list_koreksi_kas_besar";
    public static final String SUBMIT_MANAGEMENT_ALTERNATE = "submit_management_alternate";
    public static final String SUBMIT_MANAGEMENT_ALTERNATE_NEW = "submit_alternate_new";
    public static final String SUBMIT_MANAGEMENT_ALTERNATE_UPDATE = "submit_alternate_update";
    public static final String SUBMIT_MANAGEMENT_ALTERNATE_DELETE = "submit_alternate_delete";
    public static final String SUBMIT_MANAGEMENT_NONPROS_NEW = "submit_nonProspera_new";
    public static final String SUBMIT_MANAGEMENT_NONPROS_UPDATE = "submit_nonProspera_update";
    public static final String SUBMIT_MANAGEMENT_NONPROS_DELETE = "submit_nonProspera_delete";
    public static final String SUBMIT_MANAGEMENT_OFFICER = "submit_management_officer";
    public static final String GET_LIST_LIMIT_INSURANCE = "get_list_limit_insurance";
    public static final String SUBMIT_LIMIT_INSURANCE = "submit_limit_insurance";
    public static final String SUBMIT_NEW_BRANCH = "submit_new_branch";
    public static final String GET_LIST_OFFICER_NON_PROSPERA = "get_list_officer_non_prospera";
    public static final String GET_LIST_BRANCH = "get_list_branch";
    public static final String GET_LIST_ALTERNATE_ROLE = "get_list_alternate_role";
    public static final String GET_INDICATOR_PROGRESS = "get_indicator_progress";
    public final static String GET_PENDING_HEAD_TELLER_TRANSACTION = "get_pending_head_teller_transaction";
    public final static String GET_PENDING_CASH_COUNT = "get_pending_cash_count";
    public final static String GET_BRANCH_OFFICER = "get_branch_officer";
    public final static String GET_OFFICER = "get_officer";
    public final static String GET_PENDING_APPROVAL = "get_pending_approval";
    public final static String GET_PENDING_KHT = "get_pending_kht";
    public final static String GET_PENDING_MHT = "get_pending_mht";
    public final static String GET_LIST_TRANSACTION_KAS_BESAR = "get_list_transaction_kas_besar";
    public final static String GET_DETAIL_KHT = "get_detail_kht";
    public final static String GET_DETAIL_MHT = "get_detail_mht";
    public final static String GET_SALDO_AWAL = "get_saldo_awal";
    public final static String GET_HT2V_PENDING = "get_ht2v_pending";
    public final static String GET_LAST_BRANCH_BALANCE = "get_last_branch_balance";
    public final static String GET_HEAD_TELLER_BALANCE = "get_head_teller_balance";
    public final static String GET_PENDING_BALANCE = "get_pending_balance";
    public final static String GET_LIST_TELLER_EXCHANGE_BALANCE_PENDING = "get_list_teller_exchange_balance_pending";
    public final static String GET_TELLER_EXCHANGE_PENDING_KHT = "get_teller_exchange_pending_kht";
    public final static String GET_TELLER_EXCHANGE_PENDING_VAULT = "get_teller_exchange_pending_vault";
    public final static String SUBMIT_CASH_OPNAME = "submit_cash_opname";
    public final static String SUBMIT_APPROVAL = "submit_approval";
    public final static String SUBMIT_HEADTELLER_OUT = "submit_headteller_out";
    public final static String SUBMIT_HEADTELLER_IN = "submit_headteller_in";
    public final static String INPUT_BEGIN_BALANCE = "input_begin_balance";
    public final static String ADD_PENDING_HT_TRANSACTION = "add_pending_ht_transaction";
    public final static String CANCEL_HT2V = "cancel_ht2v";
    public final static String DELETE_PENDING_TRANSACTION = "delete_pending_transaction";
    public final static String SUBMIT_HT2V = "submit_ht2v";
    public final static String SUBMIT_PENDING_TRANSACTION = "submit_pending_transaction";
    public final static String KOREKSI_HEAD_TELLER = "koreksi_head_teller";
    public final static String KOREKSI_KAS_BESAR = "koreksi_kas_besar";
    public final static String APPROVAL_TELLER_EXCHANGE = "approval_teller_exchange";
    public final static String CANCEL_TELLER_EXCHANGE = "cancel_teller_exchange";
    public final static String SUBMIT_TELLER_EXCHANGE = "submit_teller_exchange";
    public final static String GET_HEAD_TELLER_BALANCE_VALIDATION = "get_head_teller_balance_validation";
    public final static String GET_KOREKSI_HEAD_TELLER = "get_koreksi_head_teller";
    public final static String GET_TELLER_EXCHANGE_ACTIVE_TRANSACTION = "get_teller_exchange_active_transaction";
    public final static String GET_TELLER_EXCHANGE_LIST_TRANSACTION = "get_teller_exchange_list_transaction";
    public final static String GET_TELLER_EXCHANGE_DETAIL_TRANSACTION = "get_teller_exchange_detail_transaction";
    public final static String USER_LOGIN = "user_login";
    public static final String INDICATOR_PROGRESS_START = "START";
    public static final String INDICATOR_PROGRESS_KHT = "KHT";
    public static final String INDICATOR_PROGRESS_MHT = "MHT";
    public static final String INDICATOR_PROGRESS_END = "END";
    public static final String INDICATOR_PROGRESS_DONE = "DONE";
    public static final String INDICATOR_PROGRESS_V2HT = "V2HT";
    public static final String INDICATOR_PROGRESS_HT2T = "HT2T";
    public static final String INDICATOR_PROGRESS_T2HT = "T2HT";
    public static final String INDICATOR_PROGRESS_HT2V = "HT2V";
    public static final String REQUEST_TYPE_ALTERNATE_OFFICER = "alternate";
    public static final String REQUEST_TYPE_NONPROSPERA_OFFICER = "nonProspera";
    public static final String CMA_CONFIG_ONLINE = "online";
    public static final String CMA_CONFIG_MAINTENANCE = "maintenance";
    public static final String SUBMIT_MODE_MAINTENANCE = "submit_mode_maintenance";
    public static final String LIMIT_ALL = "ALL";
    public static final String EAST = "east";
    public static final String SOUTH = "south";
    public static final String WEST = "west";
    public static final String NORTH = "north";
    public static final String ACTION_ADMIN = "(Admin)";

    public static final Set<String> ENV_PROD = new HashSet<>(){{
        add(EAST);
        add(SOUTH);
        add(WEST);
        add(NORTH);
    }};
    public static final String LOG_LEVEL_BODY = "BODY";
    public static final String LOG_LEVEL_HEADERS = "HEADERS";
    public static final String LOG_LEVEL_NONE = "NONE";
    public static final String DB_OFFICER = "officer";
    public static final String DB_OFFICER_NR = "officerNR";
}
