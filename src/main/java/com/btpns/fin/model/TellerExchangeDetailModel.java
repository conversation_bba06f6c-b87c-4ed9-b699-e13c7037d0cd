package com.btpns.fin.model;

import com.btpns.fin.constant.TrxStatus;
import com.btpns.fin.model.entity.TrxTellerExchange;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class TellerExchangeDetailModel {
    private String requestId;
    private String transactionId;
    private String branchId;
    private String branchName;
    private String period;
    private String type;
    private String fromName;
    private String toName;
    private boolean requestFlag;
    private boolean depositFlag;
    private String status;
    private String inputerNIK;
    private String inputerName;
    private String inputerVerified = "";

    private String verificationNIK;
    private String verificationName;
    private String verificationVerified = "";

    private Double totalAmount;
    private String totalAmountSpelled;
    private String sourceOfFund;
    private List<AmountDetail> details;

    public TellerExchangeDetailModel(String requestId, String transactionId, String branchId, String branchName, LocalDate period, String type, String fromName, String toName, boolean requestFlag, boolean depositFlag, String status, String inputerNIK, String verificationNIK, Double totalAmount, String totalAmountSpelled, String sourceOfFund,String details) {
        Gson gson = new Gson();
        this.requestId = requestId;
        this.transactionId = transactionId;
        this.branchId = branchId;
        this.branchName = branchName;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddMMyyyy");
        this.period = formatter.format(period);
        this.type = type;
        this.fromName = fromName;
        this.toName = toName;
        this.requestFlag = requestFlag;
        this.depositFlag = depositFlag;
        this.status = status;
        this.inputerNIK = inputerNIK;
        this.verificationNIK = verificationNIK;
        this.inputerVerified = TrxStatus.VERIFIED.getValue();
        if (status.equals(TrxStatus.APPROVED.getValue())){
            this.verificationVerified = TrxStatus.VERIFIED.getValue();
        }else if (status.equals(TrxStatus.REJECTED.getValue())){
            this.verificationVerified = TrxStatus.REJECTED.getValue();
        }
        this.totalAmount = totalAmount;
        this.totalAmountSpelled = totalAmountSpelled;
        this.sourceOfFund = sourceOfFund;
        this.details = gson.fromJson(details, new TypeToken<List<AmountDetail>>() {
        }.getType());
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getToName() {
        return toName;
    }

    public void setToName(String toName) {
        this.toName = toName;
    }

    public boolean isRequestFlag() {
        return requestFlag;
    }

    public void setRequestFlag(boolean requestFlag) {
        this.requestFlag = requestFlag;
    }

    public boolean isDepositFlag() {
        return depositFlag;
    }

    public void setDepositFlag(boolean depositFlag) {
        this.depositFlag = depositFlag;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getInputerNIK() {
        return inputerNIK;
    }

    public void setInputerNIK(String inputerNIK) {
        this.inputerNIK = inputerNIK;
    }

    public String getInputerName() {
        return inputerName;
    }

    public void setInputerName(String inputerName) {
        this.inputerName = inputerName;
    }

    public String getVerificationNIK() {
        return verificationNIK;
    }

    public void setVerificationNIK(String verificationNIK) {
        this.verificationNIK = verificationNIK;
    }

    public String getVerificationName() {
        return verificationName;
    }

    public void setVerificationName(String verificationName) {
        this.verificationName = verificationName;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTotalAmountSpelled() {
        return totalAmountSpelled;
    }

    public void setTotalAmountSpelled(String totalAmountSpelled) {
        this.totalAmountSpelled = totalAmountSpelled;
    }

    public List<AmountDetail> getDetails() {
        return details;
    }

    public void setDetails(List<AmountDetail> details) {
        this.details = details;
    }

    public String getInputerVerified() {
        return inputerVerified;
    }

    public void setInputerVerified(String inputerVerified) {
        this.inputerVerified = inputerVerified;
    }

    public String getVerificationVerified() {
        return verificationVerified;
    }

    public void setVerificationVerified(String verificationVerified) {
        this.verificationVerified = verificationVerified;
    }
    public String getSourceOfFund() {
        return sourceOfFund;
    }

    public void setSourceOfFund(String sourceOfFund) {
        this.sourceOfFund = sourceOfFund;
    }
}
