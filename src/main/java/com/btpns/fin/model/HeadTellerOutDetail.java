package com.btpns.fin.model;

import java.util.List;

public class HeadTellerOutDetail {
    private String transactionId;
    private String period;
    private String branch;
    private String nikVerification;
    private String nameVerification;
    private String status;
    private String reason;
    private Double totalBeginBalance;
    private Double totalOutBalance;
    private Double totalRemainingBalance;
    private List<HeadTellerAmountDetail> amountDetails;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getNikVerification() {
        return nikVerification;
    }

    public void setNikVerification(String nikVerification) {
        this.nikVerification = nikVerification;
    }

    public String getNameVerification() {
        return nameVerification;
    }

    public void setNameVerification(String nameVerification) {
        this.nameVerification = nameVerification;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Double getTotalBeginBalance() {
        return totalBeginBalance;
    }

    public void setTotalBeginBalance(Double totalBeginBalance) {
        this.totalBeginBalance = totalBeginBalance;
    }

    public Double getTotalOutBalance() {
        return totalOutBalance;
    }

    public void setTotalOutBalance(Double totalOutBalance) {
        this.totalOutBalance = totalOutBalance;
    }

    public Double getTotalRemainingBalance() {
        return totalRemainingBalance;
    }

    public void setTotalRemainingBalance(Double totalRemainingBalance) {
        this.totalRemainingBalance = totalRemainingBalance;
    }

    public List<HeadTellerAmountDetail> getAmountDetails() {
        return amountDetails;
    }

    public void setAmountDetails(List<HeadTellerAmountDetail> amountDetails) {
        this.amountDetails = amountDetails;
    }
}
