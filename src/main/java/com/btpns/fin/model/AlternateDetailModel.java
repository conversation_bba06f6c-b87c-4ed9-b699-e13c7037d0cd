package com.btpns.fin.model;

import java.time.LocalDate;

public class AlternateDetailModel {
    private String id;
    private String nik;
    private String name;
    private String alternateRoleId;
    private String alternateRoleName;
    private String branchId;
    private String branchName;
    private String startPeriod;
    private String endPeriod;
    private Integer activeStatus;

    public AlternateDetailModel(Long id, String nik, String name, String alternateRoleId, String alternateRoleName, String branchId, String branchName,LocalDate startPeriod, LocalDate endPeriod) {
        this.id = id.toString();
        this.nik = nik;
        this.name = name;
        this.alternateRoleId = alternateRoleId;
        this.alternateRoleName = alternateRoleName;
        this.branchId = branchId;
        this.branchName = branchName;
        this.startPeriod = startPeriod.toString();
        this.endPeriod = endPeriod.toString();
        LocalDate period = LocalDate.now();
        int compareStartPeriod = period.compareTo(startPeriod);
        int compareEndPeriod = period.compareTo(startPeriod);
        if (compareStartPeriod <= 0 && compareEndPeriod >= 0){
            this.activeStatus = 1;
        }else {
            this.activeStatus = 0;
        }

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlternateRoleId() {
        return alternateRoleId;
    }

    public void setAlternateRoleId(String alternateRoleId) {
        this.alternateRoleId = alternateRoleId;
    }

    public String getAlternateRoleName() {
        return alternateRoleName;
    }

    public void setAlternateRoleName(String alternateRoleName) {
        this.alternateRoleName = alternateRoleName;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(String startPeriod) {
        this.startPeriod = startPeriod;
    }

    public String getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(String endPeriod) {
        this.endPeriod = endPeriod;
    }

    public Integer getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Integer activeStatus) {
        this.activeStatus = activeStatus;
    }
}
