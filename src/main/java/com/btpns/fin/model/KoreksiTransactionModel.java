package com.btpns.fin.model;

import java.util.List;

public class KoreksiTransactionModel {
    
    private String period;
    
    private String branchId;
    
    private String branchName;
    
    private String type;
    
    private List<KoreksiTransactionDetailModel> details;

    public KoreksiTransactionModel(String period, String branchId, String branchName, String type, List<KoreksiTransactionDetailModel> details) {
        this.period = period;
        this.branchId = branchId;
        this.branchName = branchName;
        this.type = type;
        this.details = details;
    }

    public String getPeriod() {
        return period;
    }

    public KoreksiTransactionModel setPeriod(String period) {
        this.period = period;
        return this;
    }

    public String getBranchId() {
        return branchId;
    }

    public KoreksiTransactionModel setBranchId(String branchId) {
        this.branchId = branchId;
        return this;
    }

    public String getBranchName() {
        return branchName;
    }

    public KoreksiTransactionModel setBranchName(String branchName) {
        this.branchName = branchName;
        return this;
    }

    public String getType() {
        return type;
    }

    public KoreksiTransactionModel setType(String type) {
        this.type = type;
        return this;
    }

    public List<KoreksiTransactionDetailModel> getDetails() {
        return details;
    }

    public KoreksiTransactionModel setDetails(List<KoreksiTransactionDetailModel> details) {
        this.details = details;
        return this;
    }
}
