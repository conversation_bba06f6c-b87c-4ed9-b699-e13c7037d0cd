package com.btpns.fin.model;

import java.io.Serializable;

public class Attachment implements Serializable {
    private String filename;
    private String mimetype;
    private String base64;
    public Attachment() {
    }
    public String getFilename() {
        return filename;
    }
    public void setFilename(String filename) {
        this.filename = filename;
    }
    public String getMimetype() {
        return mimetype;
    }
    public void setMimetype(String mimetype) {
        this.mimetype = mimetype;
    }
    public String getBase64() {
        return base64;
    }
    public void setBase64(String base64) {
        this.base64 = base64;
    }
}
