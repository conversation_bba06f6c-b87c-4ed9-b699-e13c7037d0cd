package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.util.List;

public class CashCountDetailModel {
    private String transactionId;
    @JsonFormat(pattern="yyyy-MM-dd")
    private LocalDate period;
    private String branchId;
    private String branchName;
    private String nikVerification;
    private String nameVerification;
    private Double total;
    private String reason;
    private List<AmountDetail> balanceDetails;

    public CashCountDetailModel(String transactionId, LocalDate period, String branchId, String branchName, String nikVerification, String nameVerification, String reason) {
        this.transactionId = transactionId;
        this.period = period;
        this.branchId = branchId;
        this.branchName = branchName;
        this.nikVerification = nikVerification;
        this.nameVerification = nameVerification;
        this.reason = reason;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public LocalDate getPeriod() {
        return period;
    }

    public void setPeriod(LocalDate period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getNikVerification() {
        return nikVerification;
    }

    public void setNikVerification(String nikVerification) {
        this.nikVerification = nikVerification;
    }
    public String getNameVerification() {
        return nameVerification;
    }

    public void setNameVerification(String nameVerification) {
        this.nameVerification = nameVerification;
    }



    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public List<AmountDetail> getBalanceDetails() {
        return balanceDetails;
    }

    public void setBalanceDetails(List<AmountDetail> balanceDetails) {
        this.balanceDetails = balanceDetails;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
