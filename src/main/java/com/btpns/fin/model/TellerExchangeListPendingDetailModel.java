package com.btpns.fin.model;

import com.btpns.fin.model.entity.BranchBalance;
import com.btpns.fin.model.entity.HeadTellerBalance;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.springframework.util.StringUtils;

import java.util.List;

import static com.btpns.fin.constant.CommonConstant.*;

public class TellerExchangeListPendingDetailModel {
    private String transactionId;
    private String type;
    private String typeDesc;
    private String tellerNIK;
    private String tellerName;
    private Double p100KAmount;
    private Double p75KAmount;
    private Double p50KAmount;
    private Double p20KAmount;
    private Double p10KAmount;
    private Double p5KAmount;
    private Double p2KAmount;
    private Double p1KAmount;
    private Double c1KAmount;
    private Double c500Amount;
    private Double c200Amount;
    private Double c100Amount;
    private Double c50Amount;
    private Double totalAmount;

    public TellerExchangeListPendingDetailModel(String transactionId, String type, String typeDesc, String tellerNIK, Double totalAmount, String amountDetails) {
        this.transactionId = transactionId;
        this.type = type;
        this.typeDesc = typeDesc;
        this.tellerNIK = tellerNIK;
        this.totalAmount = totalAmount;
        if (!StringUtils.isEmpty(amountDetails)) {
            Gson gson = new Gson();
            List<AmountDetail> detailAmount = gson.fromJson(amountDetails, new TypeToken<List<AmountDetail>>() {
            }.getType());
            for (AmountDetail amountDetail : detailAmount) {
                switch (amountDetail.getId()) {
                    case COIN_50:
                        this.setC50Amount(amountDetail.getTotal());
                        break;
                    case COIN_100:
                        this.setC100Amount(amountDetail.getTotal());
                        break;
                    case COIN_200:
                        this.setC200Amount(amountDetail.getTotal());
                        break;
                    case COIN_500:
                        this.setC500Amount(amountDetail.getTotal());
                        break;
                    case COIN_1K:
                        this.setC1KAmount(amountDetail.getTotal());
                        break;
                    case PAPER_1K:
                        this.setP1KAmount(amountDetail.getTotal());
                        break;
                    case PAPER_2K:
                        this.setP2KAmount(amountDetail.getTotal());
                        break;
                    case PAPER_5K:
                        this.setP5KAmount(amountDetail.getTotal());
                        break;
                    case PAPER_10K:
                        this.setP10KAmount(amountDetail.getTotal());
                        break;
                    case PAPER_20K:
                        this.setP20KAmount(amountDetail.getTotal());
                        break;
                    case PAPER_50K:
                        this.setP50KAmount(amountDetail.getTotal());
                        break;
                    case PAPER_75K:
                        this.setP75KAmount(amountDetail.getTotal());
                        break;
                    case PAPER_100K:
                        this.setP100KAmount(amountDetail.getTotal());
                        break;
                }
            }
        }
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }

    public String getTellerNIK() {
        return tellerNIK;
    }

    public void setTellerNIK(String tellerNIK) {
        this.tellerNIK = tellerNIK;
    }

    public String getTellerName() {
        return tellerName;
    }

    public void setTellerName(String tellerName) {
        this.tellerName = tellerName;
    }

    public Double getP100KAmount() {
        return p100KAmount;
    }

    public void setP100KAmount(Double p100KAmount) {
        this.p100KAmount = p100KAmount;
    }

    public Double getP75KAmount() {
        return p75KAmount;
    }

    public void setP75KAmount(Double p75KAmount) {
        this.p75KAmount = p75KAmount;
    }

    public Double getP50KAmount() {
        return p50KAmount;
    }

    public void setP50KAmount(Double p50KAmount) {
        this.p50KAmount = p50KAmount;
    }

    public Double getP20KAmount() {
        return p20KAmount;
    }

    public void setP20KAmount(Double p20KAmount) {
        this.p20KAmount = p20KAmount;
    }

    public Double getP10KAmount() {
        return p10KAmount;
    }

    public void setP10KAmount(Double p10KAmount) {
        this.p10KAmount = p10KAmount;
    }

    public Double getP5KAmount() {
        return p5KAmount;
    }

    public void setP5KAmount(Double p5KAmount) {
        this.p5KAmount = p5KAmount;
    }

    public Double getP2KAmount() {
        return p2KAmount;
    }

    public void setP2KAmount(Double p2KAmount) {
        this.p2KAmount = p2KAmount;
    }

    public Double getP1KAmount() {
        return p1KAmount;
    }

    public void setP1KAmount(Double p1KAmount) {
        this.p1KAmount = p1KAmount;
    }

    public Double getC1KAmount() {
        return c1KAmount;
    }

    public void setC1KAmount(Double c1KAmount) {
        this.c1KAmount = c1KAmount;
    }

    public Double getC500Amount() {
        return c500Amount;
    }

    public void setC500Amount(Double c500Amount) {
        this.c500Amount = c500Amount;
    }

    public Double getC200Amount() {
        return c200Amount;
    }

    public void setC200Amount(Double c200Amount) {
        this.c200Amount = c200Amount;
    }

    public Double getC100Amount() {
        return c100Amount;
    }

    public void setC100Amount(Double c100Amount) {
        this.c100Amount = c100Amount;
    }

    public Double getC50Amount() {
        return c50Amount;
    }

    public void setC50Amount(Double c50Amount) {
        this.c50Amount = c50Amount;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public void setSaldoAwal(HeadTellerBalance balance){
        this.setP100KAmount(balance.getP100KAmount());
        this.setP75KAmount(balance.getP75KAmount());
        this.setP50KAmount(balance.getP50KAmount());
        this.setP20KAmount(balance.getP20KAmount());
        this.setP10KAmount(balance.getP10KAmount());
        this.setP5KAmount(balance.getP5KAmount());
        this.setP2KAmount(balance.getP2KAmount());
        this.setP1KAmount(balance.getP1KAmount());
        this.setC1KAmount(balance.getC1KAmount());
        this.setC500Amount(balance.getC500Amount());
        this.setC200Amount(balance.getC200Amount());
        this.setC100Amount(balance.getC100Amount());
        this.setC50Amount(balance.getC50Amount());
    }

    public void setSaldoVault(BalanceModel balance){
        this.setP100KAmount(balance.getP100KAmount());
        this.setP75KAmount(balance.getP75KAmount());
        this.setP50KAmount(balance.getP50KAmount());
        this.setP20KAmount(balance.getP20KAmount());
        this.setP10KAmount(balance.getP10KAmount());
        this.setP5KAmount(balance.getP5KAmount());
        this.setP2KAmount(balance.getP2KAmount());
        this.setP1KAmount(balance.getP1KAmount());
        this.setC1KAmount(balance.getC1KAmount());
        this.setC500Amount(balance.getC500Amount());
        this.setC200Amount(balance.getC200Amount());
        this.setC100Amount(balance.getC100Amount());
        this.setC50Amount(balance.getC50Amount());
    }
    public void setSaldoAwal(BranchBalance balance){
        this.setP100KAmount(balance.getP100KAmount());
        this.setP75KAmount(balance.getP75KAmount());
        this.setP50KAmount(balance.getP50KAmount());
        this.setP20KAmount(balance.getP20KAmount());
        this.setP10KAmount(balance.getP10KAmount());
        this.setP5KAmount(balance.getP5KAmount());
        this.setP2KAmount(balance.getP2KAmount());
        this.setP1KAmount(balance.getP1KAmount());
        this.setC1KAmount(balance.getC1KAmount());
        this.setC500Amount(balance.getC500Amount());
        this.setC200Amount(balance.getC200Amount());
        this.setC100Amount(balance.getC100Amount());
        this.setC50Amount(balance.getC50Amount());
    }
}
