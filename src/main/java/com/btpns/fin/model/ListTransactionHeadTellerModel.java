package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

public class ListTransactionHeadTellerModel {
    private String transactionId;
    private String timestamp;
    private String type;
    private String transactionType;
    private Double p100KAmount;
    private Double p75KAmount;
    private Double p50KAmount;
    private Double p20KAmount;
    private Double p10KAmount;
    private Double p5KAmount;
    private Double p2KAmount;
    private Double p1KAmount;
    private Double c1KAmount;
    private Double c500Amount;
    private Double c200Amount;
    private Double c100Amount;
    private Double c50Amount;
    private Double totalAmount;
    private String tellerId;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public Double getP100KAmount() {
        return p100KAmount;
    }

    public void setP100KAmount(Double p100KAmount) {
        this.p100KAmount = p100KAmount;
    }

    public Double getP75KAmount() {
        return p75KAmount;
    }

    public void setP75KAmount(Double p75KAmount) {
        this.p75KAmount = p75KAmount;
    }

    public Double getP50KAmount() {
        return p50KAmount;
    }

    public void setP50KAmount(Double p50KAmount) {
        this.p50KAmount = p50KAmount;
    }

    public Double getP20KAmount() {
        return p20KAmount;
    }

    public void setP20KAmount(Double p20KAmount) {
        this.p20KAmount = p20KAmount;
    }

    public Double getP10KAmount() {
        return p10KAmount;
    }

    public void setP10KAmount(Double p10KAmount) {
        this.p10KAmount = p10KAmount;
    }

    public Double getP5KAmount() {
        return p5KAmount;
    }

    public void setP5KAmount(Double p5KAmount) {
        this.p5KAmount = p5KAmount;
    }

    public Double getP2KAmount() {
        return p2KAmount;
    }

    public void setP2KAmount(Double p2KAmount) {
        this.p2KAmount = p2KAmount;
    }

    public Double getP1KAmount() {
        return p1KAmount;
    }

    public void setP1KAmount(Double p1KAmount) {
        this.p1KAmount = p1KAmount;
    }

    public Double getC1KAmount() {
        return c1KAmount;
    }

    public void setC1KAmount(Double c1KAmount) {
        this.c1KAmount = c1KAmount;
    }

    public Double getC500Amount() {
        return c500Amount;
    }

    public void setC500Amount(Double c500Amount) {
        this.c500Amount = c500Amount;
    }

    public Double getC200Amount() {
        return c200Amount;
    }

    public void setC200Amount(Double c200Amount) {
        this.c200Amount = c200Amount;
    }

    public Double getC100Amount() {
        return c100Amount;
    }

    public void setC100Amount(Double c100Amount) {
        this.c100Amount = c100Amount;
    }

    public Double getC50Amount() {
        return c50Amount;
    }

    public void setC50Amount(Double c50Amount) {
        this.c50Amount = c50Amount;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTellerId() {
        return tellerId;
    }

    public void setTellerId(String tellerId) {
        this.tellerId = tellerId;
    }
}
