package com.btpns.fin.model;

import java.util.List;

public class KoreksiDetail {
    
    private String transactionId;
    
    private Double total;
    
    private String reason;
    private String rejectReason;

    private String nikVerification;
    
    private String nameVerification;

    private String branchId;

    private String branchName;
    
    private String period;
    
    private List<KoreksiBalanceDetail> balanceDetail;

    public String getTransactionId() {
        return transactionId;
    }

    public KoreksiDetail setTransactionId(String transactionId) {
        this.transactionId = transactionId;
        return this;
    }

    public Double getTotal() {
        return total;
    }

    public KoreksiDetail setTotal(Double total) {
        this.total = total;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public KoreksiDetail setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public KoreksiDetail setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
        return this;
    }

    public String getNikVerification() {
        return nikVerification;
    }

    public KoreksiDetail setNikVerification(String nikVerification) {
        this.nikVerification = nikVerification;
        return this;
    }

    public String getNameVerification() {
        return nameVerification;
    }

    public KoreksiDetail setNameVerification(String nameVerification) {
        this.nameVerification = nameVerification;
        return this;
    }

    public String getBranchId() {
        return branchId;
    }

    public KoreksiDetail setBranchId(String branchId) {
        this.branchId = branchId;
        return this;}
    

    public String getBranchName() {
        return branchName;
    }

    public KoreksiDetail setBranchName(String branchName) {
        this.branchName = branchName;
        return this;
    }

    public String getPeriod() {
        return period;
    }

    public KoreksiDetail setPeriod(String period) {
        this.period = period;
        return this;
    }

    public List<KoreksiBalanceDetail> getBalanceDetail() {
        return balanceDetail;
    }

    public KoreksiDetail setBalanceDetail(List<KoreksiBalanceDetail> balanceDetail) {
        this.balanceDetail = balanceDetail;
        return this;
    }
}
