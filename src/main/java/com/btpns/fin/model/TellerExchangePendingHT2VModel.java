package com.btpns.fin.model;

import java.util.List;

public class TellerExchangePendingHT2VModel {
    private String requestId;
    private String transactionId;
    private String period;
    private String branchId;
    private String branchName;
    private String status;
    private String fromName;
    private String toName;
    private String inputerNik;
    private String inputerName;
    private String verificationNik;
    private String verificationName;
    private boolean requestFlag;
    private boolean depositFlag;
    private Double totalBeginBalance;
    private Double totalAmount;
    private Double totalRemainingBalance;
    private List<HeadTellerAmountDetail> amountDetails;
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }
    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getToName() {
        return toName;
    }

    public void setToName(String toName) {
        this.toName = toName;
    }

    public String getInputerNik() {
        return inputerNik;
    }

    public void setInputerNik(String inputerNik) {
        this.inputerNik = inputerNik;
    }

    public String getInputerName() {
        return inputerName;
    }

    public void setInputerName(String inputerName) {
        this.inputerName = inputerName;
    }

    public String getVerificationNik() {
        return verificationNik;
    }

    public void setVerificationNik(String verificationNik) {
        this.verificationNik = verificationNik;
    }

    public String getVerificationName() {
        return verificationName;
    }

    public void setVerificationName(String verificationName) {
        this.verificationName = verificationName;
    }

    public boolean isRequestFlag() {
        return requestFlag;
    }

    public void setRequestFlag(boolean requestFlag) {
        this.requestFlag = requestFlag;
    }

    public boolean isDepositFlag() {
        return depositFlag;
    }

    public void setDepositFlag(boolean depositFlag) {
        this.depositFlag = depositFlag;
    }

    public Double getTotalBeginBalance() {
        return totalBeginBalance;
    }

    public void setTotalBeginBalance(Double totalBeginBalance) {
        this.totalBeginBalance = totalBeginBalance;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Double getTotalRemainingBalance() {
        return totalRemainingBalance;
    }

    public void setTotalRemainingBalance(Double totalRemainingBalance) {
        this.totalRemainingBalance = totalRemainingBalance;
    }

    public List<HeadTellerAmountDetail> getAmountDetails() {
        return amountDetails;
    }

    public void setAmountDetails(List<HeadTellerAmountDetail> amountDetails) {
        this.amountDetails = amountDetails;
    }
}
