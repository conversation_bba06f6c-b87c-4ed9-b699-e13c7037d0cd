package com.btpns.fin.model;

import java.util.List;

public class TellerExchangeListPendingModel {
    private String period;
    private String branchId;
    private String branchName;
    private List<TellerExchangeListPendingDetailModel> transactions;

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public List<TellerExchangeListPendingDetailModel> getTransactions() {
        return transactions;
    }

    public void setTransactions(List<TellerExchangeListPendingDetailModel> transactions) {
        this.transactions = transactions;
    }
}
