package com.btpns.fin.model;


public class OfficerNonProsperaDetailModel {

        private String id;
        private String nik;
        private String name;
        private String branchId;
        private String branchName;
        private String roleId;
        private String roleName;
        private Boolean status;

        public OfficerNonProsperaDetailModel(Long id, String nik, String name, String branchId, String branchName, String roleId, String roleName, Boolean status) {
                this.id = id.toString();
                this.nik = nik;
                this.name = name;
                this.branchId = branchId;
                this.branchName = branchName;
                this.roleId = roleId;
                this.roleName = roleName;
                this.status = status;
        }

        public String getId() {
                return id;
        }

        public void setId(String id) {
                this.id = id;
        }

        public String getNik() {
                return nik;
        }

        public void setNik(String nik) {
                this.nik = nik;
        }

        public String getName() {
                return name;
        }

        public void setName(String name) {
                this.name = name;
        }

        public String getBranchId() {
                return branchId;
        }

        public void setBranchId(String branchId) {
                this.branchId = branchId;
        }

        public String getBranchName() {
                return branchName;
        }

        public void setBranchName(String branchName) {
                this.branchName = branchName;
        }

        public String getRoleId() {
                return roleId;
        }

        public void setRoleId(String roleId) {
                this.roleId = roleId;
        }

        public String getRoleName() {
                return roleName;
        }

        public void setRoleName(String roleName) {
                this.roleName = roleName;
        }

        public Boolean getStatus() {
                return status;
        }

        public void setStatus(Boolean status) {
                this.status = status;
        }
}
