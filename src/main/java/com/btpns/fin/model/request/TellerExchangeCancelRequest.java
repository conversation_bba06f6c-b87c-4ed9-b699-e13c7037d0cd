package com.btpns.fin.model.request;

public class TellerExchangeCancelRequest {
    private String requestId;
    private String period;
    private String branchId;
    private String cancelTransactionId;
    private String reason;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getCancelTransactionId() {
        return cancelTransactionId;
    }

    public void setCancelTransactionId(String cancelTransactionId) {
        this.cancelTransactionId = cancelTransactionId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
