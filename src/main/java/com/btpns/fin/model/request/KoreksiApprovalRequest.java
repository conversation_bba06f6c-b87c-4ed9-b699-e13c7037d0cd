package com.btpns.fin.model.request;


public class KoreksiApprovalRequest {
    
    private String requestId;
    
    private String period;
    
    private String branchId;
    
    private String approvalTransactionId;
    
    private String nikVerification;
    
    private String status;
    
    private String Reason;
    
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getApprovalTransactionId() {
        return approvalTransactionId;
    }

    public void setApprovalTransactionId(String approvalTransactionId) {
        this.approvalTransactionId = approvalTransactionId;
    }

    public String getNikVerification() {
        return nikVerification;
    }

    public void setNikVerification(String nikVerification) {
        this.nikVerification = nikVerification;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return Reason;
    }

    public void setReason(String reason) {
        Reason = reason;
    }
}
