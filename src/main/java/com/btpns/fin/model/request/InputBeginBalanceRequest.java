package com.btpns.fin.model.request;

import com.btpns.fin.model.AmountDetail;

import java.util.List;

public class InputBeginBalanceRequest {
    private String requestId;
    private String period;
    private String branchId;
    private Double totalBalance;
    private String reason;
    private List<AmountDetail> balanceDetails;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public Double getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(Double totalBalance) {
        this.totalBalance = totalBalance;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<AmountDetail> getBalanceDetails() {
        return balanceDetails;
    }

    public void setBalanceDetails(List<AmountDetail> balanceDetails) {
        this.balanceDetails = balanceDetails;
    }
}
