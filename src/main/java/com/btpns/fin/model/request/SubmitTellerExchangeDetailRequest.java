package com.btpns.fin.model.request;


import com.btpns.fin.model.AmountDetail;

import java.util.List;

public class SubmitTellerExchangeDetailRequest {
    private String requestId;
    private String transactionId;
    private String branchId;
    private String period;
    private String type;
    private String fromName;
    private String toName;
    private boolean requestFlag;
    private boolean depositFlag;
    private String status;
    private String inputerNIK;
    private String verificationNIK;
    private Double totalAmount;
    private String totalAmountSpelled;
    private List<AmountDetail> amountDetail;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getToName() {
        return toName;
    }

    public void setToName(String toName) {
        this.toName = toName;
    }

    public boolean isRequestFlag() {
        return requestFlag;
    }

    public void setRequestFlag(boolean requestFlag) {
        this.requestFlag = requestFlag;
    }

    public boolean isDepositFlag() {
        return depositFlag;
    }

    public void setDepositFlag(boolean depositFlag) {
        this.depositFlag = depositFlag;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getInputerNIK() {
        return inputerNIK;
    }

    public void setInputerNIK(String inputerNIK) {
        this.inputerNIK = inputerNIK;
    }

    public String getVerificationNIK() {
        return verificationNIK;
    }

    public void setVerificationNIK(String verificationNIK) {
        this.verificationNIK = verificationNIK;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTotalAmountSpelled() {
        return totalAmountSpelled;
    }

    public void setTotalAmountSpelled(String totalAmountSpelled) {
        this.totalAmountSpelled = totalAmountSpelled;
    }

    public List<AmountDetail> getAmountDetail() {
        return amountDetail;
    }

    public void setAmountDetail(List<AmountDetail> amountDetail) {
        this.amountDetail = amountDetail;
    }
}
