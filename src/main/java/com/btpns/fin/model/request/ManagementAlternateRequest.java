package com.btpns.fin.model.request;

import com.btpns.fin.model.ManagementAlternateModel;

public class ManagementAlternateRequest {
    private String mode;
    private String type;
    private ManagementAlternateModel alternate;

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public ManagementAlternateModel getAlternate() {
        return alternate;
    }

    public void setAlternate(ManagementAlternateModel alternate) {
        this.alternate = alternate;
    }
}
