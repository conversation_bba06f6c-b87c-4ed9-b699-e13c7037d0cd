package com.btpns.fin.model.request;

import com.btpns.fin.model.AmountDetail;

import java.util.List;

public class InputHeadTellerPendingRequest {
    private String requestId;
    private String period;
    private String branchId;
    private Double totalAmount;
    private String nikTeller;
    private List<AmountDetail> amountDetails;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getNikTeller() {
        return nikTeller;
    }

    public void setNikTeller(String nikTeller) {
        this.nikTeller = nikTeller;
    }

    public List<AmountDetail> getAmountDetails() {
        return amountDetails;
    }

    public void setAmountDetails(List<AmountDetail> amountDetails) {
        this.amountDetails = amountDetails;
    }
}
