package com.btpns.fin.model.request;

import com.btpns.fin.model.AmountDetail;

import java.time.LocalDate;
import java.util.List;

public class InputHT2VRequest {
    private String requestId;
    private String transactionId;
    private String period;
    private String branchId;
    private Double totalAmount;

    private List<AmountDetail> amountDetails;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<AmountDetail> getAmountDetails() {
        return amountDetails;
    }

    public void setAmountDetails(List<AmountDetail> amountDetails) {
        this.amountDetails = amountDetails;
    }
}
