package com.btpns.fin.model.request;

import com.google.gson.Gson;

public class LimitInsuranceRequestDetail {
    private String requestId;
    private String branchId;
    private Double limitAsuransiLembesAmount;
    private Double limitAsuransiCashBoxAmount;
    private String username;
    private String password;
    private String email;
    private String branchName;

    public LimitInsuranceRequestDetail clone(Gson gson) {
        return gson.fromJson(gson.toJson(this), LimitInsuranceRequestDetail.class);
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public Double getLimitAsuransiLembesAmount() {
        return limitAsuransiLembesAmount;
    }

    public void setLimitAsuransiLembesAmount(Double limitAsuransiLembesAmount) {
        this.limitAsuransiLembesAmount = limitAsuransiLembesAmount;
    }

    public Double getLimitAsuransiCashBoxAmount() {
        return limitAsuransiCashBoxAmount;
    }

    public void setLimitAsuransiCashBoxAmount(Double limitAsuransiCashBoxAmount) {
        this.limitAsuransiCashBoxAmount = limitAsuransiCashBoxAmount;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }
}
