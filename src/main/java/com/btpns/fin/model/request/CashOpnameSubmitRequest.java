package com.btpns.fin.model.request;

import com.btpns.fin.model.AmountDetail;

import java.util.List;

public class CashOpnameSubmitRequest {
    private String mode;
    private String transactionId;
    private String requestId;
    private String period;
    private String branchId;
    private Double totalBalance;
    private Double oldTotalBalance;
    private Double carryBalance;
    private Double totalPaperBalance;
    private Double totalCoinBalance;
    private String nikTeller;
    private String nikBOS;
    private String nikBM;
    private String nikBOM;
    private String nikQA;
    private String reason;
    private String nikNOM;
    private String nikODH;
    private String nikSKAI;
    private String nikAltTeller;
    private String nikQA2;

    private List<AmountDetail> balanceDetails;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public Double getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(Double totalBalance) {
        this.totalBalance = totalBalance;
    }

    public Double getOldTotalBalance() {
        return oldTotalBalance;
    }

    public void setOldTotalBalance(Double oldTotalBalance) {
        this.oldTotalBalance = oldTotalBalance;
    }

    public Double getCarryBalance() {
        return carryBalance;
    }

    public void setCarryBalance(Double carryBalance) {
        this.carryBalance = carryBalance;
    }

    public Double getTotalPaperBalance() {
        return totalPaperBalance;
    }

    public void setTotalPaperBalance(Double totalPaperBalance) {
        this.totalPaperBalance = totalPaperBalance;
    }

    public Double getTotalCoinBalance() {
        return totalCoinBalance;
    }

    public void setTotalCoinBalance(Double totalCoinBalance) {
        this.totalCoinBalance = totalCoinBalance;
    }

    public String getNikTeller() {
        return nikTeller;
    }

    public void setNikTeller(String nikTeller) {
        this.nikTeller = nikTeller;
    }

    public String getNikBOS() {
        return nikBOS;
    }

    public void setNikBOS(String nikBOS) {
        this.nikBOS = nikBOS;
    }

    public String getNikBOM() {
        return nikBOM;
    }

    public void setNikBOM(String nikBOM) {
        this.nikBOM = nikBOM;
    }

    public String getNikBM() {
        return nikBM;
    }

    public void setNikBM(String nikBM) {
        this.nikBM = nikBM;
    }

    public String getNikQA() {
        return nikQA;
    }

    public void setNikQA(String nikQA) {
        this.nikQA = nikQA;
    }

    public String getNikNOM() {
        return nikNOM;
    }

    public void setNikNOM(String nikNOM) {
        this.nikNOM = nikNOM;
    }

    public String getNikAltTeller() {
        return nikAltTeller;
    }

    public void setNikAltTeller(String nikAltTeller) {
        this.nikAltTeller = nikAltTeller;
    }

    public String getNikODH() {
        return nikODH;
    }

    public void setNikODH(String nikODH) {
        this.nikODH = nikODH;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<AmountDetail> getBalanceDetails() {
        return balanceDetails;
    }

    public void setBalanceDetails(List<AmountDetail> balanceDetails) {
        this.balanceDetails = balanceDetails;
    }

    public String getNikQA2() {
        return nikQA2;
    }

    public void setNikQA2(String nikQA2) {
        this.nikQA2 = nikQA2;
    }

    public String getNikSKAI() {
        return nikSKAI;
    }

    public void setNikSKAI(String nikSKAI) {
        this.nikSKAI = nikSKAI;
    }
}
