package com.btpns.fin.model.response;

import com.btpns.fin.model.AmountDetail;
import com.btpns.fin.model.VerificationStatusModel;
import com.btpns.fin.model.entity.CashOpnameDetail;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.List;

public class CashOpnameDetailResponse {
    private String requestId;
    private String transactionId;
    private String period;
    private String branchId;
    private String branchName;
    private Double totalBalance;
    private Double oldTotalBalance;
    private Double carryBalance;
    private Double totalPaperBalance;
    private Double totalCoinBalance;
    private String nikBMBranch;
    private String nameBMBranch;
    private VerificationStatusModel verificationTeller;
    private VerificationStatusModel verificationBOS;
    private VerificationStatusModel verificationBOM;
    private VerificationStatusModel verificationBM;
    private VerificationStatusModel verificationQA;
    private VerificationStatusModel verificationNOM;
    private VerificationStatusModel verificationODH;
    private VerificationStatusModel verificationSKAI;
    private VerificationStatusModel verificationAltTeller;
    private VerificationStatusModel verificationQA2;
    private String status;
    private String reason;
    private List<AmountDetail> balanceDetails;


    public CashOpnameDetailResponse(CashOpnameDetail cashOpnameDetail) {
        Gson gson = new Gson();
        this.requestId = cashOpnameDetail.getRequestId();
        this.transactionId = cashOpnameDetail.getTransactionId();
        this.period = cashOpnameDetail.getPeriod();
        this.branchId = cashOpnameDetail.getBranchId();
        this.branchName = cashOpnameDetail.getBranchName();
        this.totalBalance = cashOpnameDetail.getTotalBalance();
        this.oldTotalBalance = cashOpnameDetail.getOldTotalBalance();
        this.carryBalance = cashOpnameDetail.getCarryBalance();
        this.totalPaperBalance = cashOpnameDetail.getTotalPaperBalance();
        this.totalCoinBalance = cashOpnameDetail.getTotalCoinBalance();
        this.nikBMBranch = cashOpnameDetail.getNikBMBranch();
        this.nameBMBranch = cashOpnameDetail.getNameBMBranch();
        this.verificationTeller = new VerificationStatusModel(cashOpnameDetail.getNikTeller(),cashOpnameDetail.getNameTeller(),cashOpnameDetail.getStatusVerificationTeller(),cashOpnameDetail.getDateVerificationTeller());
        this.verificationBM = new VerificationStatusModel(cashOpnameDetail.getNikBM(),cashOpnameDetail.getNameBM(),cashOpnameDetail.getStatusVerificationBM(),cashOpnameDetail.getDateVerificationBM());
        this.verificationBOM = new VerificationStatusModel(cashOpnameDetail.getNikBOM(),cashOpnameDetail.getNameBOM(),cashOpnameDetail.getStatusVerificationBOM(),cashOpnameDetail.getDateVerificationBOM());
        this.verificationBOS = new VerificationStatusModel(cashOpnameDetail.getNikBOS(),cashOpnameDetail.getNameBOS(),cashOpnameDetail.getStatusVerificationBOS(),cashOpnameDetail.getDateVerificationBOS());
        this.verificationQA = new VerificationStatusModel(cashOpnameDetail.getNikQA(),cashOpnameDetail.getNameQA(),cashOpnameDetail.getStatusVerificationQA(),cashOpnameDetail.getDateVerificationQA());
        this.verificationNOM = new VerificationStatusModel(cashOpnameDetail.getNikNOM(),cashOpnameDetail.getNameNOM(),cashOpnameDetail.getStatusVerificationNOM(),cashOpnameDetail.getDateVerificationNOM());
        this.verificationODH = new VerificationStatusModel(cashOpnameDetail.getNikODH(),cashOpnameDetail.getNameODH(),cashOpnameDetail.getStatusVerificationODH(),cashOpnameDetail.getDateVerificationODH());
        this.verificationSKAI = new VerificationStatusModel(cashOpnameDetail.getNikSKAI(),cashOpnameDetail.getNameSKAI(),cashOpnameDetail.getStatusVerificationSKAI(),cashOpnameDetail.getDateVerificationSKAI());
        this.verificationAltTeller = new VerificationStatusModel(cashOpnameDetail.getNikAltTeller(),cashOpnameDetail.getNameAltTeller(),cashOpnameDetail.getStatusVerificationAltTeller(),cashOpnameDetail.getDateVerificationAltTeller());
        this.verificationQA2 = new VerificationStatusModel(cashOpnameDetail.getNikQA2(),cashOpnameDetail.getNameQA2(),cashOpnameDetail.getStatusVerificationQA2(),cashOpnameDetail.getDateVerificationQA2());
        this.status = cashOpnameDetail.getStatus();
        this.reason = cashOpnameDetail.getReason();
        this.balanceDetails = gson.fromJson(cashOpnameDetail.getBalanceDetails(), new TypeToken<List<AmountDetail>>() {
        }.getType());
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public Double getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(Double totalBalance) {
        this.totalBalance = totalBalance;
    }

    public Double getOldTotalBalance() {
        return oldTotalBalance;
    }

    public void setOldTotalBalance(Double oldTotalBalance) {
        this.oldTotalBalance = oldTotalBalance;
    }

    public Double getCarryBalance() {
        return carryBalance;
    }

    public void setCarryBalance(Double carryBalance) {
        this.carryBalance = carryBalance;
    }

    public Double getTotalPaperBalance() {
        return totalPaperBalance;
    }

    public void setTotalPaperBalance(Double totalPaperBalance) {
        this.totalPaperBalance = totalPaperBalance;
    }

    public Double getTotalCoinBalance() {
        return totalCoinBalance;
    }

    public void setTotalCoinBalance(Double totalCoinBalance) {
        this.totalCoinBalance = totalCoinBalance;
    }


    public String getNikBMBranch() {
        return nikBMBranch;
    }

    public void setNikBMBranch(String nikBMBranch) {
        this.nikBMBranch = nikBMBranch;
    }

    public String getNameBMBranch() {
        return nameBMBranch;
    }

    public void setNameBMBranch(String nameBMBranch) {
        this.nameBMBranch = nameBMBranch;
    }

    public VerificationStatusModel getVerificationBOM() {
        return verificationBOM;
    }

    public void setVerificationBOM(VerificationStatusModel verificationBOM) {
        this.verificationBOM = verificationBOM;
    }

    public VerificationStatusModel getVerificationBM() {
        return verificationBM;
    }

    public void setVerificationBM(VerificationStatusModel verificationBM) {
        this.verificationBM = verificationBM;
    }

    public VerificationStatusModel getVerificationBOS() {
        return verificationBOS;
    }

    public void setVerificationBOS(VerificationStatusModel verificationBOS) {
        this.verificationBOS = verificationBOS;
    }

    public VerificationStatusModel getVerificationTeller() {
        return verificationTeller;
    }

    public void setVerificationTeller(VerificationStatusModel verificationTeller) {
        this.verificationTeller = verificationTeller;
    }

    public VerificationStatusModel getVerificationQA() {
        return verificationQA;
    }

    public void setVerificationQA(VerificationStatusModel verificationQA) {
        this.verificationQA = verificationQA;
    }

    public VerificationStatusModel getVerificationNOM() {
        return verificationNOM;
    }

    public void setVerificationNOM(VerificationStatusModel verificationNOM) {
        this.verificationNOM = verificationNOM;
    }

    public VerificationStatusModel getVerificationODH() {
        return verificationODH;
    }

    public void setVerificationODH(VerificationStatusModel verificationODH) {
        this.verificationODH = verificationODH;
    }

    public VerificationStatusModel getVerificationAltTeller() {
        return verificationAltTeller;
    }

    public void setVerificationAltTeller(VerificationStatusModel verificationAltTeller) {
        this.verificationAltTeller = verificationAltTeller;
    }

    public VerificationStatusModel getVerificationQA2() {
        return verificationQA2;
    }

    public void setVerificationQA2(VerificationStatusModel verificationQA2) {
        this.verificationQA2 = verificationQA2;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<AmountDetail> getBalanceDetails() {
        return balanceDetails;
    }

    public void setBalanceDetails(List<AmountDetail> balanceDetails) {
        this.balanceDetails = balanceDetails;
    }

    public VerificationStatusModel getVerificationSKAI() {
        return verificationSKAI;
    }

    public void setVerificationSKAI(VerificationStatusModel verificationSKAI) {
        this.verificationSKAI = verificationSKAI;
    }
}
