package com.btpns.fin.model.response;

import com.btpns.fin.model.BalanceStatusDetailModel;

public class BalanceStatusResponse {
    private String branchId;
    private String branchName;
    private BalanceStatusDetailModel kasBesar;
    private BalanceStatusDetailModel headTeller;

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public BalanceStatusDetailModel getKasBesar() {
        return kasBesar;
    }

    public void setKasBesar(BalanceStatusDetailModel kasBesar) {
        this.kasBesar = kasBesar;
    }

    public BalanceStatusDetailModel getHeadTeller() {
        return headTeller;
    }

    public void setHeadTeller(BalanceStatusDetailModel headTeller) {
        this.headTeller = headTeller;
    }
}
