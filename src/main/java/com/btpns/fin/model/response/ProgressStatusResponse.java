package com.btpns.fin.model.response;

import com.btpns.fin.constant.CommonConstant;
import com.btpns.fin.model.StatusProgressModel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.btpns.fin.constant.CommonConstant.*;

public class ProgressStatusResponse {
    private String period;
    private String branchId;
    private String branchName;
    private List<StatusProgressModel> kasBesar;
    private List<StatusProgressModel> kasHeadTeller;

    public Map<String, StatusProgressModel> defaultStatusProgressKasBesar(){
        Map<String, StatusProgressModel> statusProgress = new HashMap<>();
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_START, new StatusProgressModel(INDICATOR_PROGRESS_START, INDICATOR_PROGRESS_DONE));
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_KHT, new StatusProgressModel(INDICATOR_PROGRESS_KHT, ""));
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_MHT, new StatusProgressModel(INDICATOR_PROGRESS_MHT, ""));
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_END, new StatusProgressModel(INDICATOR_PROGRESS_END, ""));
        return statusProgress;
    }
    public Map<String, StatusProgressModel> defaultStatusProgressKasHeadTeller(){
        Map<String, StatusProgressModel> statusProgress = new HashMap<>();
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_START, new StatusProgressModel(INDICATOR_PROGRESS_START, INDICATOR_PROGRESS_DONE));
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_V2HT, new StatusProgressModel(INDICATOR_PROGRESS_V2HT, ""));
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_HT2T, new StatusProgressModel(INDICATOR_PROGRESS_HT2T, ""));
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_T2HT, new StatusProgressModel(INDICATOR_PROGRESS_T2HT, ""));
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_HT2V, new StatusProgressModel(INDICATOR_PROGRESS_HT2V, ""));
        statusProgress.putIfAbsent(INDICATOR_PROGRESS_END, new StatusProgressModel(INDICATOR_PROGRESS_END, ""));
        return statusProgress;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public List<StatusProgressModel> getKasBesar() {
        return kasBesar;
    }

    public void setKasBesar(List<StatusProgressModel> kasBesar) {
        this.kasBesar = kasBesar;
    }

    public List<StatusProgressModel> getKasHeadTeller() {
        return kasHeadTeller;
    }

    public void setKasHeadTeller(List<StatusProgressModel> kasHeadTeller) {
        this.kasHeadTeller = kasHeadTeller;
    }
}
