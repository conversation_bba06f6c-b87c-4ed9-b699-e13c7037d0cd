package com.btpns.fin.model.response;


public class PendingHeadTellerTransactionResponse {
    private String period;
    private String branchId;
    private Integer ht2t = 0;
    private Integer t2ht = 0;

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public Integer getHt2t() {
        return ht2t;
    }

    public void setHt2t(Integer ht2t) {
        this.ht2t = ht2t;
    }

    public Integer getT2ht() {
        return t2ht;
    }

    public void setT2ht(Integer t2ht) {
        this.t2ht = t2ht;
    }
}
