package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "MsEmployeeHierarchy")
public class EmployeeHierarchy {
    @Id
    private String nik;
    private String fullName;
    private LocalDateTime dtJoin;
    private String dtPermanent;
    private String occupationDesc;
    private String organization;
    private String location;
    private String directSupervisorNIK;
    private String directSupervisorName;
    private String directSupervisorOccupation;
    private String directSupervisorOrganization;
    private String directSupervisor2NIK;
    private String directSupervisor2Name;
    private String directSupervisor2Occupation;
    private String directSupervisor2Organization;
    private String dtTermination;
    private LocalDateTime dtPopulateSource;
    private String fieldChecksum;
    private LocalDateTime dtPopulate;
    private String srcSystem;
    private String sysPopulate;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public LocalDateTime getDtJoin() {
        return dtJoin;
    }

    public void setDtJoin(LocalDateTime dtJoin) {
        this.dtJoin = dtJoin;
    }

    public String getDtPermanent() {
        return dtPermanent;
    }

    public void setDtPermanent(String dtPermanent) {
        this.dtPermanent = dtPermanent;
    }

    public String getOccupationDesc() {
        return occupationDesc;
    }

    public void setOccupationDesc(String occupationDesc) {
        this.occupationDesc = occupationDesc;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDirectSupervisorNIK() {
        return directSupervisorNIK;
    }

    public void setDirectSupervisorNIK(String directSupervisorNIK) {
        this.directSupervisorNIK = directSupervisorNIK;
    }

    public String getDirectSupervisorName() {
        return directSupervisorName;
    }

    public void setDirectSupervisorName(String directSupervisorName) {
        this.directSupervisorName = directSupervisorName;
    }

    public String getDirectSupervisorOccupation() {
        return directSupervisorOccupation;
    }

    public void setDirectSupervisorOccupation(String directSupervisorOccupation) {
        this.directSupervisorOccupation = directSupervisorOccupation;
    }

    public String getDirectSupervisorOrganization() {
        return directSupervisorOrganization;
    }

    public void setDirectSupervisorOrganization(String directSupervisorOrganization) {
        this.directSupervisorOrganization = directSupervisorOrganization;
    }

    public String getDirectSupervisor2NIK() {
        return directSupervisor2NIK;
    }

    public void setDirectSupervisor2NIK(String directSupervisor2NIK) {
        this.directSupervisor2NIK = directSupervisor2NIK;
    }

    public String getDirectSupervisor2Name() {
        return directSupervisor2Name;
    }

    public void setDirectSupervisor2Name(String directSupervisor2Name) {
        this.directSupervisor2Name = directSupervisor2Name;
    }

    public String getDirectSupervisor2Occupation() {
        return directSupervisor2Occupation;
    }

    public void setDirectSupervisor2Occupation(String directSupervisor2Occupation) {
        this.directSupervisor2Occupation = directSupervisor2Occupation;
    }

    public String getDirectSupervisor2Organization() {
        return directSupervisor2Organization;
    }

    public void setDirectSupervisor2Organization(String directSupervisor2Organization) {
        this.directSupervisor2Organization = directSupervisor2Organization;
    }

    public String getDtTermination() {
        return dtTermination;
    }

    public void setDtTermination(String dtTermination) {
        this.dtTermination = dtTermination;
    }

    public LocalDateTime getDtPopulateSource() {
        return dtPopulateSource;
    }

    public void setDtPopulateSource(LocalDateTime dtPopulateSource) {
        this.dtPopulateSource = dtPopulateSource;
    }

    public String getFieldChecksum() {
        return fieldChecksum;
    }

    public void setFieldChecksum(String fieldChecksum) {
        this.fieldChecksum = fieldChecksum;
    }

    public LocalDateTime getDtPopulate() {
        return dtPopulate;
    }

    public void setDtPopulate(LocalDateTime dtPopulate) {
        this.dtPopulate = dtPopulate;
    }

    public String getSrcSystem() {
        return srcSystem;
    }

    public void setSrcSystem(String srcSystem) {
        this.srcSystem = srcSystem;
    }

    public String getSysPopulate() {
        return sysPopulate;
    }

    public void setSysPopulate(String sysPopulate) {
        this.sysPopulate = sysPopulate;
    }
}
