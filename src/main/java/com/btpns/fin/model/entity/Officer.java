package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Table(name = "MSOFFICER")
public class Officer {
    @Id
    private Integer officerId;
    private Double mProsperaOfficerID;
    private Integer mmsId;
    private Integer roleID;
    @Size(max = 5)
    private String mmsCode;
    @Size(max = 6)
    private String officerCode;
    private Date dtPopulate;
    @Size(max = 50)
    private String SysPopulate;
    @Size(max = 200)
    private String OfficerName;
    @Size(max = 200)
    private String nik;
    @Size(max = 200)
    private String loginName;
    @Size(max = 255)
    private String emailName;
    @Size(max = 150)
    private String RoleName;
    private Integer OfficerStatusCode;
    @Size(max = 100)
    private String OfficerStatusDesc;
    private Double AmtApprovalLimit;
    private LocalDateTime DTKafka;

    public Integer getOfficerId() {
        return officerId;
    }

    public void setOfficerId(Integer officerId) {
        this.officerId = officerId;
    }

    public Double getmProsperaOfficerID() {
        return mProsperaOfficerID;
    }

    public void setmProsperaOfficerID(Double mProsperaOfficerID) {
        this.mProsperaOfficerID = mProsperaOfficerID;
    }

    public Integer getMmsId() {
        return mmsId;
    }

    public void setMmsId(Integer mmsId) {
        this.mmsId = mmsId;
    }

    public Integer getRoleID() {
        return roleID;
    }

    public void setRoleID(Integer roleID) {
        this.roleID = roleID;
    }

    public String getMmsCode() {
        return mmsCode;
    }

    public void setMmsCode(String mmsCode) {
        this.mmsCode = mmsCode;
    }

    public String getOfficerCode() {
        return officerCode;
    }

    public void setOfficerCode(String officerCode) {
        this.officerCode = officerCode;
    }

    public Date getDtPopulate() {
        return dtPopulate;
    }

    public void setDtPopulate(Date dtPopulate) {
        this.dtPopulate = dtPopulate;
    }

    public String getSysPopulate() {
        return SysPopulate;
    }

    public void setSysPopulate(String sysPopulate) {
        SysPopulate = sysPopulate;
    }

    public String getOfficerName() {
        return OfficerName;
    }

    public void setOfficerName(String officerName) {
        OfficerName = officerName;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getEmailName() {
        return emailName;
    }

    public void setEmailName(String emailName) {
        this.emailName = emailName;
    }

    public String getRoleName() {
        return RoleName;
    }

    public void setRoleName(String roleName) {
        RoleName = roleName;
    }

    public Integer getOfficerStatusCode() {
        return OfficerStatusCode;
    }

    public void setOfficerStatusCode(Integer officerStatusCode) {
        OfficerStatusCode = officerStatusCode;
    }

    public String getOfficerStatusDesc() {
        return OfficerStatusDesc;
    }

    public void setOfficerStatusDesc(String officerStatusDesc) {
        OfficerStatusDesc = officerStatusDesc;
    }

    public Double getAmtApprovalLimit() {
        return AmtApprovalLimit;
    }

    public void setAmtApprovalLimit(Double amtApprovalLimit) {
        AmtApprovalLimit = amtApprovalLimit;
    }

    public LocalDateTime getDTKafka() {
        return DTKafka;
    }

    public void setDTKafka(LocalDateTime DTKafka) {
        this.DTKafka = DTKafka;
    }
}
