package com.btpns.fin.model.entity;

import javax.persistence.*;
import javax.validation.constraints.Size;

@Entity
public class TrxHTAmountDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Size(max=20)
    private String transactionId;
    private Integer c50Count;
    private Double c50Amount;
    private Integer c100Count;
    private Double c100Amount;
    private Integer c200Count;
    private Double c200Amount;
    private Integer c500Count;
    private Double c500Amount;
    private Integer c1KCount;
    private Double c1KAmount;
    private Integer p1KCount;
    private Double p1KAmount;
    private Integer p2KCount;
    private Double p2KAmount;
    private Integer p5KCount;
    private Double p5KAmount;
    private Integer p10KCount;
    private Double p10KAmount;
    private Integer p20KCount;
    private Double p20KAmount;
    private Integer p50KCount;
    private Double p50KAmount;
    private Integer p75KCount;
    private Double p75KAmount;
    private Integer p100KCount;
    private Double p100KAmount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Integer getC50Count() {
        return c50Count;
    }

    public void setC50Count(Integer c50Count) {
        this.c50Count = c50Count;
    }

    public Double getC50Amount() {
        return c50Amount;
    }

    public void setC50Amount(Double c50Amount) {
        this.c50Amount = c50Amount;
    }

    public Integer getC100Count() {
        return c100Count;
    }

    public void setC100Count(Integer c100Count) {
        this.c100Count = c100Count;
    }

    public Double getC100Amount() {
        return c100Amount;
    }

    public void setC100Amount(Double c100Amount) {
        this.c100Amount = c100Amount;
    }

    public Integer getC200Count() {
        return c200Count;
    }

    public void setC200Count(Integer c200Count) {
        this.c200Count = c200Count;
    }

    public Double getC200Amount() {
        return c200Amount;
    }

    public void setC200Amount(Double c200Amount) {
        this.c200Amount = c200Amount;
    }

    public Integer getC500Count() {
        return c500Count;
    }

    public void setC500Count(Integer c500Count) {
        this.c500Count = c500Count;
    }

    public Double getC500Amount() {
        return c500Amount;
    }

    public void setC500Amount(Double c500Amount) {
        this.c500Amount = c500Amount;
    }

    public Integer getC1KCount() {
        return c1KCount;
    }

    public void setC1KCount(Integer c1KCount) {
        this.c1KCount = c1KCount;
    }

    public Double getC1KAmount() {
        return c1KAmount;
    }

    public void setC1KAmount(Double c1KAmount) {
        this.c1KAmount = c1KAmount;
    }

    public Integer getP1KCount() {
        return p1KCount;
    }

    public void setP1KCount(Integer p1KCount) {
        this.p1KCount = p1KCount;
    }

    public Double getP1KAmount() {
        return p1KAmount;
    }

    public void setP1KAmount(Double p1KAmount) {
        this.p1KAmount = p1KAmount;
    }

    public Integer getP2KCount() {
        return p2KCount;
    }

    public void setP2KCount(Integer p2KCount) {
        this.p2KCount = p2KCount;
    }

    public Double getP2KAmount() {
        return p2KAmount;
    }

    public void setP2KAmount(Double p2KAmount) {
        this.p2KAmount = p2KAmount;
    }

    public Integer getP5KCount() {
        return p5KCount;
    }

    public void setP5KCount(Integer p5KCount) {
        this.p5KCount = p5KCount;
    }

    public Double getP5KAmount() {
        return p5KAmount;
    }

    public void setP5KAmount(Double p5KAmount) {
        this.p5KAmount = p5KAmount;
    }

    public Integer getP10KCount() {
        return p10KCount;
    }

    public void setP10KCount(Integer p10KCount) {
        this.p10KCount = p10KCount;
    }

    public Double getP10KAmount() {
        return p10KAmount;
    }

    public void setP10KAmount(Double p10KAmount) {
        this.p10KAmount = p10KAmount;
    }

    public Integer getP20KCount() {
        return p20KCount;
    }

    public void setP20KCount(Integer p20KCount) {
        this.p20KCount = p20KCount;
    }

    public Double getP20KAmount() {
        return p20KAmount;
    }

    public void setP20KAmount(Double p20KAmount) {
        this.p20KAmount = p20KAmount;
    }

    public Integer getP50KCount() {
        return p50KCount;
    }

    public void setP50KCount(Integer p50KCount) {
        this.p50KCount = p50KCount;
    }

    public Double getP50KAmount() {
        return p50KAmount;
    }

    public void setP50KAmount(Double p50KAmount) {
        this.p50KAmount = p50KAmount;
    }

    public Integer getP75KCount() {
        return p75KCount;
    }

    public void setP75KCount(Integer p75KCount) {
        this.p75KCount = p75KCount;
    }

    public Double getP75KAmount() {
        return p75KAmount;
    }

    public void setP75KAmount(Double p75KAmount) {
        this.p75KAmount = p75KAmount;
    }

    public Integer getP100KCount() {
        return p100KCount;
    }

    public void setP100KCount(Integer p100KCount) {
        this.p100KCount = p100KCount;
    }

    public Double getP100KAmount() {
        return p100KAmount;
    }

    public void setP100KAmount(Double p100KAmount) {
        this.p100KAmount = p100KAmount;
    }

    public void setAmount(TrxHTAmountDetail detail){
        setHTAmountDetail(detail.getC50Count(), detail.getC50Amount(), detail.getC100Count(), detail.getC100Amount(), detail.getC200Count(), detail.getC200Amount(), detail.getC500Count(), detail.getC500Amount(), detail.getC1KCount(), detail.getC1KAmount(), detail.getP1KCount(), detail.getP1KAmount(), detail.getP2KCount(), detail.getP2KAmount(), detail.getP5KCount(), detail.getP5KAmount(), detail.getP10KCount(), detail.getP10KAmount(), detail.getP20KCount(), detail.getP20KAmount(), detail.getP50KCount(), detail.getP50KAmount(), detail.getP75KCount(), detail.getP75KAmount(), detail.getP100KCount(), detail.getP100KAmount());
    }

    public void setAmount(HeadTellerBalance detail){
        setHTAmountDetail(detail.getC50Count(), detail.getC50Amount(), detail.getC100Count(), detail.getC100Amount(), detail.getC200Count(), detail.getC200Amount(), detail.getC500Count(), detail.getC500Amount(), detail.getC1KCount(), detail.getC1KAmount(), detail.getP1KCount(), detail.getP1KAmount(), detail.getP2KCount(), detail.getP2KAmount(), detail.getP5KCount(), detail.getP5KAmount(), detail.getP10KCount(), detail.getP10KAmount(), detail.getP20KCount(), detail.getP20KAmount(), detail.getP50KCount(), detail.getP50KAmount(), detail.getP75KCount(), detail.getP75KAmount(), detail.getP100KCount(), detail.getP100KAmount());
    }
    public Double getTotal(){
        return c50Amount + c100Amount + c200Amount + c500Amount + c1KAmount + p1KAmount + p2KAmount + p5KAmount
                + p10KAmount + p20KAmount + p50KAmount + p75KAmount + p100KAmount;
    }

    public void setAmount(TrxAmountDetail detail){
        setHTAmountDetail(detail.getC50Count(), detail.getC50Amount(), detail.getC100Count(), detail.getC100Amount(), detail.getC200Count(), detail.getC200Amount(), detail.getC500Count(), detail.getC500Amount(), detail.getC1KCount(), detail.getC1KAmount(), detail.getP1KCount(), detail.getP1KAmount(), detail.getP2KCount(), detail.getP2KAmount(), detail.getP5KCount(), detail.getP5KAmount(), detail.getP10KCount(), detail.getP10KAmount(), detail.getP20KCount(), detail.getP20KAmount(), detail.getP50KCount(), detail.getP50KAmount(), detail.getP75KCount(), detail.getP75KAmount(), detail.getP100KCount(), detail.getP100KAmount());
    }

    private void setHTAmountDetail(Integer c50Count, Double c50Amount, Integer c100Count, Double c100Amount, Integer c200Count, Double c200Amount, Integer c500Count, Double c500Amount, Integer c1KCount, Double c1KAmount, Integer p1KCount, Double p1KAmount, Integer p2KCount, Double p2KAmount, Integer p5KCount, Double p5KAmount, Integer p10KCount, Double p10KAmount, Integer p20KCount, Double p20KAmount, Integer p50KCount, Double p50KAmount, Integer p75KCount, Double p75KAmount, Integer p100KCount, Double p100KAmount) {
        setC50Count(c50Count);
        setC50Amount(c50Amount);
        setC100Count(c100Count);
        setC100Amount(c100Amount);
        setC200Count(c200Count);
        setC200Amount(c200Amount);
        setC500Count(c500Count);
        setC500Amount(c500Amount);
        setC1KCount(c1KCount);
        setC1KAmount(c1KAmount);
        setP1KCount(p1KCount);
        setP1KAmount(p1KAmount);
        setP2KCount(p2KCount);
        setP2KAmount(p2KAmount);
        setP5KCount(p5KCount);
        setP5KAmount(p5KAmount);
        setP10KCount(p10KCount);
        setP10KAmount(p10KAmount);
        setP20KCount(p20KCount);
        setP20KAmount(p20KAmount);
        setP50KCount(p50KCount);
        setP50KAmount(p50KAmount);
        setP75KCount(p75KCount);
        setP75KAmount(p75KAmount);
        setP100KCount(p100KCount);
        setP100KAmount(p100KAmount);
    }

}
