package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
public class TrxTellerExchange {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Size(max=40)
    private String requestId;
    @Size(max=25)
    private String transactionId;
    @Size(max = 10)
    private String branchId;
    private LocalDate period;
    private String type;
    private String fromName;
    private String toName;
    private boolean requestFlag;
    private boolean depositFlag;
    private String status;
    private String inputerNik;
    private String verificationNik;
    private Double totalAmount;
    private String totalAmountSpelled;
    private LocalDateTime createDateTime;
    private LocalDateTime updateDateTime;
    private String amountDetail;
    private String sourceOfFund;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public LocalDate getPeriod() {
        return period;
    }

    public void setPeriod(LocalDate period) {
        this.period = period;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getToName() {
        return toName;
    }

    public void setToName(String toName) {
        this.toName = toName;
    }

    public boolean isRequestFlag() {
        return requestFlag;
    }

    public void setRequestFlag(boolean requestFlag) {
        this.requestFlag = requestFlag;
    }

    public boolean isDepositFlag() {
        return depositFlag;
    }

    public void setDepositFlag(boolean depositFlag) {
        this.depositFlag = depositFlag;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getInputerNik() {
        return inputerNik;
    }

    public void setInputerNik(String inputerNik) {
        this.inputerNik = inputerNik;
    }

    public String getVerificationNik() {
        return verificationNik;
    }

    public void setVerificationNik(String verificationNik) {
        this.verificationNik = verificationNik;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTotalAmountSpelled() {
        return totalAmountSpelled;
    }

    public void setTotalAmountSpelled(String totalAmountSpelled) {
        this.totalAmountSpelled = totalAmountSpelled;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public LocalDateTime getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(LocalDateTime updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public String getAmountDetail() {
        return amountDetail;
    }

    public void setAmountDetail(String amountDetail) {
        this.amountDetail = amountDetail;
    }

    public String getSourceOfFund() {
        return sourceOfFund;
    }

    public void setSourceOfFund(String sourceOfFund) {
        this.sourceOfFund = sourceOfFund;
    }
}
