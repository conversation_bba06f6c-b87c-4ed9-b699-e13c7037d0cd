package com.btpns.fin.model.entity;

import javax.persistence.*;
import java.time.LocalDate;

@Entity
@Table(name = "MSALTERNATEROLE")
public class AlternateRole {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String nik;
    private String name;
    private String alternateRoleId;
    private String alternateRoleName;
    private String branchId;
    private LocalDate startPeriod;
    private LocalDate endPeriod;
    private String info;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getAlternateRoleId() {
        return alternateRoleId;
    }

    public void setAlternateRoleId(String alternateRoleId) {
        this.alternateRoleId = alternateRoleId;
    }

    public String getAlternateRoleName() {
        return alternateRoleName;
    }

    public void setAlternateRoleName(String alternateRoleName) {
        this.alternateRoleName = alternateRoleName;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public LocalDate getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(LocalDate startPeriod) {
        this.startPeriod = startPeriod;
    }

    public LocalDate getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(LocalDate endPeriod) {
        this.endPeriod = endPeriod;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
