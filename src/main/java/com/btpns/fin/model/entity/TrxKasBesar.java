package com.btpns.fin.model.entity;


import javax.persistence.*;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

@Entity

public class TrxKasBesar {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String requestId;
    @Size(max = 20)
    private String transactionId;
    @Size(max = 10)
    private String branchId;
    private LocalDate period;
    @Size(max = 20)
    private String trxType;
    @Size(max = 15)
    private String status;
    @Size(max = 25)
    private String inputerNik;
    private String inputerName;
    @Size(max = 25)
    private String checkerNik;
    private String checkerName;

    private String reason;
    private Double totalAmount;
    private Double balanceAmount;
    private String additionalInfo;
    private LocalDateTime createDateTime;
    private LocalDateTime updateDateTime;

    private String additionalData;

    public TrxKasBesar() {
    }
    
    public TrxKasBesar(TrxKasBesar trxKasBesar) {
        this.requestId = trxKasBesar.requestId;
        this.transactionId = trxKasBesar.transactionId;
        this.branchId = trxKasBesar.branchId;
        this.period = trxKasBesar.period;
        this.trxType = trxKasBesar.trxType;
        this.status = trxKasBesar.status;
        this.inputerNik = trxKasBesar.inputerNik;
        this.inputerName = trxKasBesar.inputerName;
        this.checkerNik = trxKasBesar.checkerNik;
        this.checkerName = trxKasBesar.checkerName;
        this.reason = trxKasBesar.reason;
        this.totalAmount = trxKasBesar.totalAmount;
        this.balanceAmount = trxKasBesar.balanceAmount;
        this.additionalInfo = trxKasBesar.additionalInfo;
        this.createDateTime = LocalDateTime.from(OffsetDateTime.now());
        this.updateDateTime = LocalDateTime.from(OffsetDateTime.now());
        this.additionalData = trxKasBesar.additionalData;
        this.inputerName = trxKasBesar.inputerName;
        this.checkerName = trxKasBesar.checkerName;
    }

    public Long getId() {
        return id;
    }

    public TrxKasBesar setId(Long id) {
        this.id = id;
        return this;
    }

    public String getRequestId() {
        return requestId;
    }

    public TrxKasBesar setRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }


    public String getTransactionId() {
        return transactionId;
    }

    public TrxKasBesar setTransactionId(String transactionId) {
        this.transactionId = transactionId;
        return this;
    }

    public String getBranchId() {
        return branchId;
    }

    public TrxKasBesar setBranchId(String branchId) {
        this.branchId = branchId;
        return this;
    }

    public LocalDate getPeriod() {
        return period;
    }

    public TrxKasBesar setPeriod(LocalDate period) {
        this.period = period;
        return this;
    }

    public String getTrxType() {
        return trxType;
    }

    public TrxKasBesar setTrxType(String trxType) {
        this.trxType = trxType;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public TrxKasBesar setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getInputer() {
        return inputerNik;
    }

    public TrxKasBesar setInputer(String inputerNik) {
        this.inputerNik = inputerNik;
        return this;
    }

    public String getInputerName() {
        return inputerName;
    }

    public void setInputerName(String inputerName) {
        this.inputerName = inputerName;
    }

    public String getChecker() {
        return checkerNik;
    }

    public TrxKasBesar setChecker(String checkerNik) {
        this.checkerNik = checkerNik;
        return this;
    }

    public String getCheckerName() {
        return checkerName;
    }

    public void setCheckerName(String checkerName) {
        this.checkerName = checkerName;
    }

    public String getReason() {
        return reason;
    }

    public TrxKasBesar setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public TrxKasBesar setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
        return this;
    }

    public Double getBalanceAmount() {
        return balanceAmount;
    }

    public TrxKasBesar setBalanceAmount(Double balanceAmount) {
        this.balanceAmount = balanceAmount;
        return this;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public TrxKasBesar setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
        return this;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public TrxKasBesar setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
        return this;
    }

    public LocalDateTime getUpdateDateTime() {
        return updateDateTime;
    }

    public TrxKasBesar setUpdateDateTime(LocalDateTime updateDateTime) {
        this.updateDateTime = updateDateTime;
        return this;
    }

    public String getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(String additionalData) {
        this.additionalData = additionalData;
    }

    public void setKasBesar(String transactionId, LocalDate period, String branch, String status, String trxType, String inputer, String checker,
                            Double balanceAmount, Double totalAmount, LocalDateTime createdDateTime, LocalDateTime updatedDateTime, String requestId, String inputerName, String checkerName){
        setTransactionId(transactionId);
        setPeriod(period);
        setBranchId(branch);
        setStatus(status);
        setTrxType(trxType);
        setInputer(inputer);
        setChecker(checker);
        setBalanceAmount(balanceAmount);
        setTotalAmount(totalAmount);
        setCreateDateTime(createdDateTime);
        setUpdateDateTime(updatedDateTime);
        setRequestId(requestId);
        setInputerName(inputerName);
        setCheckerName(checkerName);
    }

}
