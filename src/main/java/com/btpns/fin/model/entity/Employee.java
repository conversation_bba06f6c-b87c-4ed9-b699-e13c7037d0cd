package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Entity
@Table(name = "MSEMPLOYEE")
public class Employee {
    @Id
    private String nik;
    private String identityNum;
    private String fullName;
    private String occupation;
    private String occupationDesc;
    private String statusEmployeeDesc;
    private String branchCode;
    private String costCenterCode;
    private String directSupervisorNIK;
    private String directSupervisorName;
    private String fieldChecksum;
    private String srcSystem;
    private LocalDateTime dTPopulate;
    private String sysPopulate;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getIdentityNum() {
        return identityNum;
    }

    public void setIdentityNum(String identityNum) {
        this.identityNum = identityNum;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getOccupationDesc() {
        return occupationDesc;
    }

    public void setOccupationDesc(String occupationDesc) {
        this.occupationDesc = occupationDesc;
    }

    public String getStatusEmployeeDesc() {
        return statusEmployeeDesc;
    }

    public void setStatusEmployeeDesc(String statusEmployeeDesc) {
        this.statusEmployeeDesc = statusEmployeeDesc;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getCostCenterCode() {
        return costCenterCode;
    }

    public void setCostCenterCode(String costCenterCode) {
        this.costCenterCode = costCenterCode;
    }

    public String getDirectSupervisorNIK() {
        return directSupervisorNIK;
    }

    public void setDirectSupervisorNIK(String directSupervisorNIK) {
        this.directSupervisorNIK = directSupervisorNIK;
    }

    public String getDirectSupervisorName() {
        return directSupervisorName;
    }

    public void setDirectSupervisorName(String directSupervisorName) {
        this.directSupervisorName = directSupervisorName;
    }

    public String getFieldChecksum() {
        return fieldChecksum;
    }

    public void setFieldChecksum(String fieldChecksum) {
        this.fieldChecksum = fieldChecksum;
    }

    public String getSrcSystem() {
        return srcSystem;
    }

    public void setSrcSystem(String srcSystem) {
        this.srcSystem = srcSystem;
    }

    public LocalDateTime getdTPopulate() {
        return dTPopulate;
    }

    public void setdTPopulate(LocalDateTime dTPopulate) {
        this.dTPopulate = dTPopulate;
    }

    public String getSysPopulate() {
        return sysPopulate;
    }

    public void setSysPopulate(String sysPopulate) {
        this.sysPopulate = sysPopulate;
    }
}
