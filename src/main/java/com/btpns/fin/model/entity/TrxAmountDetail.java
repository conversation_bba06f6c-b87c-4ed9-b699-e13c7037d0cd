package com.btpns.fin.model.entity;

import com.btpns.fin.model.AmountDetail;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.List;

import static com.btpns.fin.constant.CommonConstant.*;

@Entity
public class TrxAmountDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Size(max=20)
    private String transactionId;
    private Integer c50Count = 0;
    private Double c50Amount = 0.0;
    private Integer c100Count = 0;
    private Double c100Amount = 0.0;
    private Integer c200Count = 0;
    private Double c200Amount = 0.0;
    private Integer c500Count = 0;
    private Double c500Amount  = 0.0;
    private Integer c1KCount = 0;
    private Double c1KAmount  = 0.0;
    private Integer p1KCount = 0;
    private Double p1KAmount  = 0.0;
    private Integer p2KCount = 0;
    private Double p2KAmount  = 0.0;
    private Integer p5KCount = 0;
    private Double p5KAmount  = 0.0;
    private Integer p10KCount = 0;
    private Double p10KAmount  = 0.0;
    private Integer p20KCount = 0;
    private Double p20KAmount  = 0.0;
    private Integer p50KCount = 0;
    private Double p50KAmount  = 0.0;
    private Integer p75KCount = 0;
    private Double p75KAmount  = 0.0;
    private Integer p100KCount = 0;
    private Double p100KAmount  = 0.0;

    public TrxAmountDetail() {
    }
    
    public TrxAmountDetail(TrxAmountDetail trxAmountDetail) {
        this.transactionId = trxAmountDetail.getTransactionId();
        this.c50Count = trxAmountDetail.getC50Count();
        this.c50Amount = trxAmountDetail.getC50Amount();
        this.c100Count = trxAmountDetail.getC100Count();
        this.c100Amount = trxAmountDetail.getC100Amount();
        this.c200Count = trxAmountDetail.getC200Count();
        this.c200Amount = trxAmountDetail.getC200Amount();
        this.c500Count = trxAmountDetail.getC500Count();
        this.c500Amount = trxAmountDetail.getC500Amount();
        this.c1KCount = trxAmountDetail.getC1KCount();
        this.c1KAmount = trxAmountDetail.getC1KAmount();
        this.p1KCount = trxAmountDetail.getP1KCount();
        this.p1KAmount = trxAmountDetail.getP1KAmount();
        this.p2KCount = trxAmountDetail.getP2KCount();
        this.p2KAmount = trxAmountDetail.getP2KAmount();
        this.p5KCount = trxAmountDetail.getP5KCount();
        this.p5KAmount = trxAmountDetail.getP5KAmount();
        this.p10KCount = trxAmountDetail.getP10KCount();
        this.p10KAmount = trxAmountDetail.getP10KAmount();
        this.p20KCount = trxAmountDetail.getP20KCount();
        this.p20KAmount = trxAmountDetail.getP20KAmount();
        this.p50KCount = trxAmountDetail.getP50KCount();
        this.p50KAmount = trxAmountDetail.getP50KAmount();
        this.p75KCount = trxAmountDetail.getP75KCount();
        this.p75KAmount = trxAmountDetail.getP75KAmount();
        this.p100KCount = trxAmountDetail.getP100KCount();
        this.p100KAmount = trxAmountDetail.getP100KAmount();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public TrxAmountDetail setTransactionId(String transactionId) {
        this.transactionId = transactionId;
        return this;
    }

    public Integer getC50Count() {
        return c50Count;
    }

    public TrxAmountDetail setC50Count(Integer c50Count) {
        this.c50Count = c50Count;
        return this;
    }

    public Double getC50Amount() {
        return c50Amount;
    }

    public TrxAmountDetail setC50Amount(Double c50Amount) {
        this.c50Amount = c50Amount;
        return this;
    }

    public Integer getC100Count() {
        return c100Count;
    }

    public TrxAmountDetail setC100Count(Integer c100Count) {
        this.c100Count = c100Count;
        return this;
    }

    public Double getC100Amount() {
        return c100Amount;
    }

    public TrxAmountDetail setC100Amount(Double c100Amount) {
        this.c100Amount = c100Amount;
        return this;
    }

    public Integer getC200Count() {
        return c200Count;
    }

    public TrxAmountDetail setC200Count(Integer c200Count) {
        this.c200Count = c200Count;
        return this;
    }

    public Double getC200Amount() {
        return c200Amount;
    }

    public TrxAmountDetail setC200Amount(Double c200Amount) {
        this.c200Amount = c200Amount;
        return this;
    }

    public Integer getC500Count() {
        return c500Count;
    }

    public TrxAmountDetail setC500Count(Integer c500Count) {
        this.c500Count = c500Count;
        return this;
    }

    public Double getC500Amount() {
        return c500Amount;
    }

    public TrxAmountDetail setC500Amount(Double c500Amount) {
        this.c500Amount = c500Amount;
        return this;
    }

    public Integer getC1KCount() {
        return c1KCount;
    }

    public TrxAmountDetail setC1KCount(Integer c1KCount) {
        this.c1KCount = c1KCount;
        return this;
    }

    public Double getC1KAmount() {
        return c1KAmount;
    }

    public TrxAmountDetail setC1KAmount(Double c1KAmount) {
        this.c1KAmount = c1KAmount;
        return this;
    }

    public Integer getP1KCount() {
        return p1KCount;
    }

    public TrxAmountDetail setP1KCount(Integer p1KCount) {
        this.p1KCount = p1KCount;
        return this;
    }

    public Double getP1KAmount() {
        return p1KAmount;
    }

    public TrxAmountDetail setP1KAmount(Double p1KAmount) {
        this.p1KAmount = p1KAmount;
        return this;
    }

    public Integer getP2KCount() {
        return p2KCount;
    }

    public TrxAmountDetail setP2KCount(Integer p2KCount) {
        this.p2KCount = p2KCount;
        return this;
    }

    public Double getP2KAmount() {
        return p2KAmount;
    }

    public TrxAmountDetail setP2KAmount(Double p2KAmount) {
        this.p2KAmount = p2KAmount;
        return this;
    }

    public Integer getP5KCount() {
        return p5KCount;
    }

    public TrxAmountDetail setP5KCount(Integer p5KCount) {
        this.p5KCount = p5KCount;
        return this;
    }

    public Double getP5KAmount() {
        return p5KAmount;
    }

    public TrxAmountDetail setP5KAmount(Double p5KAmount) {
        this.p5KAmount = p5KAmount;
        return this;
    }

    public Integer getP10KCount() {
        return p10KCount;
    }

    public TrxAmountDetail setP10KCount(Integer p10KCount) {
        this.p10KCount = p10KCount;
        return this;
    }

    public Double getP10KAmount() {
        return p10KAmount;
    }

    public TrxAmountDetail setP10KAmount(Double p10KAmount) {
        this.p10KAmount = p10KAmount;
        return this;
    }

    public Integer getP20KCount() {
        return p20KCount;
    }

    public TrxAmountDetail setP20KCount(Integer p20KCount) {
        this.p20KCount = p20KCount;
        return this;
    }

    public Double getP20KAmount() {
        return p20KAmount;
    }

    public TrxAmountDetail setP20KAmount(Double p20KAmount) {
        this.p20KAmount = p20KAmount;
        return this;
    }

    public Integer getP50KCount() {
        return p50KCount;
    }

    public TrxAmountDetail setP50KCount(Integer p50KCount) {
        this.p50KCount = p50KCount;
        return this;
    }

    public Double getP50KAmount() {
        return p50KAmount;
    }

    public TrxAmountDetail setP50KAmount(Double p50KAmount) {
        this.p50KAmount = p50KAmount;
        return this;
    }

    public Integer getP75KCount() {
        return p75KCount;
    }

    public TrxAmountDetail setP75KCount(Integer p75KCount) {
        this.p75KCount = p75KCount;
        return this;
    }

    public Double getP75KAmount() {
        return p75KAmount;
    }

    public TrxAmountDetail setP75KAmount(Double p75KAmount) {
        this.p75KAmount = p75KAmount;
        return this;
    }

    public Integer getP100KCount() {
        return p100KCount;
    }

    public TrxAmountDetail setP100KCount(Integer p100KCount) {
        this.p100KCount = p100KCount;
        return this;
    }

    public Double getP100KAmount() {
        return p100KAmount;
    }

    public TrxAmountDetail setP100KAmount(Double p100KAmount) {
        this.p100KAmount = p100KAmount;
        return this;
    }
    public Double getTotal(){
        return c50Amount + c100Amount + c200Amount + c500Amount + c1KAmount + p1KAmount + p2KAmount + p5KAmount
                + p10KAmount + p20KAmount + p50KAmount + p75KAmount + p100KAmount;
    }

    public void setAmountDetail(String transactionId, BranchBalance branchBalance){
        setTransactionId(transactionId);
        setC50Amount(branchBalance.getC50Amount());
        setC50Count(branchBalance.getC50Count());
        setC100Amount(branchBalance.getC100Amount());
        setC100Count(branchBalance.getC100Count());
        setC200Amount(branchBalance.getC200Amount());
        setC200Count(branchBalance.getC200Count());
        setC500Amount(branchBalance.getC500Amount());
        setC500Count(branchBalance.getC500Count());
        setC1KAmount(branchBalance.getC1KAmount());
        setC1KCount(branchBalance.getC1KCount());
        setP1KAmount(branchBalance.getP1KAmount());
        setP1KCount(branchBalance.getP1KCount());
        setP2KAmount(branchBalance.getP2KAmount());
        setP2KCount(branchBalance.getP2KCount());
        setP5KAmount(branchBalance.getP5KAmount());
        setP5KCount(branchBalance.getP5KCount());
        setP10KAmount(branchBalance.getP10KAmount());
        setP10KCount(branchBalance.getP10KCount());
        setP20KAmount(branchBalance.getP20KAmount());
        setP20KCount(branchBalance.getP20KCount());
        setP50KAmount(branchBalance.getP50KAmount());
        setP50KCount(branchBalance.getP50KCount());
        setP75KAmount(branchBalance.getP75KAmount());
        setP75KCount(branchBalance.getP75KCount());
        setP100KAmount(branchBalance.getP100KAmount());
        setP100KCount(branchBalance.getP100KCount());
    }

    public void setAmountDetail(List<AmountDetail> amountDetails,String transactionId){
        setTransactionId(transactionId);

        for (AmountDetail amountDetail : amountDetails) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    setC50Amount(amountDetail.getTotal());
                    setC50Count(amountDetail.getCount());
                    break;
                case COIN_100:
                    setC100Amount(amountDetail.getTotal());
                    setC100Count(amountDetail.getCount());
                    break;
                case COIN_200:
                    setC200Amount(amountDetail.getTotal());
                    setC200Count(amountDetail.getCount());
                    break;
                case COIN_500:
                    setC500Amount(amountDetail.getTotal());
                    setC500Count(amountDetail.getCount());
                    break;
                case COIN_1K:
                    setC1KAmount(amountDetail.getTotal());
                    setC1KCount(amountDetail.getCount());
                    break;
                case PAPER_1K:
                    setP1KAmount(amountDetail.getTotal());
                    setP1KCount(amountDetail.getCount());
                    break;
                case PAPER_2K:
                    setP2KAmount(amountDetail.getTotal());
                    setP2KCount(amountDetail.getCount());
                    break;
                case PAPER_5K:
                    setP5KAmount(amountDetail.getTotal());
                    setP5KCount(amountDetail.getCount());
                    break;
                case PAPER_10K:
                    setP10KAmount(amountDetail.getTotal());
                    setP10KCount(amountDetail.getCount());
                    break;
                case PAPER_20K:
                    setP20KAmount(amountDetail.getTotal());
                    setP20KCount(amountDetail.getCount());
                    break;
                case PAPER_50K:
                    setP50KAmount(amountDetail.getTotal());
                    setP50KCount(amountDetail.getCount());
                    break;
                case PAPER_75K:
                    setP75KAmount(amountDetail.getTotal());
                    setP75KCount(amountDetail.getCount());
                    break;
                case PAPER_100K:
                    setP100KAmount(amountDetail.getTotal());
                    setP100KCount(amountDetail.getCount());
                    break;
            }
        }
    }
}
