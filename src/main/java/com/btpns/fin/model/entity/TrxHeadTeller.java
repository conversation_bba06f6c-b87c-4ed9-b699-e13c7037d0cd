package com.btpns.fin.model.entity;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

@Entity
@Table(name = "TrxHeadTeller")
public class TrxHeadTeller {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Size(max=20)
    private String transactionId;
    @Size(max = 10)
    private String branchId;
    private LocalDate period;
    private String type;
    @Size(max = 15)
    private String status;
    @Size(max = 25)
    private String inputer;
    private String tellerId;

    private String reason;

    private Double totalAmount;
    private Double balance;
    private LocalDateTime createDateTime;
    private LocalDateTime updateDateTime;

    private String refId;
    private String requestId;
    private String inputerName;
    private String tellerName;


    public TrxHeadTeller() {
    }

    public TrxHeadTeller(TrxHeadTeller trxHeadTeller) {
        this.transactionId = trxHeadTeller.transactionId;
        this.branchId = trxHeadTeller.branchId;
        this.period = trxHeadTeller.period;
        this.type = trxHeadTeller.type;
        this.status = trxHeadTeller.status;
        this.inputer = trxHeadTeller.inputer;
        this.tellerId = trxHeadTeller.tellerId;
        this.reason = trxHeadTeller.reason;
        this.totalAmount = trxHeadTeller.totalAmount;
        this.balance = trxHeadTeller.balance;
        this.createDateTime = LocalDateTime.from(OffsetDateTime.now());
        this.updateDateTime = LocalDateTime.from(OffsetDateTime.now());
        this.refId = trxHeadTeller.refId;
        this.inputerName = trxHeadTeller.inputerName;
        this.tellerName = trxHeadTeller.tellerName;
    }
    
    public Long getId() {
        return id;
    }

    public TrxHeadTeller setId(Long id) {
        this.id = id;
        return this;
    }
    public String getTransactionId() {
        return transactionId;
    }

    public TrxHeadTeller setTransactionId(String transactionId) {
        this.transactionId = transactionId;
        return this;
    }

    public String getBranchId() {
        return branchId;
    }

    public TrxHeadTeller setBranchId(String branchId) {
        this.branchId = branchId;
        return this;
    }

    public LocalDate getPeriod() {
        return period;
    }

    public TrxHeadTeller setPeriod(LocalDate period) {
        this.period = period;
        return this;
    }

    public String getType() {
        return type;
    }

    public TrxHeadTeller setType(String type) {
        this.type = type;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public TrxHeadTeller setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getInputer() {
        return inputer;
    }

    public TrxHeadTeller setInputer(String inputer) {
        this.inputer = inputer;
        return this;
    }

    public String getTellerId() {
        return tellerId;
    }

    public TrxHeadTeller setTellerId(String tellerId) {
        this.tellerId = tellerId;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public TrxHeadTeller setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public TrxHeadTeller setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
        return this;
    }

    public Double getBalance() {
        return balance;
    }

    public TrxHeadTeller setBalance(Double balance) {
        this.balance = balance;
        return this;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public TrxHeadTeller setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
        return this;
    }

    public LocalDateTime getUpdateDateTime() {
        return updateDateTime;
    }

    public TrxHeadTeller setUpdateDateTime(LocalDateTime updateDateTime) {
        this.updateDateTime = updateDateTime;
        return this;
    }

    public String getRefId() {
        return refId;
    }

    public TrxHeadTeller setRefId(String refId) {
        this.refId = refId;
        return this;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getInputerName() {
        return inputerName;
    }

    public void setInputerName(String inputerName) {
        this.inputerName = inputerName;
    }

    public String getTellerName() {
        return tellerName;
    }

    public void setTellerName(String tellerName) {
        this.tellerName = tellerName;
    }

    public void setHeadTeller(String transactionId, String branchId, LocalDate period, String trxType, String status, String inputer, String tellerId,
                              Double totalAmount, Double balance, LocalDateTime createdDateTime, LocalDateTime updateDateTime, String refId){
        setTransactionId(transactionId);
        setBranchId(branchId);
        setPeriod(period);
        setType(trxType);
        setStatus(status);
        setInputer(inputer);
        setTellerId(tellerId);
        setTotalAmount(totalAmount);
        setBalance(balance);
        setCreateDateTime(createdDateTime);
        setUpdateDateTime(updateDateTime);
        setRefId(refId);
    }

}
