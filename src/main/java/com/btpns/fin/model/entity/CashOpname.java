package com.btpns.fin.model.entity;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "TrxCashOpname")
public class CashOpname {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String transactionId;
    private String requestId;
    private String branchId;
    private LocalDate period;
    private String status;
    private Double totalBalance;
    private Double oldTotalBalance;
    private Double carryBalance;
    private Double totalPaperBalance;
    private Double totalCoinBalance;
    private String nikTeller ;
    private String statusVerificationTeller;
    private LocalDateTime dateVerificationTeller;
    private String nikBOS ;
    private String statusVerificationBOS;
    private LocalDateTime dateVerificationBOS ;
    private String nikBOM ;
    private String statusVerificationBOM;
    private LocalDateTime dateVerificationBOM;
    private String nikBM ;
    private String statusVerificationBM;
    private LocalDateTime dateVerificationBM;
    private String nikQA ;
    private String statusVerificationQA;
    private LocalDateTime dateVerificationQA;
    private String nikNOM ;
    private String statusVerificationNOM;
    private LocalDateTime dateVerificationNOM;
    private String nikODH ;
    private String statusVerificationODH;
    private LocalDateTime dateVerificationODH;
    private String nikSKAI ;
    private String statusVerificationSKAI;
    private LocalDateTime dateVerificationSKAI;
    private String nikAltTeller;
    private String statusVerificationAltTeller;
    private LocalDateTime dateVerificationAltTeller;
    private String nikQA2 ;
    private String statusVerificationQA2;
    private LocalDateTime dateVerificationQA2;
    private String reason ;
    private String amountDetail ;
    private LocalDateTime createDateTime;
    private LocalDateTime updateDateTime;
    private String nameTeller;
    private String nameBOM;
    private String nameBOS;
    private String nameQA;
    private String nameBM;
    private String nameNOM;
    private String nameODH;
    private String nameSKAI;;
    private String nameAltTeller;
    private String nameQA2;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public LocalDate getPeriod() {
        return period;
    }

    public void setPeriod(LocalDate period) {
        this.period = period;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Double getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(Double totalBalance) {
        this.totalBalance = totalBalance;
    }

    public Double getOldTotalBalance() {
        return oldTotalBalance;
    }

    public void setOldTotalBalance(Double oldTotalBalance) {
        this.oldTotalBalance = oldTotalBalance;
    }

    public Double getCarryBalance() {
        return carryBalance;
    }

    public void setCarryBalance(Double carryBalance) {
        this.carryBalance = carryBalance;
    }

    public Double getTotalPaperBalance() {
        return totalPaperBalance;
    }

    public void setTotalPaperBalance(Double totalPaperBalance) {
        this.totalPaperBalance = totalPaperBalance;
    }

    public Double getTotalCoinBalance() {
        return totalCoinBalance;
    }

    public void setTotalCoinBalance(Double totalCoinBalance) {
        this.totalCoinBalance = totalCoinBalance;
    }

    public String getNikTeller() {
        return nikTeller;
    }

    public void setNikTeller(String nikTeller) {
        this.nikTeller = nikTeller;
    }

    public String getStatusVerificationTeller() {
        return statusVerificationTeller;
    }

    public void setStatusVerificationTeller(String statusVerificationTeller) {
        this.statusVerificationTeller = statusVerificationTeller;
    }

    public LocalDateTime getDateVerificationTeller() {
        return dateVerificationTeller;
    }

    public void setDateVerificationTeller(LocalDateTime dateVerificationTeller) {
        this.dateVerificationTeller = dateVerificationTeller;
    }

    public String getNikBOS() {
        return nikBOS;
    }

    public void setNikBOS(String nikBOS) {
        this.nikBOS = nikBOS;
    }

    public String getStatusVerificationBOS() {
        return statusVerificationBOS;
    }

    public void setStatusVerificationBOS(String statusVerificationBOS) {
        this.statusVerificationBOS = statusVerificationBOS;
    }

    public LocalDateTime getDateVerificationBOS() {
        return dateVerificationBOS;
    }

    public void setDateVerificationBOS(LocalDateTime dateVerificationBOS) {
        this.dateVerificationBOS = dateVerificationBOS;
    }

    public String getNikBOM() {
        return nikBOM;
    }

    public void setNikBOM(String nikBOM) {
        this.nikBOM = nikBOM;
    }

    public String getStatusVerificationBOM() {
        return statusVerificationBOM;
    }

    public void setStatusVerificationBOM(String statusVerificationBOM) {
        this.statusVerificationBOM = statusVerificationBOM;
    }

    public LocalDateTime getDateVerificationBOM() {
        return dateVerificationBOM;
    }

    public void setDateVerificationBOM(LocalDateTime dateVerificationBOM) {
        this.dateVerificationBOM = dateVerificationBOM;
    }

    public String getNikBM() {
        return nikBM;
    }

    public void setNikBM(String nikBM) {
        this.nikBM = nikBM;
    }

    public String getStatusVerificationBM() {
        return statusVerificationBM;
    }

    public void setStatusVerificationBM(String statusVerificationBM) {
        this.statusVerificationBM = statusVerificationBM;
    }

    public LocalDateTime getDateVerificationBM() {
        return dateVerificationBM;
    }

    public void setDateVerificationBM(LocalDateTime dateVerificationBM) {
        this.dateVerificationBM = dateVerificationBM;
    }

    public String getNikQA() {
        return nikQA;
    }

    public void setNikQA(String nikQA) {
        this.nikQA = nikQA;
    }

    public String getStatusVerificationQA() {
        return statusVerificationQA;
    }

    public void setStatusVerificationQA(String statusVerificationQA) {
        this.statusVerificationQA = statusVerificationQA;
    }

    public LocalDateTime getDateVerificationQA() {
        return dateVerificationQA;
    }

    public void setDateVerificationQA(LocalDateTime dateVerificationQA) {
        this.dateVerificationQA = dateVerificationQA;
    }

    public String getNikNOM() {
        return nikNOM;
    }

    public void setNikNOM(String nikNOM) {
        this.nikNOM = nikNOM;
    }

    public String getStatusVerificationNOM() {
        return statusVerificationNOM;
    }

    public void setStatusVerificationNOM(String statusVerificationNOM) {
        this.statusVerificationNOM = statusVerificationNOM;
    }

    public LocalDateTime getDateVerificationNOM() {
        return dateVerificationNOM;
    }

    public void setDateVerificationNOM(LocalDateTime dateVerificationNOM) {
        this.dateVerificationNOM = dateVerificationNOM;
    }

    public String getNikODH() {
        return nikODH;
    }

    public void setNikODH(String nikODH) {
        this.nikODH = nikODH;
    }

    public String getStatusVerificationODH() {
        return statusVerificationODH;
    }

    public void setStatusVerificationODH(String statusVerificationODH) {
        this.statusVerificationODH = statusVerificationODH;
    }

    public LocalDateTime getDateVerificationODH() {
        return dateVerificationODH;
    }

    public void setDateVerificationODH(LocalDateTime dateVerificationODH) {
        this.dateVerificationODH = dateVerificationODH;
    }

    public String getNikAltTeller() {
        return nikAltTeller;
    }

    public void setNikAltTeller(String nikAltTeller) {
        this.nikAltTeller = nikAltTeller;
    }

    public String getStatusVerificationAltTeller() {
        return statusVerificationAltTeller;
    }

    public void setStatusVerificationAltTeller(String statusVerificationAltTeller) {
        this.statusVerificationAltTeller = statusVerificationAltTeller;
    }

    public LocalDateTime getDateVerificationAltTeller() {
        return dateVerificationAltTeller;
    }

    public void setDateVerificationAltTeller(LocalDateTime dateVerificationAltTeller) {
        this.dateVerificationAltTeller = dateVerificationAltTeller;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getAmountDetail() {
        return amountDetail;
    }

    public void setAmountDetail(String amountDetail) {
        this.amountDetail = amountDetail;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public LocalDateTime getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(LocalDateTime updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public String getNameTeller() {
        return nameTeller;
    }

    public void setNameTeller(String nameTeller) {
        this.nameTeller = nameTeller;
    }

    public String getNameBOM() {
        return nameBOM;
    }

    public void setNameBOM(String nameBOM) {
        this.nameBOM = nameBOM;
    }

    public String getNameBOS() {
        return nameBOS;
    }

    public void setNameBOS(String nameBOS) {
        this.nameBOS = nameBOS;
    }

    public String getNameQA() {
        return nameQA;
    }

    public void setNameQA(String nameQA) {
        this.nameQA = nameQA;
    }

    public String getNameBM() {
        return nameBM;
    }

    public void setNameBM(String nameBM) {
        this.nameBM = nameBM;
    }

    public String getNameNOM() {
        return nameNOM;
    }

    public void setNameNOM(String nameNOM) {
        this.nameNOM = nameNOM;
    }

    public String getNameODH() {
        return nameODH;
    }

    public void setNameODH(String nameODH) {
        this.nameODH = nameODH;
    }

    public String getNameAltTeller() {
        return nameAltTeller;
    }

    public void setNameAltTeller(String nameAltTeller) {
        this.nameAltTeller = nameAltTeller;
    }

    public String getNikQA2() {
        return nikQA2;
    }

    public void setNikQA2(String nikQA2) {
        this.nikQA2 = nikQA2;
    }

    public String getStatusVerificationQA2() {
        return statusVerificationQA2;
    }

    public void setStatusVerificationQA2(String statusVerificationQA2) {
        this.statusVerificationQA2 = statusVerificationQA2;
    }

    public LocalDateTime getDateVerificationQA2() {
        return dateVerificationQA2;
    }

    public void setDateVerificationQA2(LocalDateTime dateVerificationQA2) {
        this.dateVerificationQA2 = dateVerificationQA2;
    }

    public String getNameQA2() {
        return nameQA2;
    }

    public void setNameQA2(String nameQA2) {
        this.nameQA2 = nameQA2;
    }

    public String getNikSKAI() {
        return nikSKAI;
    }

    public void setNikSKAI(String nikSKAI) {
        this.nikSKAI = nikSKAI;
    }

    public String getStatusVerificationSKAI() {
        return statusVerificationSKAI;
    }

    public void setStatusVerificationSKAI(String statusVerificationSKAI) {
        this.statusVerificationSKAI = statusVerificationSKAI;
    }

    public LocalDateTime getDateVerificationSKAI() {
        return dateVerificationSKAI;
    }

    public void setDateVerificationSKAI(LocalDateTime dateVerificationSKAI) {
        this.dateVerificationSKAI = dateVerificationSKAI;
    }

    public String getNameSKAI() {
        return nameSKAI;
    }

    public void setNameSKAI(String nameSKAI) {
        this.nameSKAI = nameSKAI;
    }
}
