package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
public class CashOpnameDetail {
    @Id
    private String requestId;
    private String transactionId;
    private String period;
    private String branchId;
    private String branchName;
    private Double totalBalance;
    private Double oldTotalBalance;
    private Double carryBalance;
    private Double totalPaperBalance;
    private Double totalCoinBalance;
    private String nikTeller;
    private String nameTeller;
    private String statusVerificationTeller;
    private LocalDateTime dateVerificationTeller;
    private String nikBOS;
    private String nameBOS;
    private String statusVerificationBOS;
    private LocalDateTime dateVerificationBOS;
    private String nikBOM;
    private String nameBOM;
    private String statusVerificationBOM;
    private LocalDateTime dateVerificationBOM;
    private String nikBM;
    private String nameBM;
    private String statusVerificationBM;
    private LocalDateTime dateVerificationBM;
    private String nikBMBranch;
    private String nameBMBranch;
    private String nikQA;
    private String nameQA;
    private String statusVerificationQA;
    private LocalDateTime dateVerificationQA;
    private String nikNOM;
    private String nameNOM;
    private String statusVerificationNOM;
    private LocalDateTime dateVerificationNOM;
    private String nikODH;
    private String nameODH;
    private String statusVerificationODH;
    private LocalDateTime dateVerificationODH;
    private String nikSKAI;
    private String nameSKAI;
    private String statusVerificationSKAI;
    private LocalDateTime dateVerificationSKAI;
    private String nikAltTeller;
    private String nameAltTeller;
    private String statusVerificationAltTeller;
    private LocalDateTime dateVerificationAltTeller;
    private String nikQA2;
    private String nameQA2;
    private String statusVerificationQA2;
    private LocalDateTime dateVerificationQA2;
    private String status;
    private String reason;
    private String balanceDetails;


    public CashOpnameDetail() {
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public Double getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(Double totalBalance) {
        this.totalBalance = totalBalance;
    }

    public Double getOldTotalBalance() {
        return oldTotalBalance;
    }

    public void setOldTotalBalance(Double oldTotalBalance) {
        this.oldTotalBalance = oldTotalBalance;
    }

    public Double getCarryBalance() {
        return carryBalance;
    }

    public void setCarryBalance(Double carryBalance) {
        this.carryBalance = carryBalance;
    }

    public Double getTotalPaperBalance() {
        return totalPaperBalance;
    }

    public void setTotalPaperBalance(Double totalPaperBalance) {
        this.totalPaperBalance = totalPaperBalance;
    }

    public Double getTotalCoinBalance() {
        return totalCoinBalance;
    }

    public void setTotalCoinBalance(Double totalCoinBalance) {
        this.totalCoinBalance = totalCoinBalance;
    }

    public String getNikTeller() {
        return nikTeller;
    }

    public void setNikTeller(String nikTeller) {
        this.nikTeller = nikTeller;
    }

    public String getNameTeller() {
        return nameTeller;
    }

    public void setNameTeller(String nameTeller) {
        this.nameTeller = nameTeller;
    }

    public String getNikBOS() {
        return nikBOS;
    }

    public void setNikBOS(String nikBOS) {
        this.nikBOS = nikBOS;
    }

    public String getNameBOS() {
        return nameBOS;
    }

    public void setNameBOS(String nameBOS) {
        this.nameBOS = nameBOS;
    }

    public String getNikBOM() {
        return nikBOM;
    }

    public void setNikBOM(String nikBOM) {
        this.nikBOM = nikBOM;
    }

    public String getNameBOM() {
        return nameBOM;
    }

    public void setNameBOM(String nameBOM) {
        this.nameBOM = nameBOM;
    }

    public String getNikBM() {
        return nikBM;
    }

    public void setNikBM(String nikBM) {
        this.nikBM = nikBM;
    }

    public String getNameBM() {
        return nameBM;
    }

    public void setNameBM(String nameBM) {
        this.nameBM = nameBM;
    }

    public String getNikBMBranch() {
        return nikBMBranch;
    }

    public void setNikBMBranch(String nikBMBranch) {
        this.nikBMBranch = nikBMBranch;
    }

    public String getNameBMBranch() {
        return nameBMBranch;
    }

    public void setNameBMBranch(String nameBMBranch) {
        this.nameBMBranch = nameBMBranch;
    }

    public String getNikQA() {
        return nikQA;
    }

    public void setNikQA(String nikQA) {
        this.nikQA = nikQA;
    }

    public String getNameQA() {
        return nameQA;
    }

    public void setNameQA(String nameQA) {
        this.nameQA = nameQA;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getBalanceDetails() {
        return balanceDetails;
    }

    public void setBalanceDetails(String balanceDetails) {
        this.balanceDetails = balanceDetails;
    }

    public String getStatusVerificationTeller() {
        return statusVerificationTeller;
    }

    public void setStatusVerificationTeller(String statusVerificationTeller) {
        this.statusVerificationTeller = statusVerificationTeller;
    }

    public LocalDateTime getDateVerificationTeller() {
        return dateVerificationTeller;
    }

    public void setDateVerificationTeller(LocalDateTime dateVerificationTeller) {
        this.dateVerificationTeller = dateVerificationTeller;
    }

    public String getStatusVerificationBOS() {
        return statusVerificationBOS;
    }

    public void setStatusVerificationBOS(String statusVerificationBOS) {
        this.statusVerificationBOS = statusVerificationBOS;
    }

    public LocalDateTime getDateVerificationBOS() {
        return dateVerificationBOS;
    }

    public void setDateVerificationBOS(LocalDateTime dateVerificationBOS) {
        this.dateVerificationBOS = dateVerificationBOS;
    }

    public String getStatusVerificationBOM() {
        return statusVerificationBOM;
    }

    public void setStatusVerificationBOM(String statusVerificationBOM) {
        this.statusVerificationBOM = statusVerificationBOM;
    }

    public LocalDateTime getDateVerificationBOM() {
        return dateVerificationBOM;
    }

    public void setDateVerificationBOM(LocalDateTime dateVerificationBOM) {
        this.dateVerificationBOM = dateVerificationBOM;
    }

    public String getStatusVerificationBM() {
        return statusVerificationBM;
    }

    public void setStatusVerificationBM(String statusVerificationBM) {
        this.statusVerificationBM = statusVerificationBM;
    }

    public LocalDateTime getDateVerificationBM() {
        return dateVerificationBM;
    }

    public void setDateVerificationBM(LocalDateTime dateVerificationBM) {
        this.dateVerificationBM = dateVerificationBM;
    }

    public String getStatusVerificationQA() {
        return statusVerificationQA;
    }

    public void setStatusVerificationQA(String statusVerificationQA) {
        this.statusVerificationQA = statusVerificationQA;
    }

    public LocalDateTime getDateVerificationQA() {
        return dateVerificationQA;
    }

    public void setDateVerificationQA(LocalDateTime dateVerificationQA) {
        this.dateVerificationQA = dateVerificationQA;
    }

    public String getNikNOM() {
        return nikNOM;
    }

    public void setNikNOM(String nikNOM) {
        this.nikNOM = nikNOM;
    }

    public String getNameNOM() {
        return nameNOM;
    }

    public void setNameNOM(String nameNOM) {
        this.nameNOM = nameNOM;
    }

    public String getStatusVerificationNOM() {
        return statusVerificationNOM;
    }

    public void setStatusVerificationNOM(String statusVerificationNOM) {
        this.statusVerificationNOM = statusVerificationNOM;
    }

    public LocalDateTime getDateVerificationNOM() {
        return dateVerificationNOM;
    }

    public void setDateVerificationNOM(LocalDateTime dateVerificationNOM) {
        this.dateVerificationNOM = dateVerificationNOM;
    }

    public String getNikODH() {
        return nikODH;
    }

    public void setNikODH(String nikODH) {
        this.nikODH = nikODH;
    }

    public String getNameODH() {
        return nameODH;
    }

    public void setNameODH(String nameODH) {
        this.nameODH = nameODH;
    }

    public String getStatusVerificationODH() {
        return statusVerificationODH;
    }

    public void setStatusVerificationODH(String statusVerificationODH) {
        this.statusVerificationODH = statusVerificationODH;
    }

    public LocalDateTime getDateVerificationODH() {
        return dateVerificationODH;
    }

    public void setDateVerificationODH(LocalDateTime dateVerificationODH) {
        this.dateVerificationODH = dateVerificationODH;
    }

    public String getNikAltTeller() {
        return nikAltTeller;
    }

    public void setNikAltTeller(String nikAltTeller) {
        this.nikAltTeller = nikAltTeller;
    }

    public String getNameAltTeller() {
        return nameAltTeller;
    }

    public void setNameAltTeller(String nameAltTeller) {
        this.nameAltTeller = nameAltTeller;
    }

    public String getStatusVerificationAltTeller() {
        return statusVerificationAltTeller;
    }

    public void setStatusVerificationAltTeller(String statusVerificationAltTeller) {
        this.statusVerificationAltTeller = statusVerificationAltTeller;
    }

    public LocalDateTime getDateVerificationAltTeller() {
        return dateVerificationAltTeller;
    }

    public void setDateVerificationAltTeller(LocalDateTime dateVerificationAltTeller) {
        this.dateVerificationAltTeller = dateVerificationAltTeller;
    }

    public String getNikQA2() {
        return nikQA2;
    }

    public void setNikQA2(String nikQA2) {
        this.nikQA2 = nikQA2;
    }

    public String getNameQA2() {
        return nameQA2;
    }

    public void setNameQA2(String nameQA2) {
        this.nameQA2 = nameQA2;
    }

    public String getStatusVerificationQA2() {
        return statusVerificationQA2;
    }

    public void setStatusVerificationQA2(String statusVerificationQA2) {
        this.statusVerificationQA2 = statusVerificationQA2;
    }

    public LocalDateTime getDateVerificationQA2() {
        return dateVerificationQA2;
    }

    public void setDateVerificationQA2(LocalDateTime dateVerificationQA2) {
        this.dateVerificationQA2 = dateVerificationQA2;
    }

    public String getNikSKAI() {
        return nikSKAI;
    }

    public void setNikSKAI(String nikSKAI) {
        this.nikSKAI = nikSKAI;
    }

    public String getNameSKAI() {
        return nameSKAI;
    }

    public void setNameSKAI(String nameSKAI) {
        this.nameSKAI = nameSKAI;
    }

    public String getStatusVerificationSKAI() {
        return statusVerificationSKAI;
    }

    public void setStatusVerificationSKAI(String statusVerificationSKAI) {
        this.statusVerificationSKAI = statusVerificationSKAI;
    }

    public LocalDateTime getDateVerificationSKAI() {
        return dateVerificationSKAI;
    }

    public void setDateVerificationSKAI(LocalDateTime dateVerificationSKAI) {
        this.dateVerificationSKAI = dateVerificationSKAI;
    }
}
