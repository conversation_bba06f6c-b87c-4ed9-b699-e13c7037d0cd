package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "MSOFFICERNR")
public class OfficerNR {
    @Id
    private Integer msOfficerNRID;
    private String officerSource;
    private Integer officerID;
    private String officerCode;
    private String srcSystem;
    private LocalDateTime dtPopulate;
    private String sysPopulate;
    private Double mProsperaOfficerID;
    private Integer mmsId;
    private Integer roleID;
    private String mmsCode;
    private String fieldChecksum;
    private String officerName;
    private String nik;
    private String loginName;
    private String emailName;
    private String roleName;
    private Integer officerStatusCode;
    private String officerStatusDesc;
    private Double amtApprovalLimit;
    private LocalDateTime dtKafka;
    private String mmsName;
    private LocalDate dtCreated;
    private String departmentCode;
    private String firstName;
    private String lastName;
    private LocalDateTime dtLastLogon;
    private Integer accountEnabled;
    private String menuID;
    private LocalDate dtStartProfile;
    private LocalDate dtEndProfile;
    private LocalDate dtValidityPassword;
    private Integer kfoID;
    private String kfoCode;
    private String kfoName;
    private Integer kcsID;
    private String kcsCode;
    private String kcsName;
    private LocalDate dtJoin;
    private String departmentName;


    public Integer getMsOfficerNRID() {
        return msOfficerNRID;
    }

    public void setMsOfficerNRID(Integer msOfficerNRID) {
        this.msOfficerNRID = msOfficerNRID;
    }

    public String getOfficerSource() {
        return officerSource;
    }

    public void setOfficerSource(String officerSource) {
        this.officerSource = officerSource;
    }

    public Integer getOfficerID() {
        return officerID;
    }

    public void setOfficerID(Integer officerID) {
        this.officerID = officerID;
    }

    public String getOfficerCode() {
        return officerCode;
    }

    public void setOfficerCode(String officerCode) {
        this.officerCode = officerCode;
    }

    public String getSrcSystem() {
        return srcSystem;
    }

    public void setSrcSystem(String srcSystem) {
        this.srcSystem = srcSystem;
    }

    public LocalDateTime getDtPopulate() {
        return dtPopulate;
    }

    public void setDtPopulate(LocalDateTime dtPopulate) {
        this.dtPopulate = dtPopulate;
    }

    public String getSysPopulate() {
        return sysPopulate;
    }

    public void setSysPopulate(String sysPopulate) {
        this.sysPopulate = sysPopulate;
    }

    public Double getmProsperaOfficerID() {
        return mProsperaOfficerID;
    }

    public void setmProsperaOfficerID(Double mProsperaOfficerID) {
        this.mProsperaOfficerID = mProsperaOfficerID;
    }

    public Integer getMmsId() {
        return mmsId;
    }

    public void setMmsId(Integer mmsId) {
        this.mmsId = mmsId;
    }

    public Integer getRoleID() {
        return roleID;
    }

    public void setRoleID(Integer roleID) {
        this.roleID = roleID;
    }

    public String getMmsCode() {
        return mmsCode;
    }

    public void setMmsCode(String mmsCode) {
        this.mmsCode = mmsCode;
    }

    public String getFieldChecksum() {
        return fieldChecksum;
    }

    public void setFieldChecksum(String fieldChecksum) {
        this.fieldChecksum = fieldChecksum;
    }

    public String getOfficerName() {
        return officerName;
    }

    public void setOfficerName(String officerName) {
        this.officerName = officerName;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getEmailName() {
        return emailName;
    }

    public void setEmailName(String emailName) {
        this.emailName = emailName;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getOfficerStatusCode() {
        return officerStatusCode;
    }

    public void setOfficerStatusCode(Integer officerStatusCode) {
        this.officerStatusCode = officerStatusCode;
    }

    public String getOfficerStatusDesc() {
        return officerStatusDesc;
    }

    public void setOfficerStatusDesc(String officerStatusDesc) {
        this.officerStatusDesc = officerStatusDesc;
    }

    public Double getAmtApprovalLimit() {
        return amtApprovalLimit;
    }

    public void setAmtApprovalLimit(Double amtApprovalLimit) {
        this.amtApprovalLimit = amtApprovalLimit;
    }

    public LocalDateTime getDtKafka() {
        return dtKafka;
    }

    public void setDtKafka(LocalDateTime dtKafka) {
        this.dtKafka = dtKafka;
    }

    public String getMmsName() {
        return mmsName;
    }

    public void setMmsName(String mmsName) {
        this.mmsName = mmsName;
    }

    public LocalDate getDtCreated() {
        return dtCreated;
    }

    public void setDtCreated(LocalDate dtCreated) {
        this.dtCreated = dtCreated;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public LocalDateTime getDtLastLogon() {
        return dtLastLogon;
    }

    public void setDtLastLogon(LocalDateTime dtLastLogon) {
        this.dtLastLogon = dtLastLogon;
    }

    public Integer getAccountEnabled() {
        return accountEnabled;
    }

    public void setAccountEnabled(Integer accountEnabled) {
        this.accountEnabled = accountEnabled;
    }

    public String getMenuID() {
        return menuID;
    }

    public void setMenuID(String menuID) {
        this.menuID = menuID;
    }

    public LocalDate getDtStartProfile() {
        return dtStartProfile;
    }

    public void setDtStartProfile(LocalDate dtStartProfile) {
        this.dtStartProfile = dtStartProfile;
    }

    public LocalDate getDtEndProfile() {
        return dtEndProfile;
    }

    public void setDtEndProfile(LocalDate dtEndProfile) {
        this.dtEndProfile = dtEndProfile;
    }

    public LocalDate getDtValidityPassword() {
        return dtValidityPassword;
    }

    public void setDtValidityPassword(LocalDate dtValidityPassword) {
        this.dtValidityPassword = dtValidityPassword;
    }

    public Integer getKfoID() {
        return kfoID;
    }

    public void setKfoID(Integer kfoID) {
        this.kfoID = kfoID;
    }

    public String getKfoCode() {
        return kfoCode;
    }

    public void setKfoCode(String kfoCode) {
        this.kfoCode = kfoCode;
    }

    public String getKfoName() {
        return kfoName;
    }

    public void setKfoName(String kfoName) {
        this.kfoName = kfoName;
    }

    public Integer getKcsID() {
        return kcsID;
    }

    public void setKcsID(Integer kcsID) {
        this.kcsID = kcsID;
    }

    public String getKcsCode() {
        return kcsCode;
    }

    public void setKcsCode(String kcsCode) {
        this.kcsCode = kcsCode;
    }

    public String getKcsName() {
        return kcsName;
    }

    public void setKcsName(String kcsName) {
        this.kcsName = kcsName;
    }

    public LocalDate getDtJoin() {
        return dtJoin;
    }

    public void setDtJoin(LocalDate dtJoin) {
        this.dtJoin = dtJoin;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }
}
