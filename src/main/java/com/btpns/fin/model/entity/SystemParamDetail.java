package com.btpns.fin.model.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
@Entity
@Table(name="MSSYSTEMPARAMDETAIL")
public class SystemParamDetail {

    @Id
    @Size(max=25)
    private String paramId;
    private String paramDetailId;
    private String paramDetailDesc;
    public String getParamId() {
        return paramId;
    }

    public void setParamId(String paramId) {
        this.paramId = paramId;
    }

    public String getParamDetailId() {
        return paramDetailId;
    }

    public void setParamDetailId(String paramDetailId) {
        this.paramDetailId = paramDetailId;
    }

    public String getParamDetailDesc() {
        return paramDetailDesc;
    }

    public void setParamDetailDesc(String paramDetailDesc) {
        this.paramDetailDesc = paramDetailDesc;
    }
}
