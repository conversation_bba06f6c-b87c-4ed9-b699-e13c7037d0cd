package com.btpns.fin.model.entity;

import javax.persistence.*;
import javax.validation.constraints.Size;

@Entity
@Table(name = "MSCABANG")
public class Cabang
{
    @Id
    @Size(max=25)
    private String cabangId;
    private String cabangDesc;
    private Double totalBalance;
    private Double totalBalanceHT;
    private String email;

    public String getCabangId() {
        return cabangId;
    }

    public void setCabangId(String cabangId) {
        this.cabangId = cabangId;
    }

    public String getCabangDesc() {
        return cabangDesc;
    }

    public void setCabangDesc(String cabangDesc) {
        this.cabangDesc = cabangDesc;
    }

    public Double getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(Double totalBalance) {
        this.totalBalance = totalBalance;
    }

    public Double getTotalBalanceHT() {
        return totalBalanceHT;
    }

    public void setTotalBalanceHT(Double totalBalanceHT) {
        this.totalBalanceHT = totalBalanceHT;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
