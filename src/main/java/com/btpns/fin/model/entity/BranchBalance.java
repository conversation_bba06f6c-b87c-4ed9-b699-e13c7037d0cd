package com.btpns.fin.model.entity;

import com.btpns.fin.model.AmountDetail;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "MSBRANCHBALANCE")
public class BranchBalance {
    @Id
    @Size(max = 10)
    private String branchId;

    private LocalDateTime createDateTime;
    private LocalDateTime updateDateTime;
    private Integer c50Count;
    private Double c50Amount;
    private Integer c100Count;
    private Double c100Amount;
    private Integer c200Count;
    private Double c200Amount;
    private Integer c500Count;
    private Double c500Amount;
    private Integer c1KCount;
    private Double c1KAmount;
    private Integer p1KCount;
    private Double p1KAmount;
    private Integer p2KCount;
    private Double p2KAmount;
    private Integer p5KCount;
    private Double p5KAmount;
    private Integer p10KCount;
    private Double p10KAmount;
    private Integer p20KCount;
    private Double p20KAmount;
    private Integer p50KCount;
    private Double p50KAmount;
    private Integer p75KCount;
    private Double p75KAmount;
    private Integer p100KCount;
    private Double p100KAmount;

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public LocalDateTime getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(LocalDateTime updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public Integer getC50Count() {
        return c50Count;
    }

    public void setC50Count(Integer c50Count) {
        this.c50Count = c50Count;
    }

    public Double getC50Amount() {
        return c50Amount;
    }

    public void setC50Amount(Double c50Amount) {
        this.c50Amount = c50Amount;
    }

    public Integer getC100Count() {
        return c100Count;
    }

    public void setC100Count(Integer c100Count) {
        this.c100Count = c100Count;
    }

    public Double getC100Amount() {
        return c100Amount;
    }

    public void setC100Amount(Double c100Amount) {
        this.c100Amount = c100Amount;
    }

    public Integer getC200Count() {
        return c200Count;
    }

    public void setC200Count(Integer c200Count) {
        this.c200Count = c200Count;
    }

    public Double getC200Amount() {
        return c200Amount;
    }

    public void setC200Amount(Double c200Amount) {
        this.c200Amount = c200Amount;
    }

    public Integer getC500Count() {
        return c500Count;
    }

    public void setC500Count(Integer c500Count) {
        this.c500Count = c500Count;
    }

    public Double getC500Amount() {
        return c500Amount;
    }

    public void setC500Amount(Double c500Amount) {
        this.c500Amount = c500Amount;
    }

    public Integer getC1KCount() {
        return c1KCount;
    }

    public void setC1KCount(Integer c1KCount) {
        this.c1KCount = c1KCount;
    }

    public Double getC1KAmount() {
        return c1KAmount;
    }

    public void setC1KAmount(Double c1KAmount) {
        this.c1KAmount = c1KAmount;
    }

    public Integer getP1KCount() {
        return p1KCount;
    }

    public void setP1KCount(Integer p1KCount) {
        this.p1KCount = p1KCount;
    }

    public Double getP1KAmount() {
        return p1KAmount;
    }

    public void setP1KAmount(Double p1KAmount) {
        this.p1KAmount = p1KAmount;
    }

    public Integer getP2KCount() {
        return p2KCount;
    }

    public void setP2KCount(Integer p2KCount) {
        this.p2KCount = p2KCount;
    }

    public Double getP2KAmount() {
        return p2KAmount;
    }

    public void setP2KAmount(Double p2KAmount) {
        this.p2KAmount = p2KAmount;
    }

    public Integer getP5KCount() {
        return p5KCount;
    }

    public void setP5KCount(Integer p5KCount) {
        this.p5KCount = p5KCount;
    }

    public Double getP5KAmount() {
        return p5KAmount;
    }

    public void setP5KAmount(Double p5KAmount) {
        this.p5KAmount = p5KAmount;
    }

    public Integer getP10KCount() {
        return p10KCount;
    }

    public void setP10KCount(Integer p10KCount) {
        this.p10KCount = p10KCount;
    }

    public Double getP10KAmount() {
        return p10KAmount;
    }

    public void setP10KAmount(Double p10KAmount) {
        this.p10KAmount = p10KAmount;
    }

    public Integer getP20KCount() {
        return p20KCount;
    }

    public void setP20KCount(Integer p20KCount) {
        this.p20KCount = p20KCount;
    }

    public Double getP20KAmount() {
        return p20KAmount;
    }

    public void setP20KAmount(Double p20KAmount) {
        this.p20KAmount = p20KAmount;
    }

    public Integer getP50KCount() {
        return p50KCount;
    }

    public void setP50KCount(Integer p50KCount) {
        this.p50KCount = p50KCount;
    }

    public Double getP50KAmount() {
        return p50KAmount;
    }

    public void setP50KAmount(Double p50KAmount) {
        this.p50KAmount = p50KAmount;
    }

    public Integer getP75KCount() {
        return p75KCount;
    }

    public void setP75KCount(Integer p75KCount) {
        this.p75KCount = p75KCount;
    }

    public Double getP75KAmount() {
        return p75KAmount;
    }

    public void setP75KAmount(Double p75KAmount) {
        this.p75KAmount = p75KAmount;
    }

    public Integer getP100KCount() {
        return p100KCount;
    }

    public void setP100KCount(Integer p100KCount) {
        this.p100KCount = p100KCount;
    }

    public Double getP100KAmount() {
        return p100KAmount;
    }

    public void setP100KAmount(Double p100KAmount) {
        this.p100KAmount = p100KAmount;
    }

    public void setAmount(TrxAmountDetail amountDetail){
        setC50Count(amountDetail.getC50Count());
        setC50Amount(amountDetail.getC50Amount());
        setC100Count(amountDetail.getC100Count());
        setC100Amount(amountDetail.getC100Amount());
        setC200Count(amountDetail.getC200Count());
        setC200Amount(amountDetail.getC200Amount());
        setC500Count(amountDetail.getC500Count());
        setC500Amount(amountDetail.getC500Amount());
        setC1KCount(amountDetail.getC1KCount());
        setC1KAmount(amountDetail.getC1KAmount());
        setP1KCount(amountDetail.getP1KCount());
        setP1KAmount(amountDetail.getP1KAmount());
        setP2KCount(amountDetail.getP2KCount());
        setP2KAmount(amountDetail.getP2KAmount());
        setP5KCount(amountDetail.getP5KCount());
        setP5KAmount(amountDetail.getP5KAmount());
        setP10KCount(amountDetail.getP10KCount());
        setP10KAmount(amountDetail.getP10KAmount());
        setP20KCount(amountDetail.getP20KCount());
        setP20KAmount(amountDetail.getP20KAmount());
        setP50KCount(amountDetail.getP50KCount());
        setP50KAmount(amountDetail.getP50KAmount());
        setP75KCount(amountDetail.getP75KCount());
        setP75KAmount(amountDetail.getP75KAmount());
        setP100KCount(amountDetail.getP100KCount());
        setP100KAmount(amountDetail.getP100KAmount());
    }
    public Double getTotal(){
        return c50Amount + c100Amount + c200Amount + c500Amount + c1KAmount + p1KAmount + p2KAmount + p5KAmount
                + p10KAmount + p20KAmount + p50KAmount + p75KAmount + p100KAmount;
    }
}
