package com.btpns.fin.model.entity;


import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Entity
@Table(name = "MSMMS")
public class Mms {
    @Id
    private Long mmsId;
    @Size(max = 400)
    private String address;
    @Size(max = 20)
    private String CostCenter;
    @Size(max = 100)
    private String DistrictDesc;
    @Size(max = 50)
    private String KFOCode;
    @Size(max = 50)
    private String KFOName;
    @Size(max = 10)
    private String MMSCode;
    @Size(max = 200)
    private String MMSName;
    @Size(max = 300)
    private String MMSStatusCode;
    @Size(max = 100)
    private String MMSStatusDesc;
    @Size(max = 100)
    private String ProvinceDesc;
    @Size(max = 200)
    private String RTRW;
    @Size(max = 100)
    private String StateDesc;
    @Size(max = 200)
    private String SubDistrictDesc;
    @Size(max = 20)
    private String ZipCode;

    public Long getMmsId() {
        return mmsId;
    }

    public void setMmsId(Long mmsId) {
        this.mmsId = mmsId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCostCenter() {
        return CostCenter;
    }

    public void setCostCenter(String costCenter) {
        CostCenter = costCenter;
    }

    public String getDistrictDesc() {
        return DistrictDesc;
    }

    public void setDistrictDesc(String districtDesc) {
        DistrictDesc = districtDesc;
    }

    public String getKFOCode() {
        return KFOCode;
    }

    public void setKFOCode(String KFOCode) {
        this.KFOCode = KFOCode;
    }

    public String getKFOName() {
        return KFOName;
    }

    public void setKFOName(String KFOName) {
        this.KFOName = KFOName;
    }

    public String getMMSCode() {
        return MMSCode;
    }

    public void setMMSCode(String MMSCode) {
        this.MMSCode = MMSCode;
    }

    public String getMMSName() {
        return MMSName;
    }

    public void setMMSName(String MMSName) {
        this.MMSName = MMSName;
    }

    public String getMMSStatusCode() {
        return MMSStatusCode;
    }

    public void setMMSStatusCode(String MMSStatusCode) {
        this.MMSStatusCode = MMSStatusCode;
    }

    public String getMMSStatusDesc() {
        return MMSStatusDesc;
    }

    public void setMMSStatusDesc(String MMSStatusDesc) {
        this.MMSStatusDesc = MMSStatusDesc;
    }

    public String getProvinceDesc() {
        return ProvinceDesc;
    }

    public void setProvinceDesc(String provinceDesc) {
        ProvinceDesc = provinceDesc;
    }

    public String getRTRW() {
        return RTRW;
    }

    public void setRTRW(String RTRW) {
        this.RTRW = RTRW;
    }

    public String getStateDesc() {
        return StateDesc;
    }

    public void setStateDesc(String stateDesc) {
        StateDesc = stateDesc;
    }

    public String getSubDistrictDesc() {
        return SubDistrictDesc;
    }

    public void setSubDistrictDesc(String subDistrictDesc) {
        SubDistrictDesc = subDistrictDesc;
    }

    public String getZipCode() {
        return ZipCode;
    }

    public void setZipCode(String zipCode) {
        ZipCode = zipCode;
    }
}
