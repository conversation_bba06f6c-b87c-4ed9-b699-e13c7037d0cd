package com.btpns.fin.model.entity;

import javax.persistence.*;
import javax.validation.constraints.Size;

@Entity
@Table(name = "MsHTBranchBalance")
public class HeadTellerBalance {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Size(max=20)
    private String branchId;
    private String nikHT;
    private Integer c50Count = 0;
    private Double c50Amount = 0.0;
    private Integer c100Count = 0;
    private Double c100Amount = 0.0;
    private Integer c200Count = 0;
    private Double c200Amount = 0.0;
    private Integer c500Count = 0;
    private Double c500Amount = 0.0;
    private Integer c1KCount = 0;
    private Double c1KAmount = 0.0;
    private Integer p1KCount = 0;
    private Double p1KAmount = 0.0;
    private Integer p2KCount = 0;
    private Double p2KAmount = 0.0;
    private Integer p5KCount = 0;
    private Double p5KAmount = 0.0;
    private Integer p10KCount = 0;
    private Double p10KAmount = 0.0;
    private Integer p20KCount = 0;
    private Double p20KAmount = 0.0;
    private Integer p50KCount = 0;
    private Double p50KAmount = 0.0;
    private Integer p75KCount = 0;
    private Double p75KAmount = 0.0;
    private Integer p100KCount = 0;
    private Double p100KAmount = 0.0;
    private Double totalAmount = 0.0;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getNikHT() {
        return nikHT;
    }

    public void setNikHT(String nikHT) {
        this.nikHT = nikHT;
    }

    public Integer getC50Count() {
        return c50Count;
    }

    public void setC50Count(Integer c50Count) {
        this.c50Count = c50Count;
    }

    public Double getC50Amount() {
        return c50Amount;
    }

    public void setC50Amount(Double c50Amount) {
        this.c50Amount = c50Amount;
    }

    public Integer getC100Count() {
        return c100Count;
    }

    public void setC100Count(Integer c100Count) {
        this.c100Count = c100Count;
    }

    public Double getC100Amount() {
        return c100Amount;
    }

    public void setC100Amount(Double c100Amount) {
        this.c100Amount = c100Amount;
    }

    public Integer getC200Count() {
        return c200Count;
    }

    public void setC200Count(Integer c200Count) {
        this.c200Count = c200Count;
    }

    public Double getC200Amount() {
        return c200Amount;
    }

    public void setC200Amount(Double c200Amount) {
        this.c200Amount = c200Amount;
    }

    public Integer getC500Count() {
        return c500Count;
    }

    public void setC500Count(Integer c500Count) {
        this.c500Count = c500Count;
    }

    public Double getC500Amount() {
        return c500Amount;
    }

    public void setC500Amount(Double c500Amount) {
        this.c500Amount = c500Amount;
    }

    public Integer getC1KCount() {
        return c1KCount;
    }

    public void setC1KCount(Integer c1KCount) {
        this.c1KCount = c1KCount;
    }

    public Double getC1KAmount() {
        return c1KAmount;
    }

    public void setC1KAmount(Double c1KAmount) {
        this.c1KAmount = c1KAmount;
    }

    public Integer getP1KCount() {
        return p1KCount;
    }

    public void setP1KCount(Integer p1KCount) {
        this.p1KCount = p1KCount;
    }

    public Double getP1KAmount() {
        return p1KAmount;
    }

    public void setP1KAmount(Double p1KAmount) {
        this.p1KAmount = p1KAmount;
    }

    public Integer getP2KCount() {
        return p2KCount;
    }

    public void setP2KCount(Integer p2KCount) {
        this.p2KCount = p2KCount;
    }

    public Double getP2KAmount() {
        return p2KAmount;
    }

    public void setP2KAmount(Double p2KAmount) {
        this.p2KAmount = p2KAmount;
    }

    public Integer getP5KCount() {
        return p5KCount;
    }

    public void setP5KCount(Integer p5KCount) {
        this.p5KCount = p5KCount;
    }

    public Double getP5KAmount() {
        return p5KAmount;
    }

    public void setP5KAmount(Double p5KAmount) {
        this.p5KAmount = p5KAmount;
    }

    public Integer getP10KCount() {
        return p10KCount;
    }

    public void setP10KCount(Integer p10KCount) {
        this.p10KCount = p10KCount;
    }

    public Double getP10KAmount() {
        return p10KAmount;
    }

    public void setP10KAmount(Double p10KAmount) {
        this.p10KAmount = p10KAmount;
    }

    public Integer getP20KCount() {
        return p20KCount;
    }

    public void setP20KCount(Integer p20KCount) {
        this.p20KCount = p20KCount;
    }

    public Double getP20KAmount() {
        return p20KAmount;
    }

    public void setP20KAmount(Double p20KAmount) {
        this.p20KAmount = p20KAmount;
    }

    public Integer getP50KCount() {
        return p50KCount;
    }

    public void setP50KCount(Integer p50KCount) {
        this.p50KCount = p50KCount;
    }

    public Double getP50KAmount() {
        return p50KAmount;
    }

    public void setP50KAmount(Double p50KAmount) {
        this.p50KAmount = p50KAmount;
    }

    public Integer getP75KCount() {
        return p75KCount;
    }

    public void setP75KCount(Integer p75KCount) {
        this.p75KCount = p75KCount;
    }

    public Double getP75KAmount() {
        return p75KAmount;
    }

    public void setP75KAmount(Double p75KAmount) {
        this.p75KAmount = p75KAmount;
    }

    public Integer getP100KCount() {
        return p100KCount;
    }

    public void setP100KCount(Integer p100KCount) {
        this.p100KCount = p100KCount;
    }

    public Double getP100KAmount() {
        return p100KAmount;
    }

    public void setP100KAmount(Double p100KAmount) {
        this.p100KAmount = p100KAmount;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }
}
