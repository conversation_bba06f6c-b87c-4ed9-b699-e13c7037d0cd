package com.btpns.fin.model;

import java.util.List;

public class HeadTellerTransactionModel {
    private String period;
    private String branchId;
    private List<HeadTellerTransactionDetailModel> details;

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public List<HeadTellerTransactionDetailModel> getDetails() {
        return details;
    }

    public void setDetails(List<HeadTellerTransactionDetailModel> details) {
        this.details = details;
    }
}
