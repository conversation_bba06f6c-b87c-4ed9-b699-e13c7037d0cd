package com.btpns.fin.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class VerificationStatusModel {
    private String nik;
    private String name;
    private String statusVerification;
    private String dateVerification;

    public VerificationStatusModel(String nik, String name, String statusVerification, LocalDateTime dateVerification) {
        this.nik = nik;
        this.name = name;
        this.statusVerification = statusVerification;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss");
        if (dateVerification!=null){
            this.dateVerification = formatter.format(dateVerification);
        }
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatusVerification() {
        return statusVerification;
    }

    public void setStatusVerification(String statusVerification) {
        this.statusVerification = statusVerification;
    }

    public String getDateVerification() {
        return dateVerification;
    }

    public void setDateVerification(String dateVerification) {
        this.dateVerification = dateVerification;
    }
}
