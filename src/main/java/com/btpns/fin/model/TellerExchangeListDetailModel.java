package com.btpns.fin.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class TellerExchangeListDetailModel {
     private String transactionId;
     private String type;
     private String typeDesc;
     private String createTimestamp;
     private Double totalAmount;
     private String inputerNIK;
     private String inputerName;
     private String verificationNIK;
     private String verificationName;
     private String status;

     public TellerExchangeListDetailModel(String transactionId, String type, String typeDesc, LocalDateTime createTimestamp, Double totalAmount, String inputerNIK, String verificationNIK, String status) {
          this.transactionId = transactionId;
          this.type = type;
          this.typeDesc = typeDesc;
          Locale localeId = new Locale("id","ID");
          DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, dd-MM-yyyy",localeId);
          this.createTimestamp = formatter.format(createTimestamp);
          this.totalAmount = totalAmount;
          this.inputerNIK = inputerNIK;
          this.verificationNIK = verificationNIK;
          this.status = status;
     }

     public String getTransactionId() {
          return transactionId;
     }

     public void setTransactionId(String transactionId) {
          this.transactionId = transactionId;
     }

     public String getType() {
          return type;
     }

     public void setType(String type) {
          this.type = type;
     }

     public String getTypeDesc() {
          return typeDesc;
     }

     public void setTypeDesc(String typeDesc) {
          this.typeDesc = typeDesc;
     }

     public String getCreateTimestamp() {
          return createTimestamp;
     }

     public void setCreateTimestamp(String createTimestamp) {
          this.createTimestamp = createTimestamp;
     }

     public Double getTotalAmount() {
          return totalAmount;
     }

     public void setTotalAmount(Double totalAmount) {
          this.totalAmount = totalAmount;
     }

     public String getInputerNIK() {
          return inputerNIK;
     }

     public void setInputerNIK(String inputerNIK) {
          this.inputerNIK = inputerNIK;
     }

     public String getInputerName() {
          return inputerName;
     }

     public void setInputerName(String inputerName) {
          this.inputerName = inputerName;
     }

     public String getVerificationNIK() {
          return verificationNIK;
     }

     public void setVerificationNIK(String verificationNIK) {
          this.verificationNIK = verificationNIK;
     }

     public String getVerificationName() {
          return verificationName;
     }

     public void setVerificationName(String verificationName) {
          this.verificationName = verificationName;
     }

     public String getStatus() {
          return status;
     }

     public void setStatus(String status) {
          this.status = status;
     }
}
