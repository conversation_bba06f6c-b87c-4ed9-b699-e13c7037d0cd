package com.btpns.fin.model;

import java.util.List;

public class TellerExchangePendingKHTModel {
    private String transactionId;
    private String period;
    private String branchId;
    private String status;
    private Double totalBeginBalance;
    private Double totalInBalance;
    private Double totalRemainingBalance;
    private List<HeadTellerAmountDetail> amountDetails;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Double getTotalBeginBalance() {
        return totalBeginBalance;
    }

    public void setTotalBeginBalance(Double totalBeginBalance) {
        this.totalBeginBalance = totalBeginBalance;
    }

    public Double getTotalInBalance() {
        return totalInBalance;
    }

    public void setTotalInBalance(Double totalInBalance) {
        this.totalInBalance = totalInBalance;
    }

    public Double getTotalRemainingBalance() {
        return totalRemainingBalance;
    }

    public void setTotalRemainingBalance(Double totalRemainingBalance) {
        this.totalRemainingBalance = totalRemainingBalance;
    }

    public List<HeadTellerAmountDetail> getAmountDetails() {
        return amountDetails;
    }

    public void setAmountDetails(List<HeadTellerAmountDetail> amountDetails) {
        this.amountDetails = amountDetails;
    }
}
