package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.time.LocalDate;
import java.util.List;

public class PendingHeadTellerTransactionModel {
    @JsonFormat(pattern="yyyy-MM-dd ")
    private LocalDate period;
    private String branchId;
    private String branchName;
    private String type;
    private List<ListTransactionHeadTellerModel> transactions;


    public LocalDate getPeriod() {
        return period;
    }

    public void setPeriod(LocalDate period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<ListTransactionHeadTellerModel> getTransactions() {
        return transactions;
    }

    public void setTransactions(List<ListTransactionHeadTellerModel> transactions) {
        this.transactions = transactions;
    }
}
