package com.btpns.fin.model;

import java.time.LocalDate;

public class ManagementAlternateModel {
    private Long id;
    private String nik;
    private String name;
    private String roleId;
    private String roleName;
    private String alternateRoleId;
    private String alternateRoleName;
    private String branchId;
    private LocalDate startPeriod;
    private LocalDate endPeriod;
    private Boolean activeFlag;
    private String info;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getAlternateRoleId() {
        return alternateRoleId;
    }

    public void setAlternateRoleId(String alternateRoleId) {
        this.alternateRoleId = alternateRoleId;
    }

    public String getAlternateRoleName() {
        return alternateRoleName;
    }

    public void setAlternateRoleName(String alternateRoleName) {
        this.alternateRoleName = alternateRoleName;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public LocalDate getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(LocalDate startPeriod) {
        this.startPeriod = startPeriod;
    }

    public LocalDate getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(LocalDate endPeriod) {
        this.endPeriod = endPeriod;
    }

    public Boolean getActiveFlag() {
        return activeFlag;
    }

    public void setActiveFlag(Boolean activeFlag) {
        this.activeFlag = activeFlag;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
