package com.btpns.fin.model;

import java.util.List;

public class TellerExchangeListModel {
    private String period;
    private String branchId;
    private String branchName;
    private int page = 1;
    private int limit = 10;
    private int totalPages;
    private Long totalItems;
    private List<TellerExchangeListDetailModel> details;

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public Long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(Long totalItems) {
        this.totalItems = totalItems;
    }

    public List<TellerExchangeListDetailModel> getDetails() {
        return details;
    }

    public void setDetails(List<TellerExchangeListDetailModel> details) {
        this.details = details;
    }
}
