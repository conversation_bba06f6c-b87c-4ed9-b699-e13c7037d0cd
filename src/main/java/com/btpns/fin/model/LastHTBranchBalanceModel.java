package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.util.List;

public class LastHTBranchBalanceModel {
    private String transactionId;
    @JsonFormat(pattern="yyyy-MM-dd ")
    private LocalDate period;
    private String branchId;
    private Boolean isPending;
    private Double totalBeginBalance;
    private Double totalOutBalance;
    private Double totalRemainingBalance;
    private List<HeadTellerAmountDetail> amountDetails;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public LocalDate getPeriod() {
        return period;
    }

    public void setPeriod(LocalDate period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public Boolean getIsPending() {
        return isPending;
    }

    public void setIsPending(Boolean pending) {
        isPending = pending;
    }

    public Double getTotalBeginBalance() {
        return totalBeginBalance;
    }

    public void setTotalBeginBalance(Double totalBeginBalance) {
        this.totalBeginBalance = totalBeginBalance;
    }

    public Double getTotalOutBalance() {
        return totalOutBalance;
    }

    public void setTotalOutBalance(Double totalOutBalance) {
        this.totalOutBalance = totalOutBalance;
    }

    public Double getTotalRemainingBalance() {
        return totalRemainingBalance;
    }

    public void setTotalRemainingBalance(Double totalRemainingBalance) {
        this.totalRemainingBalance = totalRemainingBalance;
    }

    public List<HeadTellerAmountDetail> getAmountDetails() {
        return amountDetails;
    }

    public void setAmountDetails(List<HeadTellerAmountDetail> amountDetails) {
        this.amountDetails = amountDetails;
    }
}
