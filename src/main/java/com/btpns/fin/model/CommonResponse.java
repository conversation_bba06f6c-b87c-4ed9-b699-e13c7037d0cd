package com.btpns.fin.model;

import static com.btpns.fin.constant.ResponseStatus.SUCCESS;

public class CommonResponse<T> {
    private String type;
    private String status;
    private String statusDesc;
    private T data;

    public CommonResponse(){
        this.status = SUCCESS.getCode();
        this.statusDesc = SUCCESS.getValue();
    }

    public CommonResponse(String status, String statusDesc) {
        this.status = status;
        this.statusDesc = statusDesc;
    }

    public CommonResponse(String status, String statusDesc, T data, String type) {
        this.type = type;
        this.status = status;
        this.statusDesc = statusDesc;
        this.data = data;
    }

    public CommonResponse(T data) {
        this.status ="00";
        this.statusDesc ="success";
        this.data = data;
    }
    
    public String getStatus() {
        return status;
    }

    public CommonResponse<T> setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public CommonResponse<T> setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
        return this;
    }

    public T getData() {
        return data;
    }

    public CommonResponse<T> setData(T data) {
        this.data = data;
        return this;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
