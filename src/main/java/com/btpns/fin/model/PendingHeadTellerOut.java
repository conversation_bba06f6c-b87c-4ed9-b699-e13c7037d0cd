package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

public class PendingHeadTellerOut {
  private Boolean isExists;
  private String lastTransactionId;
  @JsonFormat(pattern="yyyy-MM-dd ")
  private LocalDate period;
  private String branchId;

  public Boolean getIsExists() {
    return isExists;
  }

  public PendingHeadTellerOut setIsExists(Boolean isExists) {
    this.isExists = isExists;
    return this;
  }

  public String getLastTransactionId() {
    return lastTransactionId;
  }

  public PendingHeadTellerOut setLastTransactionId(String lastTransactionId) {
    this.lastTransactionId = lastTransactionId;
    return this;
  }

  public LocalDate getPeriod() {
    return period;
  }

  public PendingHeadTellerOut setPeriod(LocalDate period) {
    this.period = period;
    return this;
  }

  public String getBranchId() {
    return branchId;
  }

  public PendingHeadTellerOut setBranchId(String branchId) {
    this.branchId = branchId;
    return this;
  }
}
