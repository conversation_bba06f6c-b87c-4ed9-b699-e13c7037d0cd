package com.btpns.fin.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class PendingTransactionHTModel {
    private LocalDate period;
    private String timestamp;
    private String branchId;
    private String transactionId;
    private String branchName;
    private String type;
    private String typeDesc;
    private String tellerId;
    private String amountDetail;

    public PendingTransactionHTModel(LocalDate period, LocalDateTime timestamp, String branchId, String transactionId, String branchName, String type, String typeDesc, String tellerId, String amountDetail) {
        this.period = period;
        Locale localeId = new Locale("id","ID");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, dd-MM-yyyy",localeId);
        this.timestamp = formatter.format(timestamp);
        this.branchId = branchId;
        this.transactionId = transactionId;
        this.branchName = branchName;
        this.type = type;
        this.typeDesc = typeDesc;
        this.tellerId = tellerId;
        this.amountDetail = amountDetail;
    }

    public LocalDate getPeriod() {
        return period;
    }

    public void setPeriod(LocalDate period) {
        this.period = period;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }

    public String getTellerId() {
        return tellerId;
    }

    public void setTellerId(String tellerId) {
        this.tellerId = tellerId;
    }

    public String getAmountDetail() {
        return amountDetail;
    }

    public void setAmountDetail(String amountDetail) {
        this.amountDetail = amountDetail;
    }
}
