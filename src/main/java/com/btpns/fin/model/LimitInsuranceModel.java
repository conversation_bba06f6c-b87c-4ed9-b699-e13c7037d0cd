package com.btpns.fin.model;

public class LimitInsuranceModel {
    private String branchId;
    private String branchName;
    private String email;
    private Double limitAsuransiLembesAmount;
    private Double limitAsuransiCashBoxAmount;

    public LimitInsuranceModel(String branchId, String branchName, String email, Double limitAsuransiLembesAmount, Double limitAsuransiCashBoxAmount) {
        this.branchId = branchId;
        this.branchName = branchName;
        this.email = email;
        this.limitAsuransiLembesAmount = limitAsuransiLembesAmount;
        this.limitAsuransiCashBoxAmount = limitAsuransiCashBoxAmount;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Double getLimitAsuransiLembesAmount() {
        return limitAsuransiLembesAmount;
    }

    public void setLimitAsuransiLembesAmount(Double limitAsuransiLembesAmount) {
        this.limitAsuransiLembesAmount = limitAsuransiLembesAmount;
    }

    public Double getLimitAsuransiCashBoxAmount() {
        return limitAsuransiCashBoxAmount;
    }

    public void setLimitAsuransiCashBoxAmount(Double limitAsuransiCashBoxAmount) {
        this.limitAsuransiCashBoxAmount = limitAsuransiCashBoxAmount;
    }
}
