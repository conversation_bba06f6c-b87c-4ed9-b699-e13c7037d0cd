package com.btpns.fin.model;

import com.btpns.fin.constant.RoleConstant;

public class RolesModel {
    String roleId;
    String roleName;
    String branchId;
    
    String name;

    public RolesModel(String roleId, String roleName, String branchId, String name) {
        RoleConstant role = RoleConstant.getRoleByValue(roleId);
        if (role != null){
            this.roleId = role.getRoleId() == null ? roleId : role.getRoleId();
        }else {
            this.roleId = roleId;
        }
        this.roleName = roleName;
        this.branchId = branchId;
        this.name = name;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
