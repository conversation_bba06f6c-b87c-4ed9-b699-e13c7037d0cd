package com.btpns.fin.model;

import java.time.LocalDateTime;

public class AuditTrailAdditionalInfoModel {
    private String reason;
    private LocalDateTime timestamp;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
}
