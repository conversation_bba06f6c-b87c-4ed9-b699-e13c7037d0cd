package com.btpns.fin.model;


import com.google.gson.Gson;

import java.util.Base64;

public class Token {
    private String header;
    private String payload;
    private Profile profile;

    public Token(String bearer) {
        String bearerNative = bearer.replaceAll("Bearer ", "");
        String[] chunks = bearerNative.split("\\.");
        Base64.Decoder decoder = Base64.getDecoder();
        header = new String(decoder.decode(chunks[0]));
        payload = new String(decoder.decode(chunks[1]));
        Gson gson = new Gson();
        profile = gson.fromJson(payload, Profile.class);
    }

    public String getHeader() {
        return header;
    }

    public String getPayload() {
        return payload;
    }

    public Profile getProfile() {
        return profile;
    }

    public String getUsername() {
        return profile.getPreferred_username();
    }
}
