package com.btpns.fin.model;

import java.util.List;

public class CashOpnameListBAModel {
    private String startPeriod;
    private String endPeriod;
    private int page = 1;
    private int limit = 10;

    private int totalPages;
    private Long totalItems;
    private String branchId;
    private String branchName;
    private List<CashOpnameListBADetailModel> details;

    public String getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(String startPeriod) {
        this.startPeriod = startPeriod;
    }

    public String getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(String endPeriod) {
        this.endPeriod = endPeriod;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public Long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(Long totalItems) {
        this.totalItems = totalItems;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public List<CashOpnameListBADetailModel> getDetails() {
        return details;
    }

    public void setDetails(List<CashOpnameListBADetailModel> details) {
        this.details = details;
    }
}
