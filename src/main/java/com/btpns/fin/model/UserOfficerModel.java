package com.btpns.fin.model;

import com.btpns.fin.model.entity.Officer;
import com.btpns.fin.model.entity.OfficerNR;

public class UserOfficerModel {
    private String nik;
    private String officerName;
    private String loginName;
    private Integer roleId;
    private String roleName;
    private String mmsCode;
    private Integer officerStatusCode;
    private String officerStatusDesc;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getOfficerName() {
        return officerName;
    }

    public void setOfficerName(String officerName) {
        this.officerName = officerName;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getMmsCode() {
        return mmsCode;
    }

    public void setMmsCode(String mmsCode) {
        this.mmsCode = mmsCode;
    }

    public Integer getOfficerStatusCode() {
        return officerStatusCode;
    }

    public void setOfficerStatusCode(Integer officerStatusCode) {
        this.officerStatusCode = officerStatusCode;
    }

    public String getOfficerStatusDesc() {
        return officerStatusDesc;
    }

    public void setOfficerStatusDesc(String officerStatusDesc) {
        this.officerStatusDesc = officerStatusDesc;
    }
    
    public void setFromOfficer(Officer officer){
        this.nik = officer.getNik();
        this.officerName = officer.getOfficerName();
        this.loginName = officer.getLoginName();
        this.mmsCode = officer.getMmsCode();
        this.officerStatusDesc = officer.getOfficerStatusDesc();
        this.officerStatusCode = officer.getOfficerStatusCode();
        this.roleId = officer.getRoleID();
        this.roleName = officer.getRoleName();
    }
    
    public void setFromOfficerNR(OfficerNR officerNR){
        this.nik = officerNR.getNik();
        this.officerName = officerNR.getOfficerName();
        this.loginName = officerNR.getLoginName();
        this.mmsCode = officerNR.getMmsCode() != null ? officerNR.getMmsCode() : officerNR.getKcsCode();
        this.officerStatusDesc = officerNR.getOfficerStatusDesc();
        this.officerStatusCode = officerNR.getOfficerStatusCode();
        this.roleId = officerNR.getRoleID();
        this.roleName = officerNR.getRoleName();
    }
}
