package com.btpns.fin.model;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class CashOpnameListBADetailModel {
    private String transactionId;
    private String timestamp;
    private Double totalAmount;
    private String pic1NIK;
    private String pic1Name;
    private String pic2NIK;
    private String pic2Name;
    private String status;

    public CashOpnameListBADetailModel(String transactionId, LocalDate timestamp, Double totalAmount, String pic1NIK, String pic1Name, String pic2NIK, String pic2Name, String status) {
        this.transactionId = transactionId;
        Locale localeId = new Locale("id","ID");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, dd-MM-yyyy",localeId);
        this.timestamp = formatter.format(timestamp);
        this.totalAmount = totalAmount;
        this.pic1NIK = pic1NIK;
        this.pic1Name = pic1Name;
        this.pic2NIK = pic2NIK;
        this.pic2Name = pic2Name;
        this.status = status;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getPic1NIK() {
        return pic1NIK;
    }

    public void setPic1NIK(String pic1NIK) {
        this.pic1NIK = pic1NIK;
    }

    public String getPic1Name() {
        return pic1Name;
    }

    public void setPic1Name(String pic1Name) {
        this.pic1Name = pic1Name;
    }

    public String getPic2NIK() {
        return pic2NIK;
    }

    public void setPic2NIK(String pic2NIK) {
        this.pic2NIK = pic2NIK;
    }

    public String getPic2Name() {
        return pic2Name;
    }

    public void setPic2Name(String pic2Name) {
        this.pic2Name = pic2Name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
