package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

public class PendingCashCount {
    private Boolean isExists;
    private String lastTransactionId;
    @JsonFormat(pattern="yyyy-MM-dd ")
    private LocalDate period;
    private String branchId;

    public Boolean getExists() {
        return isExists;
    }

    public void setExists(Boolean exists) {
        isExists = exists;
    }

    public String getLastTransactionId() {
        return lastTransactionId;
    }

    public void setLastTransactionId(String lastTransactionId) {
        this.lastTransactionId = lastTransactionId;
    }

    public LocalDate getPeriod() {
        return period;
    }

    public void setPeriod(LocalDate period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }
}

