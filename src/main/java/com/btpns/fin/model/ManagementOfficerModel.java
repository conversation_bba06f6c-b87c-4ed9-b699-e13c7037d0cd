package com.btpns.fin.model;

public class ManagementOfficerModel {
    private Integer msOfficerNRId;
    private String officerId;
    private String roleId;
    private String roleName;
    private String officerCode;
    private String officerName;
    private String nik;
    private String name;
    private String loginName;
    private String emailName;
    private String kfoCode;
    private String kfoName;
    private String kcsCode;
    private String kcsName;
    private Integer officerStatusCode;
    private String officerStatusDesc;
    private Integer amtApprovalLimit;

    public Integer getMsOfficerNRId() {
        return msOfficerNRId;
    }

    public void setMsOfficerNRId(Integer msOfficerNRId) {
        this.msOfficerNRId = msOfficerNRId;
    }

    public String getOfficerId() {
        return officerId;
    }

    public void setOfficerId(String officerId) {
        this.officerId = officerId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getOfficerCode() {
        return officerCode;
    }

    public void setOfficerCode(String officerCode) {
        this.officerCode = officerCode;
    }

    public String getOfficerName() {
        return officerName;
    }

    public void setOfficerName(String officerName) {
        this.officerName = officerName;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getEmailName() {
        return emailName;
    }

    public void setEmailName(String emailName) {
        this.emailName = emailName;
    }

    public String getKfoCode() {
        return kfoCode;
    }

    public void setKfoCode(String kfoCode) {
        this.kfoCode = kfoCode;
    }

    public String getKfoName() {
        return kfoName;
    }

    public void setKfoName(String kfoName) {
        this.kfoName = kfoName;
    }

    public String getKcsCode() {
        return kcsCode;
    }

    public void setKcsCode(String kcsCode) {
        this.kcsCode = kcsCode;
    }

    public String getKcsName() {
        return kcsName;
    }

    public void setKcsName(String kcsName) {
        this.kcsName = kcsName;
    }

    public Integer getOfficerStatusCode() {
        return officerStatusCode;
    }

    public void setOfficerStatusCode(Integer officerStatusCode) {
        this.officerStatusCode = officerStatusCode;
    }

    public String getOfficerStatusDesc() {
        return officerStatusDesc;
    }

    public void setOfficerStatusDesc(String officerStatusDesc) {
        this.officerStatusDesc = officerStatusDesc;
    }

    public Integer getAmtApprovalLimit() {
        return amtApprovalLimit;
    }

    public void setAmtApprovalLimit(Integer amtApprovalLimit) {
        this.amtApprovalLimit = amtApprovalLimit;
    }
}
