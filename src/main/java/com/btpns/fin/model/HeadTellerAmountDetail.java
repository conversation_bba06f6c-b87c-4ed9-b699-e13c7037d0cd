package com.btpns.fin.model;

public class HeadTellerAmountDetail extends BaseAmount{
    private Double beginBalance;
    private Integer amountCount;
    private Double amountTotal;
    private Double remainingBalance;

    public Double getBeginBalance() {
        return beginBalance;
    }

    public void setBeginBalance(Double beginBalance) {
        this.beginBalance = beginBalance;
    }

    public Integer getAmountCount() {
        return amountCount;
    }

    public void setAmountCount(Integer amountCount) {
        this.amountCount = amountCount;
    }

    public Double getAmountTotal() {
        return amountTotal;
    }

    public void setAmountTotal(Double amountTotal) {
        this.amountTotal = amountTotal;
    }

    public Double getRemainingBalance() {
        return remainingBalance;
    }

    public void setRemainingBalance(Double remainingBalance) {
        this.remainingBalance = remainingBalance;
    }
}
