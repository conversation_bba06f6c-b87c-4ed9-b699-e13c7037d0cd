package com.btpns.fin.model;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class HeadTellerTransactionDetailModel {
    private String transactionId;
    private String type;
    private String typeDesc;
    private String createTimeStamp;
    private Double amount;
    private String nikInputer;
    private String nameInputer;
    private String nikVerification;
    private String nameVerification;
    private String status;

    public HeadTellerTransactionDetailModel(String transactionId, String type, String typeDesc, LocalDateTime createTimeStamp, Double amount, String nikInputer, String nameInputer, String nikVerification, String nameVerification, String status) {
        this.transactionId = transactionId;
        this.type = type;
        this.typeDesc = typeDesc;
        Locale localeId = new Locale("id","ID");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, dd-MM-yyyy",localeId);
        this.createTimeStamp = formatter.format(createTimeStamp);
        this.amount = amount;
        this.nikInputer = nikInputer;
        this.nameInputer = nameInputer;
        this.nikVerification = nikVerification;
        this.nameVerification = nameVerification;
        this.status = status;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }

    public String getCreateTimeStamp() {
        return createTimeStamp;
    }

    public void setCreateTimeStamp(String createTimeStamp) {
        this.createTimeStamp = createTimeStamp;
    }

    public String getNikInputer() {
        return nikInputer;
    }

    public void setNikInputer(String nikInputer) {
        this.nikInputer = nikInputer;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getNameInputer() {
        return nameInputer;
    }

    public void setNameInputer(String nameInputer) {
        this.nameInputer = nameInputer;
    }

    public String getNikVerification() {
        return nikVerification;
    }

    public void setNikVerification(String nikVerification) {
        this.nikVerification = nikVerification;
    }

    public String getNameVerification() {
        return nameVerification;
    }

    public void setNameVerification(String nameVerification) {
        this.nameVerification = nameVerification;
    }
}
