package com.btpns.fin.model;

import com.btpns.fin.model.entity.TrxHTAmountDetail;
import com.btpns.fin.model.entity.TrxHeadTeller;

public class TransactionHeadTeller {
    private TrxHeadTeller trxHeadTeller;
    private TrxHTAmountDetail trxHTAmountDetail;

    public TransactionHeadTeller(TrxHeadTeller trxHeadTeller, TrxHTAmountDetail trxHTAmountDetail) {
        this.trxHeadTeller = trxHeadTeller;
        this.trxHTAmountDetail = trxHTAmountDetail;
    }

    public TrxHeadTeller getTrxHeadTeller() {
        return trxHeadTeller;
    }

    public void setTrxHeadTeller(TrxHeadTeller trxHeadTeller) {
        this.trxHeadTeller = trxHeadTeller;
    }

    public TrxHTAmountDetail getTrxHTAmountDetail() {
        return trxHTAmountDetail;
    }

    public void setTrxHTAmountDetail(TrxHTAmountDetail trxHTAmountDetail) {
        this.trxHTAmountDetail = trxHTAmountDetail;
    }
}
