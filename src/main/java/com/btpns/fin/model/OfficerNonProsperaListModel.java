package com.btpns.fin.model;

import java.util.List;

public class OfficerNonProsperaListModel {

    private String nik;
    private int page = 1;
    private int limit = 10;

    private int totalPages;
    private Long totalItems;
    private List<OfficerNonProsperaDetailModel> details;

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public Long getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(Long totalItems) {
        this.totalItems = totalItems;
    }

    public List<OfficerNonProsperaDetailModel> getDetails() {
        return details;
    }

    public void setDetails(List<OfficerNonProsperaDetailModel> details) {
        this.details = details;
    }
}
