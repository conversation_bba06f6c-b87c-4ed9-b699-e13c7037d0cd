package com.btpns.fin.model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class KoreksiTransactionDetailModel {
    
    private String transactionId;
    
    private String type;
    
    private String typeDesc;
    
    private String createTimeStamp;
    
    private Double totalAmount;
    
    private String inputer;
    
    private String PICVerifikasi;
    
    private String status;

    public KoreksiTransactionDetailModel(String transactionId, String type, String typeDesc, LocalDateTime createTimeStamp, Double totalAmount, String inputer, String PICVerifikasi, String status) {
        this.transactionId = transactionId;
        this.type = type;
        this.typeDesc = typeDesc;

        Locale localeId = new Locale("id", "ID");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, dd-MM-yyyy", localeId);
        this.createTimeStamp = formatter.format(createTimeStamp);

        this.totalAmount = totalAmount;
        this.inputer = inputer;
        this.PICVerifikasi = PICVerifikasi;
        this.status = status;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public KoreksiTransactionDetailModel setTransactionId(String transactionId) {
        this.transactionId = transactionId;
        return this;
    }

    public String getType() {
        return type;
    }

    public KoreksiTransactionDetailModel setType(String type) {
        this.type = type;
        return this;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public KoreksiTransactionDetailModel setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
        return this;
    }

    public String getCreateTimeStamp() {
        return createTimeStamp;
    }

    public KoreksiTransactionDetailModel setCreateTimeStamp(String createTimeStamp) {
        this.createTimeStamp = createTimeStamp;
        return this;
    }

    public Double getTotalAmount() {
        return totalAmount;
    }

    public KoreksiTransactionDetailModel setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
        return this;
    }

    public String getInputer() {
        return inputer;
    }

    public KoreksiTransactionDetailModel setInputer(String inputer) {
        this.inputer = inputer;
        return this;
    }

    public String getPICVerifikasi() {
        return PICVerifikasi;
    }

    public KoreksiTransactionDetailModel setPICVerifikasi(String PICVerifikasi) {
        this.PICVerifikasi = PICVerifikasi;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public KoreksiTransactionDetailModel setStatus(String status) {
        this.status = status;
        return this;
    }
}
