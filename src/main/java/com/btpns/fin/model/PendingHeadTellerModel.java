package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.util.List;

public class PendingHeadTellerModel {
    @JsonFormat(pattern="yyyy-MM-dd ")
    private LocalDate period;
    private String branchName;
    private String branchId;
    private Double totalBalance;
    private List<BalanceDetailModel> balanceDetail;

    public LocalDate getPeriod() {
        return period;
    }

    public void setPeriod(LocalDate period) {
        this.period = period;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public Double getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(Double totalBalance) {
        this.totalBalance = totalBalance;
    }

    public List<BalanceDetailModel> getBalanceDetail() {
        return balanceDetail;
    }

    public void setBalanceDetail(List<BalanceDetailModel> balanceDetail) {
        this.balanceDetail = balanceDetail;
    }
}
