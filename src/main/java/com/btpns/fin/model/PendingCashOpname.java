package com.btpns.fin.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.persistence.Transient;
import java.time.LocalDate;
import java.util.List;

public class PendingCashOpname {
    private Boolean isExists;
    private String lastTransactionId;
    @JsonFormat(pattern="yyyy-MM-dd ")
    private LocalDate period;
    private String branchId;
    private String status;
    private Double branchBalance;
    private List<AmountDetail> balanceDetails;


    public Boolean getExists() {
        return isExists;
    }

    public void setExists(Boolean exists) {
        isExists = exists;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLastTransactionId() {
        return lastTransactionId;
    }

    public void setLastTransactionId(String lastTransactionId) {
        this.lastTransactionId = lastTransactionId;
    }

    public LocalDate getPeriod() {
        return period;
    }

    public void setPeriod(LocalDate period) {
        this.period = period;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public Double getBranchBalance() {
        return branchBalance;
    }

    public void setBranchBalance(Double branchBalance) {
        this.branchBalance = branchBalance;
    }

    public List<AmountDetail> getBalanceDetails() {
        return balanceDetails;
    }

    public void setBalanceDetails(List<AmountDetail> balanceDetails) {
        this.balanceDetails = balanceDetails;
    }
}

