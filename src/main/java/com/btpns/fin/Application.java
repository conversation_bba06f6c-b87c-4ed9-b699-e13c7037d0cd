package com.btpns.fin;

import com.btpns.platform.BtpnsApplication;
import org.joda.time.DateTimeZone;
import org.springframework.boot.Banner;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.util.TimeZone;

@BtpnsApplication
public class Application {
    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Jakarta"));
        DateTimeZone.setDefault(DateTimeZone.forID("Asia/Jakarta"));
        new SpringApplicationBuilder(Application.class)
                .bannerMode(Banner.Mode.OFF)
                .build()
                .run(args);
    }
}
