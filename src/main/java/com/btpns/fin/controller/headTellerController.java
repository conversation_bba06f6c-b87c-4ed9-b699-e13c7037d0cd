package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.*;
import com.btpns.fin.model.request.CancelHT2VRequest;
import com.btpns.fin.model.request.InputHT2VRequest;
import com.btpns.fin.model.request.InputHeadTellerPendingRequest;
import com.btpns.fin.model.request.SubmitPendingTransactionRequest;
import com.btpns.fin.model.response.*;
import com.btpns.fin.service.CMAConfigService;
import com.btpns.fin.service.HeadTellerService;
import com.btpns.fin.service.ManagementOfficerService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value = "/v1/kht/headteller")
@CrossOrigin("*")
public class headTellerController {

    private static final Logger logger = LoggerFactory.getLogger(headTellerController.class);

    @Autowired
    HeadTellerService headTellerService;

    @Autowired
    ManagementOfficerService managementOfficerService;
    @Autowired
    CMAConfigService cmaConfigService;
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping(value = "/{branchId}/pending/{type}")
    public ResponseEntity<CommonResponse<PendingHeadTellerTransactionModel>> getHeadTellerTransaction(@RequestHeader("Authorization") String authorization,
                                                                                      @PathVariable String branchId,
                                                                                      @PathVariable String type) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getHeadTellerTransaction branchId {} type {} from {}", branchId, type, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getHeadTellerTransaction Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)) {
                CommonResponse<PendingHeadTellerTransactionModel> headTellerTransaction = headTellerService.pendingTransaction(profile.getProfile(), branchId, type);
                logger.info("Response >>> getHeadTellerTransaction : with size {}", headTellerTransaction.getData().getTransactions().size());
                return ResponseEntity.ok(headTellerTransaction);
            }
                else {
                logger.error("Fail to get head teller Transaction, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get head teller Transaction ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/{branchId}/pending/balance/{type}")
    public ResponseEntity<CommonResponse<PendingHeadTellerModel>> getHeadTellerBalance(@RequestHeader("Authorization") String authorization,
                                                                       @PathVariable String branchId,
                                                                       @PathVariable String type) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getHeadTellerBalance branchId {} type {} from {}", branchId, type, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getHeadTellerBalance Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<PendingHeadTellerModel> headTellerTransaction = headTellerService.pendingBalance(profile.getProfile(), branchId,type);
                logger.info("Response >>> getHeadTellerBalance : {}", gson.toJson(headTellerTransaction));
                return ResponseEntity.ok(headTellerTransaction);
            }else {
                logger.error("Fail to get pending balance, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get pending balance ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/{branchId}/pending/{type}")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> addHTPendingTransaction(@RequestHeader("Authorization") String authorization,
                                                                          @PathVariable String branchId,
                                                                          @PathVariable String type,
                                                                          @RequestBody InputHeadTellerPendingRequest request) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< addHTPendingTransaction branchId {} type {} request {} from {}", branchId, type, gson.toJson(request), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> addHTPendingTransaction Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                if (isExistInInterval(profile.getProfile().getPreferred_username(),  branchId, type)){
                    logger.info("Response >>> addHTPendingTransaction : {}", HttpStatus.TOO_MANY_REQUESTS);
                    return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
                }
                CommonResponse<InputTransactionResponse> inputTransactionResponse = headTellerService.addPendingTransaction(profile.getProfile(), request, branchId, type);
                logger.info("Response >>> addHTPendingTransaction : {}", gson.toJson(inputTransactionResponse));
                return ResponseEntity.ok(inputTransactionResponse);
            }else {
                logger.error("Fail to input pending transaction, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to input pending transaction ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping(value = "/{branchId}/pending/{type}/{transactionId}")
    public ResponseEntity<CommonResponse<DeleteTransactionResponse>> deleteHTPendingTransaction(@RequestHeader("Authorization") String authorization,
                                                                       @PathVariable String branchId,
                                                                       @PathVariable String type,
                                                                       @PathVariable String transactionId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< deleteHTPendingTransaction branchId {} type {} transactionId {} from {}", branchId, type, transactionId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> deleteHTPendingTransaction Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<DeleteTransactionResponse> deleteTransactionResponse = headTellerService.deleteTransactionHT(profile.getProfile(), branchId, type, transactionId);
                logger.info("Response >>> deleteHTPendingTransaction : {}", gson.toJson(deleteTransactionResponse));
                return ResponseEntity.ok(deleteTransactionResponse);
            }else {
                logger.error("Fail to get Branch Officer, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get head teller Transaction ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/{branchId}/pending/{type}/release")
    public ResponseEntity<CommonResponse<SubmitPendingTransactionResponse>> submitHTPendingTransaction(@RequestHeader("Authorization") String authorization,
                                                                                     @PathVariable String branchId,
                                                                                     @PathVariable String type,
                                                                                     @RequestBody SubmitPendingTransactionRequest request) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< submitHTPendingTransaction branchId {} type {} request {} from {}", branchId, type, gson.toJson(request), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> submitHTPendingTransaction Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<SubmitPendingTransactionResponse> submitPendingTransactionResponse = headTellerService.submitPendingTransaction(profile.getProfile(), branchId, type, request);
                logger.info("Response >>> submitHTPendingTransaction : {}", gson.toJson(submitPendingTransactionResponse));
                return ResponseEntity.ok(submitPendingTransactionResponse);
            }else {
                logger.error("Fail to Submit pending head teller Transaction, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to Submit pending head teller Transaction ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }

    }

    @GetMapping(value = "/{branchId}/balance")
    public ResponseEntity<CommonResponse<LastHTBranchBalanceModel>> getHTLastBalance(@RequestHeader("Authorization") String authorization,
                                                                     @PathVariable String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getHTLastBalance branchId {} from {}", branchId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getHTLastBalance Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<LastHTBranchBalanceModel> lastHTBranchBalance = headTellerService.getLastHTBalance(profile.getProfile(), branchId);
                logger.info("Response >>> getHTLastBalance : {}", gson.toJson(lastHTBranchBalance));
                return ResponseEntity.ok(lastHTBranchBalance);
            }else {
                logger.error("Fail to get head teller Transaction, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get head teller Transaction ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @PostMapping(value = "/{branchId}/HT2V")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> submitHT2V(@RequestHeader("Authorization") String authorization,
                                                               @PathVariable String branchId,
                                                               @RequestBody InputHT2VRequest request) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< submitHT2V branchId {} request {} from {}", branchId, gson.toJson(request), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> submitHT2V Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<InputTransactionResponse> inputTransactionResponse = headTellerService.inputHT2V(profile.getProfile(),request, branchId);
                logger.info("Response >>> submitHT2V : {}", gson.toJson(inputTransactionResponse));
                return ResponseEntity.ok(inputTransactionResponse);
            }else {
                logger.error("Fail to submit HT2V, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to submit HT2V ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping(value = "/{branchId}/HT2V/pending")
    public ResponseEntity<CommonResponse<HeadTellerInModel>> getPendingHT2V(@RequestHeader("Authorization") String authorization,
                                                                     @PathVariable String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getPendingHT2V branchId {} from {}", branchId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getPendingHT2V Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if (managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)) {
                CommonResponse<HeadTellerInModel> headTellerInModel = headTellerService.getHT2VPending(profile.getProfile(), branchId);
                logger.info("Response >>> getPendingHT2V : {}", gson.toJson(headTellerInModel));
                return ResponseEntity.ok(headTellerInModel);
            } else {
                logger.error("Fail to get Pending HT2V, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get Pending HT2V ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/{branchId}/HT2V/cancel")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> cancelHT2V(@RequestHeader ("Authorization")String authorization,
                                                                            @RequestBody CancelHT2VRequest request,
                                                               @PathVariable String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< cancelHT2V branchId {} request {} from {}", branchId, gson.toJson(request), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> cancelHT2V Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<InputTransactionResponse> cancelHT2VResponse = headTellerService.cancelHT2V(profile.getProfile(),request,branchId);
                logger.info("Response >>> cancelHT2V : {}", gson.toJson(cancelHT2VResponse));
                return ResponseEntity.ok(cancelHT2VResponse);
            }else {
                logger.error("Fail to cancel HT2V, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to cancel HT2V ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    public boolean isExistInInterval(String nik, String branch, String type) {
        return headTellerService.existInIntervalPendingHeadTeller(nik, 60, branch, type);
    }


    @GetMapping(value = "/{branchId}/total-balance")
    public ResponseEntity<CommonResponse<TotalBranchBalanceResponse>> getTotalBranchBalance(@RequestHeader("Authorization") String authorization,
                                                                                            @PathVariable String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getTotalBranchBalance branchId {} from {}", branchId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getTotalBranchBalance Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<TotalBranchBalanceResponse> totalBranchBalance = headTellerService.totalBranchBalance(profile.getProfile(), branchId);
                logger.info("Response >>> getTotalBranchBalance : {}", gson.toJson(totalBranchBalance));
                return ResponseEntity.ok(totalBranchBalance);
            }else {
                logger.error("Fail to get total branch balanc, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get total branch balance ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/{branchId}/pendingTransaction")
    public ResponseEntity<CommonResponse<PendingHeadTellerTransactionResponse>> getPendingHeadTellerTransaction(@RequestHeader("Authorization") String authorization,
                                                                                                      @PathVariable String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getPendingHeadTellerTransaction branchId {} from {}", branchId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getPendingHeadTellerTransaction Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<PendingHeadTellerTransactionResponse> pendingHeadTeller = headTellerService.pendingHeadTellerTransaction(branchId);
                logger.info("Response >>> getPendingHeadTellerTransaction : {}", gson.toJson(pendingHeadTeller));
                return ResponseEntity.ok(pendingHeadTeller);
            }else {
                logger.error("Fail to getPendingHeadTellerTransaction, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get PendingHeadTellerTransaction ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}

