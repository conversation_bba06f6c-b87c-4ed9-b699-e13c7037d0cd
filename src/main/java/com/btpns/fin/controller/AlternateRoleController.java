package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.AlternateListModel;
import com.btpns.fin.model.CommonResponse;
import com.btpns.fin.model.Token;
import com.btpns.fin.service.ManagementOfficerService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value ="/v1/alternate/")
@CrossOrigin("*")
public class AlternateRoleController {
    private static final Logger logger = LoggerFactory.getLogger(AlternateRoleController.class);
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    ManagementOfficerService managementOfficerService;

    @GetMapping(value = "list")
    public ResponseEntity<CommonResponse<AlternateListModel>> getListAlternate(@RequestHeader("Authorization") String authorization,
                                                                                     @RequestParam (required = false) String nik,
                                                                                     @RequestParam(required = false) String page,
                                                                                     @RequestParam(required = false) String limit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getListAlternate with request {} from {}", gson.toJson(nik), getProfile(gson, profile));
            CommonResponse<AlternateListModel> listAlternate = managementOfficerService.getListAlternate(nik, page, limit);
            logger.info("Response >>> getListAlternate : {}", gson.toJson(listAlternate));
            return ResponseEntity.ok(listAlternate);
        } catch (Exception e) {
            logger.error("Fail to input cash count ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
