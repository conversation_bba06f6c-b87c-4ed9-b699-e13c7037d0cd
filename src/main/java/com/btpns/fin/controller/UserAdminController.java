package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.CommonResponse;
import com.btpns.fin.model.OfficerNonProsperaListModel;
import com.btpns.fin.model.Token;
import com.btpns.fin.service.ManagementOfficerService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value ="/v1/admin/")
@CrossOrigin("*")
public class UserAdminController {
    private static final Logger logger = LoggerFactory.getLogger(UserAdminController.class);
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    @Autowired
    ManagementOfficerService managementOfficerService;

    @GetMapping(value = "nonProspera")
    public ResponseEntity<CommonResponse<OfficerNonProsperaListModel>> getListOfficerNonProspera(@RequestHeader("Authorization") String authorization,
                                                                                                       @RequestParam(required = false) String nik,
                                                                                                       @RequestParam(required = false) String page,
                                                                                                       @RequestParam(required = false) String limit) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getListAlternate with request {} from {}", gson.toJson(nik), getProfile(gson, profile));
            CommonResponse<OfficerNonProsperaListModel> listAlternate = managementOfficerService.getListNonProspera(nik, page, limit);
            logger.info("Response >>> getListAlternate : {}", gson.toJson(listAlternate));
            return ResponseEntity.ok(listAlternate);
        } catch (Exception e) {
            logger.error("Fail to input cash count ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
