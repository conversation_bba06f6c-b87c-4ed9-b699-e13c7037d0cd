package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.CommonResponse;
import com.btpns.fin.model.LimitInsuranceModel;
import com.btpns.fin.model.Token;
import com.btpns.fin.model.request.LimitInsuranceRequest;
import com.btpns.fin.model.request.LimitInsuranceRequestDetail;
import com.btpns.fin.model.response.LimitInsuranceSubmitResponse;
import com.btpns.fin.service.CMAConfigService;
import com.btpns.fin.service.LimitInsuranceService;
import com.btpns.fin.service.ManagementOfficerService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value ="/v1")
@CrossOrigin("*")
public class LimitInsuranceController {
    private static final Logger logger = LoggerFactory.getLogger(LimitInsuranceController.class);
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    
    @Autowired
    ManagementOfficerService managementOfficerService;
    @Autowired
    LimitInsuranceService limitInsuranceService;
    @Autowired
    CMAConfigService cmaConfigService;
    
    @GetMapping(value = "/limitInsurance")
    public ResponseEntity<CommonResponse<List<LimitInsuranceModel>>> getListLimitInsurance(@RequestHeader("Authorization")String authorization
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getListLimitInsurance from {}",getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getListLimitInsurance Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<List<LimitInsuranceModel>> getListLimitInsurance = limitInsuranceService.getListLimitInsurance();
            logger.info("Response >>> getListLimitInsurance : {}", gson.toJson(getListLimitInsurance));
            return ResponseEntity.ok(getListLimitInsurance);
        }  catch (Exception e) {
            logger.error("Fail to get getListLimitInsurance ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @PostMapping(value = "/limitInsurance")
    public ResponseEntity<CommonResponse<LimitInsuranceSubmitResponse>> saveLimit(@RequestHeader("Authorization")String authorization,
                                                                                  @RequestBody LimitInsuranceRequest request) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveLimit request {} from {}", gson.toJson(maskingPassword(request.getDetails())), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> saveLimit Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<LimitInsuranceSubmitResponse> saveLimit = limitInsuranceService.submitLimitInsurance(profile.getProfile(),request);
            logger.info("Response >>> saveLimit : {}", gson.toJson(saveLimit));
            return ResponseEntity.ok(saveLimit);
        }  catch (Exception e) {
            logger.error("Fail to get saveLimit ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private LimitInsuranceRequestDetail maskingPassword(LimitInsuranceRequestDetail request) {
        LimitInsuranceRequestDetail clone = request.clone(gson);
        clone.setPassword("*****");
        return clone;
    }
}
