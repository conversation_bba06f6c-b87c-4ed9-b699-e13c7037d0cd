package com.btpns.fin.controller;

import com.btpns.fin.constant.ResponseStatus;
import com.btpns.fin.constant.TrxType;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.*;
import com.btpns.fin.model.request.InputCashCountRequest;
import com.btpns.fin.model.response.InputTransactionResponse;
import com.btpns.fin.service.CMAConfigService;
import com.btpns.fin.service.CashCountService;
import com.btpns.fin.service.ManagementOfficerService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value ="/v1/")
@CrossOrigin("*")
public class cashCountController {

    private static final Logger logger = LoggerFactory.getLogger(cashCountController.class);

    @Autowired
    CashCountService cashCountService;

    @Autowired
    ManagementOfficerService managementOfficerService;
    @Autowired
    CMAConfigService cmaConfigService;
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping(value = "cash-count")
    public ResponseEntity<InputTransactionResponse> postCashCount(@RequestHeader("Authorization") String authorization,
                                                                             @RequestBody InputCashCountRequest request) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< postCashCount with request {} from {}", gson.toJson(request), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> postCashCount Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            String trxType = cashCountService.cashCountType(TrxType.SAW.getCode(),TrxType.SAK.getCode(),TrxType.CC.getCode(), request.getType(), LocalDate.now(),request.getBranchId());
            if (isExistInInterval(profile.getProfile().getPreferred_username(), request.getBranchId(), trxType)){
                logger.info("Response >>> postCashCount : {}", HttpStatus.TOO_MANY_REQUESTS);
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            InputTransactionResponse headTellerTransaction = cashCountService.inputCashCount(profile.getProfile().getPreferred_username(), request);
            logger.info("Response >>> postCashCount : {}", gson.toJson(headTellerTransaction));
            return ResponseEntity.ok(headTellerTransaction);
        } catch (Exception e) {
            logger.error("Fail to input cash count ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "cash-count/{transactionId}")
    public ResponseEntity<CommonResponse<CashCountDetailModel>> getCashCountTransaction(@RequestHeader ("Authorization") String authorization,
                                                                                         @PathVariable String transactionId) {
        Token profile = new Token(authorization);
        CommonResponse<CashCountDetailModel> response = new CommonResponse<>();
        try {
            logger.info("Received <<< getCashCountTransaction with transactionId {} from {}", transactionId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getCashCountTransaction Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CashCountDetailModel cashCountDetailModel = cashCountService.getDetailCashCount(profile.getProfile().getPreferred_username(), transactionId);
            response.setData(cashCountDetailModel);
            logger.info("Response >>> getCashCountTransaction : {}", gson.toJson(cashCountDetailModel));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Fail to get cash count detail ", e);
            response.setStatus(com.btpns.fin.constant.ResponseStatus.GENERAL_ERROR.getCode());
            response.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "cash-count/pending")
    public ResponseEntity<CommonResponse<PendingCashCount>> getPendingCashCount(@RequestHeader ("Authorization") String authorization,
                                                                                         @RequestParam String branch) {
        Token profile = new Token(authorization);
        try {
            logger.info("Received <<< getPendingCashCount with branch {} from {}", branch, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getPendingCashCount Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if (managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)) {
                CommonResponse<PendingCashCount> pendingCashCount = cashCountService.getPendingCashCount(profile.getProfile().getPreferred_username(), branch);
                logger.info("Response >>> getPendingCashCount : {}", gson.toJson(pendingCashCount));
                return ResponseEntity.ok(pendingCashCount);
            } else {
                logger.error("Fail to get cash count pending, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get cash count pending ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "cash-count/akhirhari")
    public ResponseEntity<CommonResponse<PendingAkhirHari>> getCheckPendingAkhirHari(@RequestHeader ("Authorization") String authorization,
                                                                                     @RequestParam String branch
                                                                               ) {
        Token profile = new Token(authorization);
        try {
            logger.info("Received <<< getCheckPendingCCAkhirHari with branch {} from {}", branch, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getCheckPendingCCAkhirHari Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<PendingAkhirHari> checkPendingAkhirHari = cashCountService.checkPendingAkhirHari(profile.getProfile().getPreferred_username(),branch);
                logger.info("Response >>> getCheckPendingCCAkhirHari : {}", gson.toJson(checkPendingAkhirHari));
                return ResponseEntity.ok(checkPendingAkhirHari);
            }else {
                logger.error("Fail to get cash count pending, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get cash count pending ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    public boolean isExistInInterval(String nik, String branch, String type) {
        return cashCountService.existInInterval(nik, 180, branch, type);
    }
}
