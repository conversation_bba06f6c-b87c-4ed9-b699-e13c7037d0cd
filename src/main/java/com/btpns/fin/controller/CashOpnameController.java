package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.RequestHelper;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.CashOpname;
import com.btpns.fin.model.request.CashOpnameSubmitRequest;
import com.btpns.fin.model.response.CashOpnameDetailResponse;
import com.btpns.fin.model.response.CheckMonthlyCOPResponse;
import com.btpns.fin.model.response.InputTransactionResponse;
import com.btpns.fin.service.CMAConfigService;
import com.btpns.fin.service.CashOpnameService;
import com.btpns.fin.service.EmailService;
import com.btpns.fin.service.ManagementOfficerService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;


import java.io.IOException;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value ="/v1/")
@CrossOrigin("*")
public class CashOpnameController {
    private static final Logger logger = LoggerFactory.getLogger(CashOpnameController.class);
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    CashOpnameService cashOpnameService;

    @Autowired
    ManagementOfficerService managementOfficerService;
    @Autowired
    CMAConfigService cmaConfigService;
    @Autowired
    EmailService emailService;
    @Autowired
    RequestHelper requestHelper;
    
    @PostMapping(value = "cash-opname")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> submitCashOpname(@RequestHeader("Authorization") String authorization,
                                                                  @RequestBody CashOpnameSubmitRequest request) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< submitCashOpname with request {} from {}", gson.toJson(request), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> submitCashOpname Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), request.getBranchId())){
                if (isExistInInterval(profile.getProfile().getPreferred_username(), request.getBranchId())){
                    logger.info("Response >>> postCashCount : {}", HttpStatus.TOO_MANY_REQUESTS);
                    return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
                }
                CommonResponse<InputTransactionResponse> submitCashOpname = cashOpnameService.submitCashOpname(profile.getProfile().getPreferred_username(), request);
                logger.info("Response >>> submitCashOpname : {}", gson.toJson(submitCashOpname));

                sendEmailVerificationCashOpname(submitCashOpname.getData().getTransactionId());
                return ResponseEntity.ok(submitCashOpname);
            }else {
                logger.error("Fail to submit Cash Opname, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            
        } catch (Exception e) {
            logger.error("Fail to submit Cash Opname ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    public void sendEmailVerificationCashOpname(String transactionId) {
        try {
            CashOpname cashOpname = requestHelper.getCashOpname(transactionId);
            if (cashOpname != null){
                if (!StringUtils.isEmpty(cashOpname.getNikTeller())) {
                    createRequestAsync(cashOpname.getNikTeller(), cashOpname.getNameTeller(), cashOpname, false);
                }
                if (!StringUtils.isEmpty(cashOpname.getNikBOS())) {
                    createRequestAsync(cashOpname.getNikBOS(), cashOpname.getNameBOS(), cashOpname, false);
                }
                if (!StringUtils.isEmpty(cashOpname.getNikBM())) {
                    createRequestAsync(cashOpname.getNikBM(), cashOpname.getNameBM(), cashOpname, true);
                }
                if (!StringUtils.isEmpty(cashOpname.getNikQA())) {
                    createRequestAsync(cashOpname.getNikQA(), cashOpname.getNameQA(), cashOpname, false);
                }
                if (!StringUtils.isEmpty(cashOpname.getNikQA2())) {
                    createRequestAsync(cashOpname.getNikQA2(), cashOpname.getNameQA2(), cashOpname, false);
                }
                if (!StringUtils.isEmpty(cashOpname.getNikAltTeller())) {
                    createRequestAsync(cashOpname.getNikAltTeller(), cashOpname.getNameAltTeller(), cashOpname, false);
                }
                if (!StringUtils.isEmpty(cashOpname.getNikODH())) {
                    createRequestAsync(cashOpname.getNikODH(), cashOpname.getNameODH(), cashOpname, false);
                }
                if (!StringUtils.isEmpty(cashOpname.getNikSKAI())) {
                    createRequestAsync(cashOpname.getNikSKAI(), cashOpname.getNameSKAI(), cashOpname, false);
                }
                if (!StringUtils.isEmpty(cashOpname.getNikNOM())) {
                    createRequestAsync(cashOpname.getNikNOM(), cashOpname.getNameNOM(), cashOpname, false);
                }
            }
        } catch (Exception e) {
            logger.error("Fail to send email verification Cash Opname ", e);
        }
    }

    public boolean createRequestAsync(String nik, String name, CashOpname cashOpname, boolean isBM) throws IOException {
        return emailService.sendEmail(requestHelper.createEmailHeader(),requestHelper.createSendEmailVerification(nik, name, cashOpname, isBM));
    }
    
    public boolean isExistInInterval(String nik, String branch) {
        return cashOpnameService.existInInterval(nik, 180, branch);
    }
    @GetMapping(value = "/cash-opname/{transactionId}")
    public ResponseEntity<CommonResponse<CashOpnameDetailResponse>> getDetailCashOpname(@RequestHeader("Authorization")String authorization,
                                                                                @PathVariable String transactionId
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getDetailCashOpname transactionId {} from {}",transactionId,getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getDetailCashOpname Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<CashOpnameDetailResponse> cashOpnameDetail = cashOpnameService.cashOpnameDetail(profile.getProfile(), transactionId);
            logger.info("Response >>> getDetailCashOpname : {}", gson.toJson(cashOpnameDetail));
            return ResponseEntity.ok(cashOpnameDetail);
        }  catch (Exception e) {
            logger.error("Fail to get getDetailCashOpname ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "cash-opname/pending")
    public ResponseEntity<CommonResponse<PendingCashOpname>> getPendingCashOpname(@RequestHeader ("Authorization") String authorization,
                                                                @RequestParam String branch) {
        Token profile = new Token(authorization);
        try {
            logger.info("Received <<< getPendingCashOpname with branch {} from {}", branch, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getPendingCashOpname Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if (managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)) {
                CommonResponse<PendingCashOpname> pendingCashOpname = cashOpnameService.getPendingCashOpname(profile.getProfile().getPreferred_username(), branch);
                logger.info("Response >>> getPendingCashOpname : {}", gson.toJson(pendingCashOpname));
                return ResponseEntity.ok(pendingCashOpname);
            } else {
                logger.error("Fail to get Branch Officer, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get cash opname pending ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/cash-opname/ba")
    public ResponseEntity<CommonResponse<CashOpnameListBAModel>> getListBACashOpname(@RequestHeader("Authorization")String authorization,
                                                                                     @RequestParam(value = "startPeriod", required = false) String startPeriod,
                                                                                     @RequestParam(value = "endPeriod", required = false) String endPeriod,
                                                                                     @RequestParam(required = false) String branchId,
                                                                                     @RequestParam(required = false) String page,
                                                                                     @RequestParam(required = false) String limit
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getListBACashOpname   page {} limit {} from {}", page, limit, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getListBACashOpname Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<CashOpnameListBAModel> listBA = cashOpnameService.cashOpnameListBA(profile.getProfile(), branchId, page,limit, startPeriod, endPeriod);
            logger.info("Response >>> getListBACashOpname : with size {}", listBA.getData().getDetails().size());
            return ResponseEntity.ok(listBA);
        }  catch (Exception e) {
            logger.error("Fail to get getListTellerExchange ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/cash-opname/check-monthly")
    public ResponseEntity<CommonResponse<CheckMonthlyCOPResponse>> checkMonthlyCashOpname(@RequestHeader("Authorization")String authorization,
                                                                                       @RequestParam String branch
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< checkMonthlyCashOpname   branchId {} from {}",branch, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> checkMonthlyCashOpname Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<CheckMonthlyCOPResponse> checkMonthly = cashOpnameService.checkMonthlyCashOpname(profile.getProfile(),branch);
                logger.info("Response >>> checkMonthlyCashOpname : {}", gson.toJson(checkMonthly));
                return ResponseEntity.ok(checkMonthly);
            }else {
                logger.error("Fail to get checkMonthlyCashOpname, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            
        }  catch (Exception e) {
            logger.error("Fail to get checkMonthlyCashOpname ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
