package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.*;
import com.btpns.fin.model.request.SubmitTellerExchangeRequest;
import com.btpns.fin.model.request.TellerExchangeApprovalRequest;
import com.btpns.fin.model.request.TellerExchangeCancelRequest;
import com.btpns.fin.model.response.InputTransactionResponse;
import com.btpns.fin.model.response.TellerExchangeCancelResponse;
import com.btpns.fin.service.CMAConfigService;
import com.btpns.fin.service.ManagementOfficerService;
import com.btpns.fin.service.TellerExchangeService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value ="/v1/te")
@CrossOrigin("*")
public class TellerExchangeController {

    private static final Logger logger = LoggerFactory.getLogger(TellerExchangeController.class);
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    TellerExchangeService tellerExchangeService;

    @Autowired
    ManagementOfficerService managementOfficerService;
    @Autowired
    CMAConfigService cmaConfigService;

    @GetMapping(value = "/teller-exchange/active-transaction")
    public ResponseEntity<CommonResponse<ActiveTellerExchangeModel>> getActiveTellerExchange(@RequestHeader("Authorization")String authorization,
                                                                                             @RequestParam String type,
                                                                                             @RequestParam String branchId
                                                                                 ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getActiveTellerExchange type {} from {}",type,getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getActiveTellerExchange Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<ActiveTellerExchangeModel> activeTellerExchange = tellerExchangeService.activeTellerExchange(profile.getProfile(),type, branchId);
            logger.info("Response >>> getActiveTellerExchange : {}", gson.toJson(activeTellerExchange));
            return ResponseEntity.ok(activeTellerExchange);
        }  catch (Exception e) {
            logger.error("Fail to get getActiveTellerExchange ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @PostMapping(value = "/teller-exchange")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> submitTellerExchange(@RequestHeader("Authorization")String authorization,
                                                                        @RequestBody SubmitTellerExchangeRequest request
                                                                        ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< submitTellerExchange with request {} from {}",gson.toJson(request),getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> submitTellerExchange Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), request.getDetails().getBranchId())){
                if (isExistInInterval(profile.getProfile().getPreferred_username(),  request.getDetails().getBranchId(), request.getType())){
                    logger.info("Response >>> submitTellerExchange : {}", HttpStatus.TOO_MANY_REQUESTS);
                    return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
                }
                CommonResponse<InputTransactionResponse> submitTellerExchange = tellerExchangeService.submitTellerExchange(request,profile.getProfile());
                logger.info("Response >>> submitTellerExchange : with response {}", gson.toJson(submitTellerExchange));

                return ResponseEntity.ok(submitTellerExchange);
            }else {
                logger.error("Fail to submitTellerExchange, Not Authorize Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to submitTellerExchange ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/teller-exchange")
    public ResponseEntity<CommonResponse<TellerExchangeListModel>> getListTellerExchange(@RequestHeader("Authorization")String authorization,
                                                                                         @RequestParam String branch,
                                                                                         @RequestParam(required = false) String status,
                                                                                         @RequestParam String type,
                                                                                         @RequestParam(required = false) String page,
                                                                                         @RequestParam(required = false) String limit
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getListTellerExchange branch {} status {} type {} page {} limit {} from {}",branch, status, type, page, limit, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getListTellerExchange Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<TellerExchangeListModel> listTellerExchange = tellerExchangeService.tellerExchangeList(profile.getProfile(),branch,status,type,page,limit);
                logger.info("Response >>> getListTellerExchange : with size {}", listTellerExchange.getData().getDetails().size());
                return ResponseEntity.ok(listTellerExchange);
            }else {
                logger.error("Fail to get getListTellerExchange, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
           
        }  catch (Exception e) {
            logger.error("Fail to get getListTellerExchange ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/teller-exchange/{transactionId}")
    public ResponseEntity<CommonResponse<TellerExchangeDetailModel>> getDetailTellerExchange(@RequestHeader("Authorization")String authorization,
                                                                                             @PathVariable String transactionId
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getDetailTellerExchange transactionId {} from {}",transactionId,getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getDetailTellerExchange Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<TellerExchangeDetailModel> detailTellerExchange = tellerExchangeService.detailTellerExchange(profile.getProfile(), transactionId);
            logger.info("Response >>> getDetailTellerExchange : {}", gson.toJson(detailTellerExchange));
            return ResponseEntity.ok(detailTellerExchange);
        }  catch (Exception e) {
            logger.error("Fail to get getDetailTellerExchange ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @PostMapping(value = "/teller-exchange/approval")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> approvalTellerExchange(@RequestHeader("Authorization")String authorization,
                                                                         @RequestBody TellerExchangeApprovalRequest request
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< approvalTellerExchange with request {} from {}",gson.toJson(request),getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> approvalTellerExchange Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), request.getBranchId())){
                CommonResponse<InputTransactionResponse> approveTellerExchange = tellerExchangeService.approvalTellerExchange(request,profile.getProfile());
                logger.info("Response >>> approvalTellerExchange : response {}", gson.toJson(approveTellerExchange));
                return ResponseEntity.ok(approveTellerExchange);
            }else {
                logger.error("Fail to approvalTellerExchange, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to approvalTellerExchange ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/teller-exchange/pending/{type}")
    public ResponseEntity<CommonResponse<TellerExchangePendingHT2VModel>> getPendingTEV2HT(@RequestHeader("Authorization")String authorization,
                                                                           @PathVariable String type,
                                                                           @RequestParam String branchId
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getPendingTEV2HT type {}  from {}",type, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getPendingTEV2HT Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<TellerExchangePendingHT2VModel> pendingTEV2HT = tellerExchangeService.pendingV2HT(profile.getProfile(),type, branchId);
            logger.info("Response >>> getPendingTEV2HT : {}", gson.toJson(pendingTEV2HT));
            return ResponseEntity.ok(pendingTEV2HT);
        }  catch (Exception e) {
            logger.error("Fail to get getPendingTEV2HT ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/teller-exchange/pending/KHT")
    public ResponseEntity<CommonResponse<TellerExchangePendingKHTModel>> getPendingKHT(@RequestHeader("Authorization")String authorization,
                                                                       @RequestParam String branchId
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getPendingKHT  from {}",getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getPendingKHT Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<TellerExchangePendingKHTModel> pendingKHT = tellerExchangeService.getPendingKHT(profile.getProfile(), branchId);
            logger.info("Response >>> getPendingKHT : {}", gson.toJson(pendingKHT));
            return ResponseEntity.ok(pendingKHT);
        }  catch (Exception e) {
            logger.error("Fail to get getPendingKHT ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/teller-exchange-balance")
    public ResponseEntity<CommonResponse<TellerExchangeListPendingModel>> getPendingTellerExchange(@RequestHeader("Authorization")String authorization,
                                                                                   @RequestParam String branch,
                                                                                   @RequestParam String type
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getPendingTellerExchange branch {} type {} from {}",branch,type,getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getPendingTellerExchange Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<TellerExchangeListPendingModel> pendingKHT = tellerExchangeService.getListPending(profile.getProfile(), branch, type);
                logger.info("Response >>> getPendingTellerExchange : {}", gson.toJson(pendingKHT));
                return ResponseEntity.ok(pendingKHT);
            }else {
                logger.error("Fail to  getPendingTellerExchange, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to  getPendingTellerExchange ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/teller-exchange/cancel")
    public ResponseEntity<CommonResponse<TellerExchangeCancelResponse>> cancelTellerExchange(@RequestHeader("Authorization")String authorization,
                                                                                 @RequestBody TellerExchangeCancelRequest request
    ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< cancelTellerExchange with request {}  from {}",gson.toJson(request),getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> cancelTellerExchange Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), request.getBranchId())){

                CommonResponse<TellerExchangeCancelResponse> cancelTellerExchange = tellerExchangeService.cancelTellerExchange(profile.getProfile().getPreferred_username(), request);
                logger.info("Response >>> cancelTellerExchange : {}", gson.toJson(cancelTellerExchange));
                return ResponseEntity.ok(cancelTellerExchange);
            }else {
                logger.error("Fail to cancelTellerExchange, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to cancelTellerExchange ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    public boolean isExistInInterval(String nik, String branch, String type) {
        return tellerExchangeService.existInInterval(nik, 120, branch, type);
    }
}
