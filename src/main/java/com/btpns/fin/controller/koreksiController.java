package com.btpns.fin.controller;

import com.btpns.fin.constant.CommonConstant;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.CommonResponse;
import com.btpns.fin.model.KoreksiDetail;
import com.btpns.fin.model.KoreksiTransactionModel;
import com.btpns.fin.model.Token;
import com.btpns.fin.model.request.KoreksiKasBesarRequest;
import com.btpns.fin.model.response.KoreksiKasBesarResponse;
import com.btpns.fin.service.CMAConfigService;
import com.btpns.fin.service.KoreksiService;
import com.btpns.fin.service.ManagementOfficerService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.constant.ResponseStatus.PENDING;
import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value ="/v1/koreksi")
@CrossOrigin("*")
public class koreksiController {
    private static final Logger logger = LoggerFactory.getLogger(koreksiController.class);
    
    @Autowired
    KoreksiService koreksiService;

    @Autowired
    ManagementOfficerService managementOfficerService;
    @Autowired
    CMAConfigService cmaConfigService;
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();
    
    @GetMapping(value = "/kasBesar")
    public ResponseEntity<CommonResponse<KoreksiTransactionModel>> getKoreksiKasBesar(
            @RequestHeader("Authorization") String authorization,
            @RequestParam String branch,
            @RequestParam(required = false) String status) {

        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getKoreksiKasBesar branchId {} status {} from {}", branch, status, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getKoreksiKasBesar Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<KoreksiTransactionModel> response = new CommonResponse<>();
                String inputerNik = new Token(authorization).getUsername();
                response.setType(CommonConstant.GET_LIST_KOREKSI_KAS_BESAR);
                KoreksiTransactionModel koreksiTransaction = koreksiService.getKoreksiTransactionKasBesar(branch, status, inputerNik);

                if (koreksiService.isPendingReversal(branch) != null) {
                    response.setStatus(PENDING.getCode()).setStatusDesc(PENDING.getValue()).setData(koreksiTransaction);
                } else {
                    response.setData(koreksiTransaction);
                }
                logger.info("Response >>> getKoreksiKasBesar : with size {}", response.getData().getDetails().size());
                return ResponseEntity.ok(response);
            }else {
                logger.error("Fail to get Koreksi Kas Besar List, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            
        } catch (Exception e) {
            logger.error("Fail to get Koreksi Kas Besar List ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/headTeller")
    public ResponseEntity<CommonResponse<KoreksiTransactionModel>> getKoreksiHeadTeller(
            @RequestHeader("Authorization") String authorization,
            @RequestParam String branch,
            @RequestParam(required = false) String status) {

        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getKoreksiHeadTeller branchId {} status {} from {}", branch, status, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getKoreksiHeadTeller Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<KoreksiTransactionModel> response = new CommonResponse<>();
                response.setType(CommonConstant.GET_KOREKSI_HEAD_TELLER);
                String inputerNik = new Token(authorization).getUsername();

                KoreksiTransactionModel koreksiTransaction = koreksiService.getKoreksiTransactionHeadTeller(branch, status, inputerNik);
                response.setData(koreksiTransaction);
                logger.info("Response >>> getKoreksiHeadTeller : with size {}", response.getData().getDetails().size());
                return ResponseEntity.ok(response);
            }else {
                logger.error("Fail to get Koreksi Head Teller List, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            
        } catch (Exception e) {
            logger.error("Fail to get Koreksi Head Teller List ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/kas-besar")
    public ResponseEntity<CommonResponse<KoreksiKasBesarResponse>> koreksiKasBesar(
            @RequestHeader("Authorization") String authorization,
            @RequestBody KoreksiKasBesarRequest request) {

        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< koreksiKasBesar request {} from {}", gson.toJson(request), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> koreksiKasBesar Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), request.getBranchId())){
                CommonResponse<KoreksiKasBesarResponse> response = koreksiService.koreksiKasBesar(request, new Token(authorization).getUsername());
                logger.info("Response >>> koreksiKasBesar : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }else {
                logger.error("Fail to post Koreksi Kas Besar, Not Authorize Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            
        }  catch (Exception e) {
            logger.error("Fail to post Koreksi Kas Besar ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/head-teller")
    public ResponseEntity<CommonResponse<KoreksiKasBesarResponse>> koreksiHeadTeller(
            @RequestHeader("Authorization") String authorization,
            @RequestBody KoreksiKasBesarRequest request) {

        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< koreksiHeadTeller request {} from {}", gson.toJson(request), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> koreksiHeadTeller Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), request.getBranchId())){
                CommonResponse<KoreksiKasBesarResponse> response = koreksiService.processReversalHeadTeller(request, new Token(authorization).getUsername());
                logger.info("Response >>> koreksiHeadTeller : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }else {
                logger.error("Fail to post Koreksi Head Teller, Not Authorize Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to post Koreksi Head Teller", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/{transactionId}")
    public ResponseEntity<CommonResponse<KoreksiDetail>> getKoreksiDetail(
            @RequestHeader ("Authorization") String authorization,
            @PathVariable String transactionId) {
        
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getKoreksiDetail transactionId {} from {}", transactionId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getKoreksiDetail Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<KoreksiDetail> response = new CommonResponse<>();
            String inputerNik = new Token(authorization).getUsername();
            response.setType(CommonConstant.GET_KOREKSI_DETAIL);
            KoreksiDetail koreksiDetail = koreksiService.getKoreksiDetail(transactionId);
            response.setData(koreksiDetail);
            logger.info("Response >>> getKoreksiDetail : {}", gson.toJson(response));
            return ResponseEntity.ok(response);
        }  catch (Exception e) {
            logger.error("Fail to get Koreksi Detail ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
}
