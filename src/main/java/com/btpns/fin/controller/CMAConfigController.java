package com.btpns.fin.controller;

import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.CommonResponse;
import com.btpns.fin.model.Token;
import com.btpns.fin.model.entity.CMAConfig;
import com.btpns.fin.model.request.ManagementAlternateRequest;
import com.btpns.fin.service.CMAConfigService;
import com.btpns.fin.service.ManagementOfficerService;
import com.btpns.fin.service.TrxKasBesarService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value ="/v1/cmaConfig")
@CrossOrigin("*")
public class CMAConfigController {

    private static final Logger logger = LoggerFactory.getLogger(cmaController.class);

    @Autowired
    CMAConfigService cmaConfigService;
    @Autowired
    ManagementOfficerService managementOfficerService;
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @PostMapping(value = "/maintenance")
    public ResponseEntity<CommonResponse<String>> submitMaintenance(@RequestHeader("Authorization") String authorization,
                                                                            @RequestParam String status) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< submit maintenance  from {}", getProfile(gson, profile));
            if (managementOfficerService.checkUserAdmin(profile.getProfile().getPreferred_username(), null)) {
                CommonResponse<String> submitMaintenance =  cmaConfigService.submitModeMaintenance(status);
                return ResponseEntity.ok(submitMaintenance);
            } else {
                logger.error("Fail to submit maintenance Forbidden User ");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
        } catch (Exception e) {
            logger.error("Fail to submit maintenance ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
