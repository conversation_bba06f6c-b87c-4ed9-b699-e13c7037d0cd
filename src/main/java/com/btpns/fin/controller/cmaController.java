package com.btpns.fin.controller;

import com.btpns.fin.constant.CommonConstant;
import com.btpns.fin.constant.ResponseStatus;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.RequestHelper;
import com.btpns.fin.model.*;
import com.btpns.fin.model.request.ManagementAlternateRequest;
import com.btpns.fin.model.request.ManagementOfficerRequest;
import com.btpns.fin.service.EmailService;
import com.btpns.fin.service.CMAConfigService;
import com.btpns.fin.service.ManagementOfficerService;
import com.btpns.fin.service.TrxKasBesarService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

import static com.btpns.fin.constant.CommonConstant.*;
import static com.btpns.fin.helper.CommonHelper.getProfile;
import static com.btpns.fin.helper.CommonHelper.validationPeriodRange;

@RestController
@RequestMapping(value ="/v1/general")
@CrossOrigin("*")
public class cmaController {
    private static final Logger logger = LoggerFactory.getLogger(cmaController.class);

    @Autowired
    TrxKasBesarService trxKasBesarService;
    @Autowired
    ManagementOfficerService managementOfficerService;
    @Autowired
    EmailService emailService;
    @Autowired
    RequestHelper requestHelper;
    @Autowired
    CMAConfigService cmaConfigService;
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping(value = "pending-approval")
    public ResponseEntity<CommonResponse<PendingApprovalModel>> getPendingTransaction(@RequestHeader ("Authorization")String authorization,
                                                                      @RequestParam String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getPendingTransaction from {}", getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getPendingTransaction Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<PendingApprovalModel> getPending = trxKasBesarService.getPendingApproval(profile.getProfile().getPreferred_username(), branchId);
            logger.info("Response >>> getPendingTransaction : {}", gson.toJson(getPending));
            return ResponseEntity.ok(getPending);
        }  catch (Exception e) {
            logger.error("Fail to get Pending Transaction ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping(value = "/officer/{nik}")
    public ResponseEntity<CommonResponse<OfficerModel>> getOfficer(@RequestHeader ("Authorization")String authorization,
                                                   @PathVariable("nik") String nik) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getOfficer nik {} from {}", nik, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getOfficer Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if (managementOfficerService.checkUserBranchByNIK(profile.getProfile().getPreferred_username(), nik)) {
                CommonResponse<OfficerModel> getOfficer = trxKasBesarService.getOfficer(nik);
                logger.info("Response >>> getOfficer : {}", gson.toJson(getOfficer));
                return ResponseEntity.ok(getOfficer);
            } else {
                logger.error("Fail to get Officer, Not Authorize Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get Officer ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/branch/{branchId}/officer")
    public ResponseEntity<CommonResponse<BranchOfficerModel>> getBranchOfficer(@RequestHeader ("Authorization")String authorization,
                                                   @PathVariable("branchId") String branchId,
                                                   @RequestParam String roleId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getBranchOfficer branchId {} roleId {} from {}", branchId, roleId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getBranchOfficer Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<BranchOfficerModel> branchOfficerModel = trxKasBesarService.getBranchOfficer(roleId,branchId);
                logger.info("Response >>> getBranchOfficer : {}", gson.toJson(branchOfficerModel));
                return ResponseEntity.ok(branchOfficerModel);
            }else {
                logger.error("Fail to get Branch Officer, Not Authorize Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to get Branch Officer ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/branch")
    public ResponseEntity<CommonResponse<HashMap<String,List<ListBranchModel>>>> getBranch(@RequestHeader ("Authorization")String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getBranch from {}", getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getBranch Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<HashMap<String,List<ListBranchModel>>> listBranchModel = trxKasBesarService.getListBranch();
            logger.info("Response >>> getBranch : with size {}", listBranchModel.getData().size());
            return ResponseEntity.ok(listBranchModel);
        }  catch (Exception e) {
            logger.error("Fail to get List Branch ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/officer")
    public ResponseEntity<CommonResponse<String>> submitManagementOfficer(@RequestHeader ("Authorization")String authorization,
                                                      @RequestBody ManagementOfficerRequest request) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< management from {}", getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> management Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<String> submitManagementOfficer;
            if (StringUtils.isEmpty(request.getOfficer().getOfficerId())){
                logger.error("Fail to submit Management Officer Officer Id empty ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            if (managementOfficerService.checkUserAdmin(profile.getProfile().getPreferred_username(), null)) {
                if (request.getMode().equals(REQUEST_TYPE_NEW)) {
                    if (managementOfficerService.checkOfficer(request.getOfficer().getOfficerId())) {
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
                    }
                } else if (request.getMode().equals(REQUEST_TYPE_UPDATE) || request.getMode().equals(REQUEST_TYPE_DELETE)) {
                    if (!managementOfficerService.checkOfficer(request.getOfficer().getOfficerId())) {
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
                    }
                }
                submitManagementOfficer = managementOfficerService.submitManagementOfficer(profile.getProfile().getPreferred_username(), request);
            } else {
                logger.error("Fail to submit Management Officer Forbidden User ");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            logger.info("Response >>> submit Management Officer : {}", gson.toJson(submitManagementOfficer));
            return ResponseEntity.ok(submitManagementOfficer);
        }  catch (Exception e) {
            logger.error("Fail to submit Management Officer ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/branch/{branchId}/officers")
    public ResponseEntity<CommonResponse<BranchOfficersModel>> getBranchOfficers(@RequestHeader ("Authorization")String authorization,
                                                               @PathVariable("branchId") String branchId,
                                                               @RequestParam String roleId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getBranchOfficer branchId {} roleId {} from {}", branchId, roleId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getBranchOfficer Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<BranchOfficersModel> branchOfficerModel = trxKasBesarService.getBranchOfficers(roleId,branchId);
                logger.info("Response >>> getBranchOfficer : {}", gson.toJson(branchOfficerModel));
                return ResponseEntity.ok(branchOfficerModel);
            }else {
                logger.error("Fail to get Branch Officer, Not Authorize Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to get Branch Officer ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/alternate")
    public ResponseEntity<CommonResponse<String>> submitManagementAlternate(@RequestHeader("Authorization") String authorization,
                                                                            @RequestBody ManagementAlternateRequest request) {
        try {
            Token profile = new Token(authorization);
            String nikInputer = profile.getProfile().getPreferred_username();
            logger.info("Received <<< management alternate from {}", getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> management alternate  Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<String> submitManagementAlternate = new CommonResponse<>();
            if (managementOfficerService.checkUserAdmin(profile.getProfile().getPreferred_username(), null)) {
                if (request.getType().equalsIgnoreCase(REQUEST_TYPE_ALTERNATE_OFFICER)) {
                    if (request.getMode().equalsIgnoreCase(REQUEST_TYPE_NEW)) {
                        CommonResponse<OfficerModel> officerModel = trxKasBesarService.getOfficer(request.getAlternate().getNik());
                        if (!officerModel.getData().getRoles().get(0).getRoleId().equals(BOS_ROLE)) {
                            logger.error("Fail to submit Management Alternate Officer Period Bad Request ");
                            submitManagementAlternate.setType(SUBMIT_MANAGEMENT_ALTERNATE);
                            submitManagementAlternate.setStatus(ResponseStatus.ROLE_IS_NOT_BOS.getCode());
                            submitManagementAlternate.setStatusDesc(ResponseStatus.ROLE_IS_NOT_BOS.getValue());
                            return ResponseEntity.ok(submitManagementAlternate);
                        }
                    }
                    if (!validationPeriodRange(request.getAlternate().getStartPeriod(), request.getAlternate().getEndPeriod())) {
                        logger.error("Fail to submit Management Alternate Officer Period Bad Request ");
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
                    }
                    submitManagementAlternate = managementOfficerService.submitAlternateOfficer(nikInputer, request);
                    if (request.getMode().equalsIgnoreCase(REQUEST_TYPE_NEW) || request.getMode().equalsIgnoreCase(REQUEST_TYPE_UPDATE)){
                        createRequestAsync(request.getAlternate().getNik(), request.getAlternate().getName(), request.getAlternate().getBranchId(), request.getAlternate().getStartPeriod().toString(), request.getAlternate().getEndPeriod().toString());
                    }
                    logger.info("Response >>> submit Management Alternate : {}", gson.toJson(submitManagementAlternate));
                } else if (request.getType().equalsIgnoreCase(REQUEST_TYPE_NONPROSPERA_OFFICER)) {
                    submitManagementAlternate = managementOfficerService.submitOfficerNonProspera(nikInputer, request);
                    logger.info("Response >>> submit Management Alternate Non Prospera : {}", gson.toJson(submitManagementAlternate));
                }
            } else {
                logger.error("Fail to submit Management Alternate Forbidden User ");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            return ResponseEntity.ok(submitManagementAlternate);
        } catch (Exception e) {
            logger.error("Fail to submit Management Alternate ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/user-login")
    public ResponseEntity<CommonResponse<UserLoginModel>> userLogin(@RequestHeader("Authorization") String authorization,
                                                                            @RequestParam String branchId) {
        try {
            Token profile = new Token(authorization);
            String nikInputer = profile.getProfile().getPreferred_username();
            CommonResponse<UserLoginModel> response;
            logger.info("Received <<< userLogin from {}", getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> userLogin Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                response = managementOfficerService.userLogin(nikInputer,branchId);
                logger.info("Response >>> User Login : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }else {
                logger.error("Fail to User Login, Not Authorize Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to User Login ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    public boolean createRequestAsync(String userNik, String name,  String cabangId, String startPeriod, String endPeriod) throws IOException {
        return emailService.sendEmail(requestHelper.createEmailHeader(),requestHelper.createSendEmailAlternate(userNik, name ,cabangId, startPeriod, endPeriod ));
    }
}
