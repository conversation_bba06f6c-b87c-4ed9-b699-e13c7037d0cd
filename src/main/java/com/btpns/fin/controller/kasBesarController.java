package com.btpns.fin.controller;

import com.btpns.fin.constant.CommonConstant;
import com.btpns.fin.constant.ResponseStatus;
import com.btpns.fin.constant.TrxStatus;
import com.btpns.fin.constant.TrxType;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.RequestHelper;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.TrxKasBesar;
import com.btpns.fin.model.request.HeadTellerApprovalReq;
import com.btpns.fin.model.request.InputBeginBalanceRequest;
import com.btpns.fin.model.request.InputHeadTellerRequest;
import com.btpns.fin.model.request.InputMasukHeadTellerRequest;
import com.btpns.fin.model.response.InputTransactionResponse;
import com.btpns.fin.model.response.BalanceStatusResponse;
import com.btpns.fin.model.response.ProgressStatusResponse;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDate;

import static com.btpns.fin.helper.CommonHelper.getProfile;

@RestController
@RequestMapping(value ="/v1/kb")
@CrossOrigin("*")
public class kasBesarController {
    private static final Logger logger = LoggerFactory.getLogger(cmaController.class);

    @Autowired
    TrxKasBesarService trxKasBesarService;

    @Autowired
    KoreksiService koreksiService;

    @Autowired
    CashCountService cashCountService;

    @Autowired
    CashOpnameService cashOpnameService;

    @Autowired
    EmailService emailService;
    
    @Autowired
    ManagementOfficerService managementOfficerService;
    @Autowired
    CMAConfigService cmaConfigService;
    @Autowired
    RequestHelper requestHelper;
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping(value = "/headteller")
    public ResponseEntity<CommonResponse<HeadTellerTransactionModel>> getKBHeadTellerTransaction(@RequestHeader ("Authorization")String authorization,
                                                                                 @RequestParam String branch,
                                                                                 @RequestParam(value = "status", required = false) String status,
                                                                                 @RequestParam(value = "type", required = false) String type,
                                                                                 @RequestParam(value = "transactionId", required = false) String transactionId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getKBHeadTellerTransaction branchId {} status {} type {} from {}", branch, status, type, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getKBHeadTellerTransaction Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<HeadTellerTransactionModel> headTellerTransaction = trxKasBesarService.getHeadTellerTransaction(profile.getProfile(),branch,status,type, transactionId);
                logger.info("Response >>> getKBHeadTellerTransaction : with size {}", headTellerTransaction.getData().getDetails().size());
                return ResponseEntity.ok(headTellerTransaction);
            }else {
                logger.error("Fail to get head teller Transaction, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to get head teller Transaction ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/begin-balance")
    public ResponseEntity<CommonResponse<SaldoAwalModel>> getBeginBalance(@RequestHeader ("Authorization")String authorization,
                                                          @RequestParam String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getBeginBalance from {}", getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getBeginBalance Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<SaldoAwalModel> saldoAwal = trxKasBesarService.getSaldoAwal(profile.getProfile(), branchId);
            logger.info("Response >>> getBeginBalance : {}", gson.toJson(saldoAwal));
            return ResponseEntity.ok(saldoAwal);
        }  catch (Exception e) {
            logger.error("Fail to get begin balance ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/begin-balance")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> inputBeginBalance(@RequestHeader ("Authorization")String authorization,
                                                                      @RequestBody InputBeginBalanceRequest req) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< inputBeginBalance request {} from {}", gson.toJson(req), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> inputBeginBalance Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), req.getBranchId())){
                CommonResponse<InputTransactionResponse> inputTransactionResponse = trxKasBesarService.inputBeginBalance(req,profile.getProfile());
                logger.info("Response >>> inputBeginBalance : {}", gson.toJson(inputTransactionResponse));
                return ResponseEntity.ok(inputTransactionResponse);
            }else {
                logger.error("Fail to Input Begin Balance, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to Input Begin Balance ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/headteller/out")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> inputHeadTellerOut(@RequestHeader("Authorization") String authorization,
                                                                       @RequestBody InputHeadTellerRequest req) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(CommonConstant.SUBMIT_HEADTELLER_OUT);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< inputHeadTellerOut request {} from {}", gson.toJson(req), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> inputHeadTellerOut Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if (managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), req.getBranchId())) {
                if (isExistInInterval(profile.getProfile().getPreferred_username(), req.getBranchId(), TrxType.KHT.getCode())) {
                    logger.info("Response >>> inputHeadTellerOut : {}", HttpStatus.TOO_MANY_REQUESTS);
                    return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
                }
                if (trxKasBesarService.checkPendingTransactionKasBesar(req.getBranchId())) {
                    TrxKasBesar checkBeginBalance = trxKasBesarService.checkBeginBalance(LocalDate.now(), req.getBranchId());
                    boolean overlimit;
                    if (checkBeginBalance == null) {
                        String beginBalance = trxKasBesarService.insertTodayBeginBalance(req.getBranchId(), LocalDate.now(), profile.getProfile(), req.getNikVerification());
                        overlimit = trxKasBesarService.checkBalanceOverlimit(req.getBranchId(), beginBalance);
                        if (overlimit) {
                            createRequestAsync(profile.getProfile().getPreferred_username(), req.getBranchId());
                        }
                    }
                    response = trxKasBesarService.inputKeluarHeadTeller(req, profile.getProfile());
                    logger.info("Response >>> inputHeadTellerOut : {}", gson.toJson(inputTransactionResponse));
                } else {
                    inputTransactionResponse.setRequestId(req.getRequestId());
                    inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
                    inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
                    response.setData(inputTransactionResponse);
                    response.setStatus(TrxStatus.FAILED.getCode());
                    response.setStatusDesc(TrxStatus.FAILED.getValue());
                    logger.error("Fail to Input Head Teller Out, Pending Transaction Kas Besar");
                }
            } else {
                logger.error("Fail to Input Head Teller Out, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to Input Head Teller Out ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
        return ResponseEntity.ok(response);
    }

    @GetMapping(value = "headteller/out/{transactionId}")
    public ResponseEntity<CommonResponse<HeadTellerOutDetail>> getHeadTellerOut(@RequestHeader ("Authorization")String authorization,
                                                                       @PathVariable String transactionId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getHeadTellerOut transactionId {} from {}", transactionId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getHeadTellerOut Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<HeadTellerOutDetail> headTellerOutDetail = trxKasBesarService.getHeadTellerOutDetail(transactionId,profile.getProfile());
            logger.info("Response >>> getHeadTellerOut : {}", gson.toJson(headTellerOutDetail));
            return ResponseEntity.ok(headTellerOutDetail);
        }  catch (Exception e) {
            logger.error("Fail to Input Head Teller Out ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "headteller/in/{transactionId}")
    public ResponseEntity<CommonResponse<HeadTellerInDetail>> getHeadTellerIn(@RequestHeader ("Authorization")String authorization,
                                                              @PathVariable String transactionId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getHeadTellerIn transactionId {} from {}", transactionId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getHeadTellerIn Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            CommonResponse<HeadTellerInDetail> headTellerInDetail = trxKasBesarService.getHeadTellerInDetail(transactionId, profile.getProfile());
            logger.info("Response >>> getHeadTellerIn : {}", gson.toJson(headTellerInDetail));
            return ResponseEntity.ok(headTellerInDetail);
        }  catch (Exception e) {
            logger.error("Fail to Input Head Teller In ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "headteller/out/pending")
    public ResponseEntity<CommonResponse<PendingHeadTellerOut>> getHeadTellerOutPending(@RequestHeader ("Authorization")String authorization,
                                                                       @RequestParam String branch) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< getHeadTellerOutPending branchId {} from {}", branch, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> getHeadTellerOutPending Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<PendingHeadTellerOut> pendingHeadTellerOut = trxKasBesarService.getHeadTellerOutPending(branch, profile.getProfile());
                logger.info("Response >>> getHeadTellerOutPending : {}", gson.toJson(pendingHeadTellerOut));
                return ResponseEntity.ok(pendingHeadTellerOut);
            }else {
                logger.error("Fail to get pending head teller out, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to get pending head teller out", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping(value = "/headteller/approval")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> submitApproval(@RequestHeader ("Authorization")String authorization,
                                                                       @RequestBody HeadTellerApprovalReq req) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< submitApproval request {} from {}", gson.toJson(req), getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> submitApproval Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            boolean isAdmin = false;
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), req.getBranchId())){
                if (managementOfficerService.checkUserAdmin(profile.getProfile().getPreferred_username(), CommonConstant.ADMIN_ROLE)) {
                    if (!req.getStatus().equalsIgnoreCase(TrxStatus.REJECTED.getValue())){
                        logger.error("Fail of Approval, Admin can only rejected ");
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
                    }
                    isAdmin = true;
                    req.setNikVerification(CommonConstant.ADMIN_ROLE);
                }
                CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
                InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
                boolean overlimit = false;

                if (req.getApprovalTransactionId().startsWith(TrxType.RV_DASH.getCode())) {
                    response = koreksiService.koreksiApproval(req, profile.getProfile(), isAdmin);
                } else if (req.getApprovalTransactionId().startsWith(TrxType.KHT.getCode()) || req.getApprovalTransactionId().startsWith(TrxType.MHT.getCode())){
                    response = trxKasBesarService.headTellerApproval(req, profile.getProfile(), isAdmin);
                    overlimit = trxKasBesarService.checkBalanceOverlimit(req.getBranchId(),req.getApprovalTransactionId());
                } else if (req.getApprovalTransactionId().startsWith(TrxType.SAW.getCode()) || req.getApprovalTransactionId().startsWith(TrxType.SAK.getCode()) || req.getApprovalTransactionId().startsWith(TrxType.CC.getCode())){
                    response = cashCountService.cashCountVerification(req, profile.getProfile(), isAdmin);
                    overlimit = trxKasBesarService.checkBalanceOverlimit(req.getBranchId(),req.getApprovalTransactionId());
                } else if (req.getApprovalTransactionId().startsWith(TrxType.CASHOPNAME.getCode())){
                    response = cashOpnameService.cashOpnameVerification(req, profile.getProfile(), isAdmin);
                    overlimit = trxKasBesarService.checkBalanceOverlimit(req.getBranchId(), req.getApprovalTransactionId());
                }
                if (overlimit){
                    createRequestAsync(profile.getProfile().getPreferred_username(),req.getBranchId());
                    inputTransactionResponse.setRequestId(req.getRequestId());
                    inputTransactionResponse.setTransactionId(req.getApprovalTransactionId());
                    inputTransactionResponse.setStatus(ResponseStatus.SUCCESS_WITH_OVERLIMIT.getCode());
                    inputTransactionResponse.setStatusDesc(ResponseStatus.SUCCESS_WITH_OVERLIMIT.getValue());
                    response.setData(inputTransactionResponse);
                    response.setStatus(ResponseStatus.SUCCESS_WITH_OVERLIMIT.getCode());
                    response.setStatusDesc(ResponseStatus.SUCCESS_WITH_OVERLIMIT.getValue());
                }
                logger.info("Response >>> submitApproval : {}", gson.toJson(response));
                return ResponseEntity.ok(response);
            }else {
                logger.error("Fail of Approval, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail of Approval ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/headteller/in/pending")
    public ResponseEntity<CommonResponse<PendingMHTModel>> checkPendingMHT(@RequestHeader ("Authorization")String authorization,
                                                             @RequestParam String branch,
                                                           @RequestParam(required = false) String period ) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< checkPendingMHT branch {} period {} from {}", branch, period, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> checkPendingMHT Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<PendingMHTModel> pendingMHTModel = trxKasBesarService.getPendingMHT(profile.getProfile(), branch, period);
                logger.info("Response >>> checkPendingMHT : {}", gson.toJson(pendingMHTModel));
                return ResponseEntity.ok(pendingMHTModel);
            }else {
                logger.error("Fail to Get Pending MHT, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to Get Pending MHT ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @PostMapping(value = "/headteller/in")
    public ResponseEntity<CommonResponse<InputTransactionResponse>> inputMHT(@RequestHeader ("Authorization")String authorization,
                                                             @RequestBody InputMasukHeadTellerRequest request) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(CommonConstant.SUBMIT_HEADTELLER_IN);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< inputMHT request {} from {}", request, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> inputMHT Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), request.getBranchId())){
                if (isExistInInterval(profile.getProfile().getPreferred_username(), request.getBranchId(), TrxType.MHT.getCode())){
                    logger.info("Response >>> inputMHT : {}", HttpStatus.TOO_MANY_REQUESTS);
                    return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
                }
                if (trxKasBesarService.checkPendingTransactionKasBesar(request.getBranchId())) {
                    response = trxKasBesarService.inputMHT(profile.getProfile(), request);
                    logger.info("Response >>> inputMHT : {}", gson.toJson(response));
                    return ResponseEntity.ok(response);
                } else {
                    inputTransactionResponse.setRequestId(request.getRequestId());
                    inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
                    inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
                    response.setData(inputTransactionResponse);
                    response.setStatus(TrxStatus.FAILED.getCode());
                    response.setStatusDesc(TrxStatus.FAILED.getValue());
                    logger.error("Fail to Input Head Teller Out, Pending Transaction Kas Besar");
                    return ResponseEntity.ok(response);
                }
            } else {
                logger.error("Fail to get Branch Officer, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to Input MHT ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/headteller/balance-validation")
    public ResponseEntity<CommonResponse<HeadTellerBalanceValidation>> headTellerBalanceValidation(@RequestHeader ("Authorization")String authorization,
                                                           @RequestParam String branch) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< headTellerBalanceValidation branch {} from {}", branch, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> headTellerBalanceValidation Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branch)){
                CommonResponse<HeadTellerBalanceValidation> headTellerBalanceValidation = trxKasBesarService.headTellerBalanceValidation(profile.getProfile(), branch);
                logger.info("Response >>> headTellerBalanceValidation : {}", gson.toJson(headTellerBalanceValidation));
                return ResponseEntity.ok(headTellerBalanceValidation);
            }else {
                logger.error("Fail to get Head Teller Balance validation, User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to get Head Teller Balance validation ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    public boolean isExistInInterval(String nik, String branch, String type) {
        return trxKasBesarService.existInInterval(nik, 180, branch, type);
    }
    @GetMapping(value = "/balance-status/{branchId}")
    public ResponseEntity<CommonResponse<BalanceStatusResponse>> balanceStatus(@RequestHeader ("Authorization")String authorization,
                                                                                         @PathVariable String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< balanceStatus branch {} from {}", branchId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> balanceStatus Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if(managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)){
                CommonResponse<BalanceStatusResponse> overlimitBalance = trxKasBesarService.overlimitBranchBalance(profile.getProfile(), branchId);
                logger.info("Response >>> balanceStatus : {}", gson.toJson(overlimitBalance));
                return ResponseEntity.ok(overlimitBalance);
            }else {
                logger.error("Fail to get balanceStatus , User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        }  catch (Exception e) {
            logger.error("Fail to get balanceStatus  ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @GetMapping(value = "/limit/{branchId}")
    public ResponseEntity<CommonResponse<BalanceStatusResponse>> overlimit(@RequestHeader ("Authorization")String authorization,
                                                                       @PathVariable String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< overlimit branch {} from {}", branchId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> overlimit Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if (managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)) {
                CommonResponse<BalanceStatusResponse> overlimitBalance = trxKasBesarService.overlimitBranchBalance(profile.getProfile(), branchId);
                logger.info("Response >>> overlimit : {}", gson.toJson(overlimitBalance));
                return ResponseEntity.ok(overlimitBalance);
            } else {
                logger.error("Fail to get balanceStatus , User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get overlimit Balance  ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    public boolean createRequestAsync(String userNik, String cabangId) throws IOException {
        return emailService.sendEmail(requestHelper.createEmailHeader(),requestHelper.createSendEmailOverlimit(userNik, cabangId));
    }

    @GetMapping(value = "/progressStatus/{branchId}")
    public ResponseEntity<CommonResponse<ProgressStatusResponse>> indicatorProgress(@RequestHeader ("Authorization")String authorization,
                                                                            @PathVariable String branchId) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< indicatorProgress branch {} from {}", branchId, getProfile(gson, profile));
            if (cmaConfigService.checkModeMaintenance()){
                logger.info("Response >>> indicatorProgress Maintenance : {}", HttpStatus.LOCKED);
                return ResponseEntity.status(HttpStatus.LOCKED).build();
            }
            if (managementOfficerService.checkUserBranch(profile.getProfile().getPreferred_username(), branchId)) {
                CommonResponse<ProgressStatusResponse> progressStatus = trxKasBesarService.getIndicatorProgress(branchId);
                logger.info("Response >>> indicatorProgress : {}", gson.toJson(progressStatus));
                return ResponseEntity.ok(progressStatus);
            } else {
                logger.error("Fail to get indicatorProgress , User Not Authorize for access Branch ");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
        } catch (Exception e) {
            logger.error("Fail to get indicatorProgress   ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
