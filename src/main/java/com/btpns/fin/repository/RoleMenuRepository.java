package com.btpns.fin.repository;

import com.btpns.fin.model.entity.RoleMenu;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoleMenuRepository  extends JpaRepository<RoleMenu, Long> {

    @Query(value = "Select menuDesc from RoleMenu where roleId = :role")
    List<String> findByRoleId(String role);

}
