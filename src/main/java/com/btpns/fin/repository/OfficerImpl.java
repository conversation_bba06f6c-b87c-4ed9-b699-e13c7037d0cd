package com.btpns.fin.repository;

import com.btpns.fin.constant.CommonConstant;
import com.btpns.fin.model.ListOfficerModel;
import com.btpns.fin.model.OfficersDetailModel;
import com.btpns.fin.model.RolesModel;
import com.btpns.fin.model.UserOfficerModel;
import com.btpns.fin.model.entity.Officer;
import com.btpns.fin.model.entity.OfficerNR;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Repository
public class OfficerImpl implements OfficerInterface {
    @Autowired
    OfficerNRRepository officerNRRepository;
    @Autowired
    OfficerRepository officerRepository;
    @Value("${officer.database}")
    private String officerDatabase;

    @Override
    public List<RolesModel> getRoles(String nik) {
        if (officerDatabase.equals(CommonConstant.DB_OFFICER)) {
            return officerRepository.getRoles(nik);
        } else if (officerDatabase.equals(CommonConstant.DB_OFFICER_NR)) {
            return officerNRRepository.getRoles(nik);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<ListOfficerModel> getListOfficerByBranchIdAndRoleID(String branchId, Integer roleId) {
        if (officerDatabase.equals(CommonConstant.DB_OFFICER)) {
            return officerRepository.findAllByBranchIdAndRoleID(branchId, roleId);
        } else if (officerDatabase.equals(CommonConstant.DB_OFFICER_NR)) {
            return officerNRRepository.findAllByBranchIdAndRoleID(branchId, roleId);
        }
        return new ArrayList<>();
    }

    @Override
    public String getOfficerNameByNik(String nik, String branchId) {
        if (officerDatabase.equals(CommonConstant.DB_OFFICER)) {
            return officerRepository.getOfficerNameByNik(nik, branchId);
        } else if (officerDatabase.equals(CommonConstant.DB_OFFICER_NR)) {
            return officerNRRepository.getOfficerNameByNik(nik, branchId);
        } else {
            return "";
        }
    }

    @Override
    public UserOfficerModel getOfficerByNikAndRoleIdAndStatusCode(String nik, List<Integer> roleId, int status) {
        UserOfficerModel userOfficerModel = new UserOfficerModel();
        if (officerDatabase.equals(CommonConstant.DB_OFFICER)) {
            Officer officer = officerRepository.getOfficerByNikAndRoleIdAndStatusCode(nik, roleId, status);
            if (officer != null) {
                userOfficerModel.setFromOfficer(officer);
            } else {
                return null;
            }
        } else if (officerDatabase.equals(CommonConstant.DB_OFFICER_NR)) {
            OfficerNR officerNR = officerNRRepository.getOfficerByNikAndRoleIdAndStatusCode(nik, roleId, status);
            if (officerNR != null) {
                userOfficerModel.setFromOfficerNR(officerNR);
            } else {
                return null;
            }
        }
        return userOfficerModel;
    }

    @Override
    public List<OfficersDetailModel> getRoleOfficers(String branchId, List<String> roleId) {
        if (officerDatabase.equals(CommonConstant.DB_OFFICER)) {
            return officerRepository.getAllRoleOfficers(branchId, roleId);
        } else if (officerDatabase.equals(CommonConstant.DB_OFFICER_NR)) {
            return officerNRRepository.getAllRoleOfficers(branchId, roleId);
        }
        return new ArrayList<>();
    }

    @Override
    public List<ListOfficerModel> getByBranchIdAndRoleIDIn(String branchId, Set<Integer> roleId) {
        if (officerDatabase.equals(CommonConstant.DB_OFFICER)) {
            return officerRepository.findAllByBranchIdAndRoleIDIn(branchId, roleId);
        } else if (officerDatabase.equals(CommonConstant.DB_OFFICER_NR)) {
            return officerNRRepository.findAllByBranchIdAndRoleIDIn(branchId, roleId);
        }
        return new ArrayList<>();
    }

    @Override
    public List<UserOfficerModel> getOfficerName(List<String> nik) {
        List<UserOfficerModel> listOfficer = new ArrayList<>();
        if (officerDatabase.equals(CommonConstant.DB_OFFICER)) {
            List<Officer> officerList = officerRepository.getOfficerName(nik);
            if (!officerList.isEmpty()) {
                for (Officer officer : officerList) {
                    UserOfficerModel userOfficerModel = new UserOfficerModel();
                    userOfficerModel.setFromOfficer(officer);
                    listOfficer.add(userOfficerModel);
                }
                return listOfficer;
            } else {
                return new ArrayList<>();
            }
        } else if (officerDatabase.equals(CommonConstant.DB_OFFICER_NR)) {
            List<OfficerNR> officerNRList = officerNRRepository.getOfficerName(nik);
            if (!officerNRList.isEmpty()) {
                for (OfficerNR officerNR : officerNRList) {
                    UserOfficerModel userOfficerModel = new UserOfficerModel();
                    userOfficerModel.setFromOfficerNR(officerNR);
                    listOfficer.add(userOfficerModel);
                }
                return listOfficer;
            } else {
                return new ArrayList<>();
            }
        }
        return new ArrayList<>();
    }

    @Override
    public UserOfficerModel getOfficerByNikAndBranchAndRoleIdAndStatusCode(String nik, String branch, Integer roleId, int status) {
        UserOfficerModel userOfficerModel = new UserOfficerModel();
        if (officerDatabase.equals(CommonConstant.DB_OFFICER)) {
            Officer officer = officerRepository.getOfficerByNikAndBranchAndRoleIdAndStatusCode(nik, branch, roleId, status);
            if (officer != null) {
                userOfficerModel.setFromOfficer(officer);
            } else {
                return null;
            }
        } else if (officerDatabase.equals(CommonConstant.DB_OFFICER_NR)) {
            OfficerNR officerNR = officerNRRepository.getOfficerByNikAndBranchAndRoleIdAndStatusCode(nik, branch, roleId, status);
            if (officerNR != null) {
                userOfficerModel.setFromOfficerNR(officerNR);
            } else {
                return null;
            }
        } else {
            return null;
        }
        return userOfficerModel;
    }
}
