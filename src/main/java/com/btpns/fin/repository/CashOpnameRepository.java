package com.btpns.fin.repository;

import com.btpns.fin.model.CashOpnameListBADetailModel;
import com.btpns.fin.model.entity.CashOpname;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Repository
public interface CashOpnameRepository extends JpaRepository<CashOpname, Long> {
    @Query(value = "select cast(max(SUBSTRING(C.transactionId, length(C.transactionId)-1, 2)) as int) from CashOpname C where C.period =?1 and C.branchId = ?2")
    Integer countTransaction(LocalDate date, String branchId);

    @Query("Select count(*) from CashOpname C " +
            "where C.createDateTime between ?1 and ?2 and C.branchId = ?3 and C.nikBOM = ?4  ")
    Integer checkRequestInterval(LocalDateTime startDate, LocalDateTime endDate, String branch, String nik);

    CashOpname findByTransactionId(String transactionId);

    @Query(value = "select Count(C) from CashOpname C " +
            "Join Cabang CB on C.branchId = CB.cabangId " +
            "Left Join Officer inputer on C.nikBOM  = inputer.nik and substring(inputer.mmsCode,2,length(inputer.mmsCode)) = CB.cabangId and inputer.roleID = ?4 " +
            "Left Join Officer verification on C.nikTeller = verification.nik and substring(verification.mmsCode,2,length(verification.mmsCode)) = CB.cabangId and verification.roleID =?5 " +
            "where C.period >= ?1 AND C.period <= ?2 and C.branchId = ?3 and C.status = 'Approved' ")
    Integer getAllListTransaction(LocalDate startPeriod, LocalDate endPeriod, String branchId, Integer bomRole, Integer tellerRole);

    @Query(value = "select new com.btpns.fin.model.CashOpnameListBADetailModel(C.transactionId, C.period, C.totalBalance, C.nikBOM, C.nameBOM, C.nikTeller , C.nameTeller, C.status) from CashOpname C " +
            "Join Cabang CB on C.branchId = CB.cabangId " +
            "where C.period >= ?1 AND C.period <= ?2 and C.branchId = ?3 and C.status = 'Approved' ")
    Page<CashOpnameListBADetailModel> getDetailListBACashOpname(LocalDate startPeriod, LocalDate endPeriod, String branchId, Integer bomRole, Integer tellerRole, Pageable pageable);

    CashOpname findTopByBranchIdAndPeriodGreaterThanEqualAndPeriodLessThanEqualAndStatus(String branchId, LocalDate startDate, LocalDate endDate, String status);

    @Query(value = "Select Count(C) from CashOpname C where C.period = :period  " +
            "and ((C.nikBM  = :nik and C.statusVerificationBM ='Pending') or (C.nikBOS  = :nik and C.statusVerificationBOS ='Pending') or (C.nikQA =:nik and C.statusVerificationQA ='Pending') or (C.nikBOM =:nik and C.statusVerificationBOM  ='Pending') or (C.nikTeller =:nik and C.statusVerificationTeller ='Pending') or (C.nikNOM  = :nik and C.statusVerificationNOM ='Pending') or (C.nikODH  = :nik and C.statusVerificationODH ='Pending') or (C.nikSKAI  = :nik and C.statusVerificationSKAI ='Pending') or (C.nikAltTeller  = :nik and C.statusVerificationAltTeller ='Pending') or (C.nikQA2  = :nik and C.statusVerificationQA2 ='Pending'))")
    Integer cashOpnamePendingVerification(LocalDate period , String nik);
}
