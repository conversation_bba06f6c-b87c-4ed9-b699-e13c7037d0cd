package com.btpns.fin.repository;

import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.OfficerNonProspera;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface OfficerNonProsperaRepository extends JpaRepository<OfficerNonProspera, Long> {
    @Query(value = "Select new com.btpns.fin.model.RolesModel(O.roleId, O.roleName, O.branchId, " +
            " O.name) from OfficerNonProspera O " +
            " where O.nik =:nik and O.activeFlag = true  ")
    List<RolesModel> getRoles(String nik);

    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(O<PERSON>nik,O.name) FROM OfficerNonProspera O " +
            "WHERE O.nik <> '' and O.branchId = :branchId And O.roleId = :roleId and O.activeFlag =true ")
    List<ListOfficerModel> findAllByBranchIdAndRoleID(String branchId, String roleId);

    @Query(value = "select O from OfficerNonProspera O where O.nik in (:nik) and O.activeFlag = true order by O.activeFlag, O.nik")
    List<OfficerNonProspera> getOfficerName(List<String> nik);

    OfficerNonProspera findOfficerNonProsperaById(Long id);
    @Query(value = "SELECT new com.btpns.fin.model.OfficersDetailModel(O.roleId , O.nik,O.name) FROM OfficerNonProspera O " +
            "WHERE O.nik <> '' and O.branchId = :branchId and O.roleId in (:roleId) and O.activeFlag = true ")
    List<OfficersDetailModel> getAllRoleOfficers(String branchId, List<String> roleId);

    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(O.nik,O.name) FROM OfficerNonProspera O " +
            "WHERE O.nik <> '' and O.branchId = :branchId and O.roleId in (:roleId) and O.activeFlag = true ")
    List<ListOfficerModel> findAllByBranchIdAndRoleIDIn(String branchId, List<String> roleId);

    @Query(value = "select count(*) from OfficerNonProspera O where O.nik = case when :nik is null then O.nik else :nik end order by O.id asc ")
    Integer getCountOfficer(String nik);

    @Query(value = "Select new com.btpns.fin.model.OfficerNonProsperaDetailModel(O.id ,O.nik, O.name, O.branchId, C.cabangDesc, O.roleId, O.roleName, O.activeFlag) from OfficerNonProspera O " +
            "left join Cabang C on O.branchId = C.cabangId where O.nik = case when :nik is null then O.nik else :nik end order by O.id asc ")
    Page<OfficerNonProsperaDetailModel> getListOfficer(String nik, Pageable pageable);
    
    @Query(value = "Select O.name From OfficerNonProspera O where O.nik =:nik and O.activeFlag = true")
    String getOfficerNameByNIK(String nik);


}
