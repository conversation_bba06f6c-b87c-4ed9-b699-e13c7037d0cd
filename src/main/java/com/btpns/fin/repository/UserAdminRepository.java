package com.btpns.fin.repository;

import com.btpns.fin.model.ListOfficerModel;
import com.btpns.fin.model.RolesModel;
import com.btpns.fin.model.entity.Employee;
import com.btpns.fin.model.entity.UserAdmin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserAdminRepository extends JpaRepository<UserAdmin, Long> {
    @Query(value = "Select new com.btpns.fin.model.RolesModel(U.role,U.role ,'HO', U.name) " +
            "from UserAdmin U " +
            "where U.nik = :nik and U.activeFlag ='1' ")
    List<RolesModel> getViewer(String nik);
    
    UserAdmin findByNikAndActiveFlag(String nik, String activeFlag);
    UserAdmin findByNikAndRoleAndActiveFlag(String nik, String role, String activeFlag);
}
