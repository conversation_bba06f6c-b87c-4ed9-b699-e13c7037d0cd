package com.btpns.fin.repository;

import com.btpns.fin.model.KoreksiTransactionDetailModel;
import com.btpns.fin.model.TransactionHeadTeller;
import com.btpns.fin.model.TransactionKasBesar;
import com.btpns.fin.model.entity.TrxHeadTeller;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface TrxHeadTellerRepository extends JpaRepository<TrxHeadTeller, String> {

    TrxHeadTeller findByPeriodAndBranchIdAndType(LocalDate period, String branchId, String type);
    TrxHeadTeller findTopByPeriodAndBranchIdAndTypeInAndStatusOrderByCreateDateTimeDesc(LocalDate period, String branchId, Set<String> type, String status);
    TrxHeadTeller findTopByPeriodAndBranchIdAndTypeInAndStatusInOrderByCreateDateTimeDesc(LocalDate period, String branchId, Set<String> type, Set<String> status);
    List<TrxHeadTeller> findAllByPeriodAndBranchIdAndTypeInAndStatusIn(LocalDate period, String branchId, Set<String> type, Set<String> status);

    TrxHeadTeller findByTransactionId(String transactionId);
    @Query(value = "select cast(max(SUBSTRING(TH.transactionId,length(TH.transactionId)-1 ,2)) as int) from TrxHeadTeller TH where TH.period = ?3 AND TH.branchId =?1 And TH.type = ?2 ")
    Integer getLastTrxHeadTellerTransactionId(String branchId,String type,LocalDate period);

    TrxHeadTeller findTopByPeriodGreaterThanEqualAndBranchIdAndTypeAndStatusAndRefIdIsNullOrderByCreateDateTimeDesc(LocalDate period, String branchId,String type,String status);

    TrxHeadTeller findByPeriodGreaterThanEqualAndBranchIdAndTypeAndStatus(LocalDate period,String branchId,String type,String status);
    TrxHeadTeller findByRefId(String transactionId);
    @Query(value = "select new com.btpns.fin.model.KoreksiTransactionDetailModel(HT.transactionId, HT.type, SP.paramDetailDesc, HT.createDateTime, HT.totalAmount, concat( HT.inputer,' - ', HT.inputerName),concat( HT.tellerId,' - ', HT.tellerName), HT.status) " +
            "from TrxHeadTeller AS HT Join SystemParamDetail AS SP on HT.type = SP.paramDetailId " +
            "where HT.period = :period and SP.paramId = '001' and HT.branchId = :branchId and HT.type in('HT2T','T2HT') and HT.status = :status and HT.inputer = :inputerNik")
    List<KoreksiTransactionDetailModel> getListTransactionKoreksi(LocalDate period, String branchId, String status, String inputerNik);


    TrxHeadTeller findByPeriodAndRequestIdAndType(LocalDate period, String requestId,String type);

    @Query("select new com.btpns.fin.model.TransactionHeadTeller(HT,DT) from TrxHeadTeller HT" +
            " Join TrxHTAmountDetail DT on HT.transactionId = DT.transactionId " +
            " where HT.transactionId = ?1")
    TransactionHeadTeller transactionHeadTeller(String transactionId);

    TrxHeadTeller findTopByPeriodAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(LocalDate period, String branchId, String type, String status);

    TrxHeadTeller findTopByPeriodAndBranchIdAndTypeInAndStatusOrderByIdDesc(LocalDate period, String branchId, Set<String> type, String status);

    @Query("Select count(*) from TrxHeadTeller HT " +
            "where HT.createDateTime between ?1 and ?2 and HT.branchId = ?3 and HT.inputer = ?4 and HT.type = ?5 ")
    Integer checkRequestInterval(LocalDateTime startDate, LocalDateTime endDate, String branch, String nik, String type);
    TrxHeadTeller findTopByPeriodAndBranchIdAndTypeInAndStatusAndCreateDateTimeLessThanEqualAndCreateDateTimeGreaterThanEqualOrderByCreateDateTimeDesc(LocalDate period, String branchId, Set<String> type, String status, LocalDateTime timeLess, LocalDateTime timeGreater);
    TrxHeadTeller findTopByPeriodAndBranchIdAndTypeInAndStatusAndCreateDateTimeGreaterThanEqualOrderByCreateDateTimeDesc(LocalDate period, String branchId, Set<String> type, String status, LocalDateTime timeGreater);
    TrxHeadTeller findTopByPeriodGreaterThanEqualAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(LocalDate period, String branchId,String type,String status);

}
