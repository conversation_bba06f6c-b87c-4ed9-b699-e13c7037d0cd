package com.btpns.fin.repository;

import org.springframework.beans.factory.annotation.Value;
import retrofit2.Call;
import retrofit2.http.*;

public interface ILDAPServerRepository {


    @FormUrlEncoded
    @POST("token")
    public Call<Object> sendLDAP(@Field("client_id") String clientId,
                                 @Field("client_secret") String clientSecret,
                                 @Field("grant_type") String grantType,
                                 @Field("username") String username,
                                 @Field("password") String password,
                                 @Field("scope") String scope);
}
