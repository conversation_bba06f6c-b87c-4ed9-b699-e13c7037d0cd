package com.btpns.fin.repository;

import com.btpns.fin.model.AlternateDetailModel;
import com.btpns.fin.model.AlternateListModel;
import com.btpns.fin.model.entity.AlternateRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface AlternateRoleRepository extends JpaRepository<AlternateRole, String> {
    AlternateRole findByNikAndStartPeriodLessThanEqualAndEndPeriodGreaterThanEqual(String nik, LocalDate start, LocalDate end);
    AlternateRole findById(Long id);
    @Query(value = "Select new com.btpns.fin.model.AlternateDetailModel(ALT.id, ALT.nik, ALT.name, ALT.alternateRoleId, ALT.alternateRoleName, ALT.branchId, C.cabangDesc, ALT.startPeriod, ALT.endPeriod) from AlternateRole ALT " +
            "left join Cabang C on ALT.branchId = C.cabangId where ALT.nik = case when :nik is null then ALT.nik else :nik end " +
            "order by ALT.id Desc ")
    Page<AlternateDetailModel> getListAlternate(String nik, Pageable pageable);

    @Query(value = "select count(*) from AlternateRole ALT where nik = case when :nik is null then nik else :nik end  ")
    Integer getCountAlternate(String nik);
}
