package com.btpns.fin.repository;

import com.btpns.fin.model.CashCountDetailModel;
import com.btpns.fin.model.HeadTellerTransactionDetailModel;
import com.btpns.fin.model.KoreksiTransactionDetailModel;
import com.btpns.fin.model.TransactionKasBesar;
import com.btpns.fin.model.entity.TrxKasBesar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface TrxKasBesarRepository extends JpaRepository<TrxKasBesar, String> {

    @Query(value = "select KB from TrxKasBesar KB where KB.period >= :period and KB.branchId = :branchId and KB.trxType in (:trxType) and KB.status = :status and (KB.checkerNik = :checkerNik or KB.inputerNik =:inputerNik )")
    List<TrxKasBesar> findAllByStatusAndCheckerNikOrInputerNik(LocalDate period,String branchId,Set<String> trxType, String status,String checkerNik,String inputerNik);
    List<TrxKasBesar> findAll();

    @Query(value = "select cast(max(SUBSTRING(KB.transactionId, length(KB.transactionId)-1, 2)) as int) from TrxKasBesar KB where KB.period =?1 and KB.trxType = ?2 and KB.branchId = ?3")
    Integer countTransaction(LocalDate date,String trxType,String branchId);
    
    @Query(value ="select new com.btpns.fin.model.HeadTellerTransactionDetailModel( KB.transactionId,KB.trxType ,SP.paramDetailDesc,KB.createDateTime, KB.totalAmount, KB.inputerNik, KB.inputerName, KB.checkerNik, KB.checkerName, KB.status) from TrxKasBesar AS KB " +
            "Join SystemParamDetail SP on KB.trxType = SP.paramDetailId " +
            "Left Join CashOpname CO on KB.transactionId = CO.transactionId " +
            "where KB.period >= :endPeriod and KB.period <= :period " +
            "and KB.trxType in ('KHT','MHT','RV-KHT','RV-MHT','RV-SAW','RV-SAK','RV-CC','SAW','SAK','CC','COP') " +
            "and SP.paramId= :paramId and KB.branchId = case when :branchId = 'HO' and :type ='Verify' then KB.branchId else :branchId end " +
            "and KB.status = coalesce(NULLIF(:status,'') ,KB.status) " +
            "and (('Verify' = :type and ((CO.nikBM  = :nikLogin and CO.statusVerificationBM ='Pending') or (CO.nikBOS  = :nikLogin and CO.statusVerificationBOS ='Pending') or (CO.nikQA =:nikLogin and CO.statusVerificationQA ='Pending') or (CO.nikBOM =:nikLogin and CO.statusVerificationBOM  ='Pending') or (CO.nikTeller =:nikLogin and CO.statusVerificationTeller ='Pending') or (CO.nikNOM  = :nikLogin and CO.statusVerificationNOM ='Pending') or (CO.nikODH  = :nikLogin and CO.statusVerificationODH ='Pending') or (CO.nikSKAI  = :nikLogin and CO.statusVerificationSKAI ='Pending') or (CO.nikAltTeller  = :nikLogin and CO.statusVerificationAltTeller ='Pending') or (CO.nikQA2  = :nikLogin and CO.statusVerificationQA2 ='Pending') or (KB.checkerNik = :nikLogin and KB.trxType != 'COP'))) " +
            "or ('All' = :type and KB.trxType in ('KHT','MHT','RV-MHT','RV-KHT','RV-SAW','RV-SAK','RV-CC','SAW','SAK','CC','COP'))" +
            "or ('Pending' = :type and KB.transactionId = case when :transactionId is null then KB.transactionId else :transactionId end and KB.status ='Pending')) " +
            "and KB.status != 'Success' " +
            "order by KB.createDateTime ASC ")
    List<HeadTellerTransactionDetailModel> headTellerdetail(String paramId, String branchId, LocalDate period,String status, LocalDate endPeriod, String type, String nikLogin, String transactionId);

    @Query(value = "select new com.btpns.fin.model.KoreksiTransactionDetailModel(KB.transactionId, KB.trxType, SP.paramDetailDesc, KB.createDateTime, KB.totalAmount, KB.inputerNik, KB.checkerNik, KB.status) " +
            "from TrxKasBesar AS KB Join SystemParamDetail AS SP on KB.trxType = SP.paramDetailId " +
            "where KB.period = ?1 and SP.paramId = '001' and KB.branchId = ?2 and KB.status = coalesce(NULLIF(?3, ''), KB.status) and KB.trxType in('KHT', 'MHT','SAW','SAK','CC') and KB.inputerNik = ?4")
    List<KoreksiTransactionDetailModel> getListKoreksiTransactionKasBesar(LocalDate period, String branchId, String status, String inputerNik);
    
    TrxKasBesar findByTransactionId(String transactionId);

    TrxKasBesar findTopByPeriodAndBranchIdAndTrxTypeInAndStatus(LocalDate period, String branchId, Set<String> trxType, String status);
    
    TrxKasBesar findFirstByPeriodGreaterThanEqualAndCreateDateTimeLessThanAndBranchIdAndTrxTypeInAndStatusInOrderByCreateDateTimeDesc(LocalDate period, LocalDateTime createdDateTime, String branchId, Set<String> type, Set<String> status);
   
    
    TrxKasBesar findTopByPeriodAndBranchIdAndTrxTypeAndStatusOrderByCreateDateTimeDesc(LocalDate period, String branchId, String trxType, String status);

    TrxKasBesar findTopByPeriodAndBranchIdAndTrxTypeInAndStatusOrderByCreateDateTimeDesc(LocalDate period, String branchId, Set<String> trxType,String status);

    List<TrxKasBesar> findAllByPeriodAndBranchIdAndTrxTypeInAndStatusIn(LocalDate period, String branchId, Set<String> trxType, Set<String> status);
    @Modifying(clearAutomatically = true)
    @Transactional
    @Query("update TrxKasBesar as KB set KB.transactionId = ?1, KB.trxType=?3 where KB.transactionId=?2 ")
    void updateTransactionId(String newTransactionId,String oldTransactionId,String trxType);

    @Query("Select new com.btpns.fin.model.CashCountDetailModel(KB.transactionId, KB.period, CB.cabangId, CB.cabangDesc, KB.checkerNik, OFC.OfficerName, KB.reason) From TrxKasBesar KB " +
            "Join TrxAmountDetail AD on KB.transactionId = AD.transactionId " +
            "Join Cabang CB on KB.branchId = CB.cabangId " +
            "left Join Officer OFC on KB.checkerNik = OFC.nik and substring(OFC.mmsCode,2,length(OFC.mmsCode)) = CB.cabangId and OFC.roleID =?2 " +
            "Where KB.transactionId = ?1 ")
    CashCountDetailModel cashCountDetail(String transactionId, Integer roleId);
    TrxKasBesar findTopByPeriodAndBranchIdAndTrxTypeAndStatusInOrderByCreateDateTimeDesc(LocalDate period, String branchId, String trxType,Set<String> status);

    TrxKasBesar findFirstByPeriodAndBranchIdAndTrxTypeInAndCreateDateTimeLessThanAndStatusInOrderByCreateDateTimeDesc(LocalDate period, String branchId, Set<String> type,LocalDateTime createdDateTime, Set<String> status);

    @Query("select new com.btpns.fin.model.TransactionKasBesar(KB,AD) from TrxKasBesar KB" +
            " Join TrxAmountDetail AD on KB.transactionId = AD.transactionId " +
            " where KB.transactionId = ?1")
    TransactionKasBesar kasBesar(String transactionId);

    @Query(value = "select cast(max(SUBSTRING(KB.transactionId, length(?1)+2, 2)) as int) from TrxKasBesar KB where KB.transactionId like concat('%',?1,'%') ")
    Integer getReversalSequence(String transactionId);

    TrxKasBesar findTopByPeriodAndBranchIdAndTrxTypeAndStatusInAndCreateDateTimeLessThanEqual(LocalDate period, String branchId,String type, Set<String> status, LocalDateTime createdDateTime);


    TrxKasBesar findTopByPeriodGreaterThanEqualAndPeriodLessThanEqualAndBranchIdAndTrxTypeAndStatusOrderByCreateDateTimeDesc(LocalDate startDate, LocalDate endDate, String branchId, String status, String trxType);

    @Query(value = "Select sum(totalTransaction) from (Select Count(*) as totalTransaction from TrxKasBesar KB " +
            "where KB.period = :period and KB.branchId = :branchId and (KB.trxType in (:typeKB) and KB.status ='Approved') " +
            "UNION " +
            "Select Count(*) as totalTransaction from TrxHeadTeller HT " +
            "where HT.period = :period and HT.branchId = :branchId and (HT.type  in (:typeHT)) and HT.status in ('Success','Pending')) as transactionJoin ", nativeQuery = true)
    Integer checkTransactionToday(LocalDate period, String branchId, Set<String> typeKB, Set<String> typeHT);

    TrxKasBesar findTopByPeriodAndBranchIdAndTrxTypeInAndStatusInOrderByCreateDateTimeDesc(LocalDate period, String branchId, Set<String> trxType,Set<String> status);

    @Query("Select count(*) from TrxKasBesar KB " +
            "where KB.createDateTime between ?1 and ?2 and KB.branchId = ?3 and KB.inputerNik = ?4 and KB.trxType = ?5 ")
    Integer checkRequestInterval(LocalDateTime startDate, LocalDateTime endDate, String branch, String nik, String type);

    @Query("Select count(*) from TrxKasBesar KB " +
            "where KB.createDateTime between ?1 and ?2 and KB.branchId = ?3 and KB.inputerNik = ?4 and KB.trxType = ?5 ")
    Integer checkRequestIntervalCashCount(LocalDateTime startDate, LocalDateTime endDate, String branch, String nik, String type);

    TrxKasBesar findTopByPeriodAndBranchIdAndTrxTypeAndStatusInAndCreateDateTimeGreaterThan(LocalDate period, String branchId, String type, Set<String> status, LocalDateTime time);
    TrxKasBesar findTopByPeriodAndBranchIdAndTrxTypeInAndStatusAndCreateDateTimeGreaterThanOrderByCreateDateTimeAsc(LocalDate period, String branchId, Set<String> type, String status, LocalDateTime time);
    TrxKasBesar findTopByBranchIdAndTrxTypeInAndStatusInAndCreateDateTimeLessThanOrderByCreateDateTimeDesc(String branchId, Set<String> trxType, Set<String> status, LocalDateTime time);
    TrxKasBesar findFirstByPeriodGreaterThanEqualAndBranchIdAndTrxTypeInAndCreateDateTimeLessThanAndStatusInOrderByCreateDateTimeDesc(LocalDate period, String branchId, Set<String> type,LocalDateTime createdDateTime, Set<String> status);


}
