package com.btpns.fin.repository;

import com.btpns.fin.model.LimitInsuranceModel;
import com.btpns.fin.model.ListBranchModel;
import com.btpns.fin.model.entity.Cabang;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CabangRepository extends JpaRepository<Cabang, String> {
    @Query(value = "select DISTINCT CB.* from MsCabang CB " +
            "            Left Join MsOfficer OFC on case when OFC.mmsCode like '%HO%' then 'HO' else substring(OFC.mmsCode,2,10) end  = CB.cabangId and OFC.roleID IS NOT NULL and OFC.NIK = :nik " +
            "            Left Join MsOfficerNR ONR on case when ONR.kfoCode is null then ONR.kcsCode else substring(ONR.kfoCode,2,10) end  = CB.cabangId and ONR.roleID IS NOT NULL and ONR.NIK = :nik and ONR.srcSystem = 'PROSPERA' " +
            "            Left Join MsOfficerNonProspera ONP on CB.CabangId  = ONP.BranchId and ONP.nik = :nik and ONP.activeFlag = 1 " +
            "            Where  ONP.NIK =:nik or ((OFC.NIK = :nik and :dbOfficer = 'officer') or (ONR.nik = :nik and :dbOfficer ='officerNR') ) ", nativeQuery = true)
    List<Cabang> getCabangUser(String nik, String dbOfficer);

    @Query(value = "select DISTINCT CB.* from MsCabang CB " +
            "            Left Join MsBranchEmployee BE on CB.CabangId  = BE.branchId" +
            "            Left Join MsEmployeeHierarchy EH on BE.location = EH.location " +
            "            Left Join MsEmployee E on EH.nik = E.nik " +
            "            Where E.nik = :nik ", nativeQuery = true)
    List<Cabang> getCabangUserEmployee(String nik);

    Cabang findByCabangId(String cabangId);

    @Query(value = "select new com.btpns.fin.model.ListBranchModel(C.cabangId,C.cabangDesc) from Cabang C")
    List<ListBranchModel> getListBranch();

    @Query(value = "select new com.btpns.fin.model.LimitInsuranceModel(C.cabangId, C.cabangDesc, C.email, C.totalBalance, C.totalBalanceHT) from Cabang C ")
    List<LimitInsuranceModel> getListLimitInsurance();

}
