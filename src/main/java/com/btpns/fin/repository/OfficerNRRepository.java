package com.btpns.fin.repository;

import com.btpns.fin.model.ListOfficerModel;
import com.btpns.fin.model.OfficersDetailModel;
import com.btpns.fin.model.RolesModel;
import com.btpns.fin.model.entity.OfficerNR;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


@Repository
public interface OfficerNRRepository extends JpaRepository<OfficerNR, Long> {
    @Query(value = "Select new com.btpns.fin.model.RolesModel(cast(O.roleID as string ),SD.paramDetailDesc ,case when O.kfoCode is null then kcsCode " +
            " else substring(O.kfoCode,2,length(O.kfoCode)) end, O.officerName) from OfficerNR O " +
            "Join SystemParamDetail SD on cast(O.roleID as string) = SD.paramDetailId where O.nik =:nik and O.srcSystem = 'PROSPERA' and O.officerStatusDesc ='Active' and O.roleID IS NOT NULL ")
    List<RolesModel> getRoles(String nik);

    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(O.nik,O.officerName) FROM OfficerNR O " +
            "join Cabang CB on substring(O.kfoCode,2,10)  = CB.cabangId WHERE O.nik <> '' and O.srcSystem = 'PROSPERA' and CB.cabangId = :branchId And O.roleID = :roleId and O.officerStatusDesc ='Active' ")
    List<ListOfficerModel> findAllByBranchIdAndRoleID(String branchId, Integer roleId);

    @Query(value = "Select TOP 1 O.OfficerName from MsOfficerNR O where O.nik = :nik and O.srcSystem = 'PROSPERA' and substring(O.kfoCode,2,10)  = :branchId and O.roleId is not null order by O.OfficerStatusCode", nativeQuery = true)
    String getOfficerNameByNik(String nik, String branchId);

    @Query(value = "select * from MsOfficerNR  where nik =:nik and srcSystem = 'PROSPERA' and roleID in (:roleId) and officerStatusCode = :status ", nativeQuery = true)
    OfficerNR getOfficerByNikAndRoleIdAndStatusCode(String nik, List<Integer> roleId, int status);

    @Query(value = "SELECT new com.btpns.fin.model.OfficersDetailModel(cast(O.roleID as string), O.nik,O.officerName) FROM OfficerNR O " +
            "join Cabang CB on substring(O.kfoCode,2,10)  = CB.cabangId WHERE O.nik <> '' and O.srcSystem = 'PROSPERA'  and CB.cabangId = ?1 And cast(O.roleID as string) in (?2) and O.officerStatusDesc ='Active' ")
    List<OfficersDetailModel> getAllRoleOfficers(String branchId, List<String> roleId);

    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(O.nik,O.officerName) FROM OfficerNR O " +
            "join Cabang CB on substring(O.kfoCode,2,10)  = CB.cabangId WHERE O.nik <> '' and O.srcSystem = 'PROSPERA' and CB.cabangId = ?1 And O.roleID in (?2) and O.officerStatusDesc ='Active' ")
    List<ListOfficerModel> findAllByBranchIdAndRoleIDIn(String branchId, Set<Integer> roleId);

    @Query(value = "select O from OfficerNR O where (O.nik in (:nik) or O.loginName in (:nik)) and O.srcSystem = 'PROSPERA' order by O.officerStatusCode, O.nik")
    List<OfficerNR> getOfficerName(List<String> nik);

    @Query(value = "select * from MsOfficerNR  where nik =:nik and srcSystem = 'PROSPERA' and kcsCode = :branch and roleID = :roleId and officerStatusCode = :status ", nativeQuery = true)
    OfficerNR getOfficerByNikAndBranchAndRoleIdAndStatusCode(String nik, String branch, Integer roleId, int status);

    OfficerNR findByOfficerID(int officerId);

    @Modifying
    @Query(value = "INSERT INTO MsOfficerNR (MsOfficerNRID, OfficerID, OfficerCode, OfficerName, NIK, RoleID, RoleName, LoginName, EmailName, OfficerStatusCode, OfficerStatusDesc, KFOCode, KFOName, KCSCode, KCSName, AmtApprovalLimit, SrcSystem, SysPopulate) " +
                   "VALUES (:msOfficerNRId, :officerId, :officerCode, :officerName, :nik, :roleId, :roleName, :loginName, :emailName, :officerStatusCode, :officerStatusDesc, :kfoCode, :kfoName, :kcsCode, :kcsName, :amtApprovalLimit, 'PROSPERA', 'dp.dbo')"
           , nativeQuery = true)
    void newOfficer(Integer msOfficerNRId, String officerId, String officerCode, String officerName, String nik, String roleId, String roleName, String loginName, String emailName, int officerStatusCode, String officerStatusDesc, String kfoCode, String kfoName, String kcsCode, String kcsName, int amtApprovalLimit);

    @Modifying
    @Query(value = "DELETE from MsOfficerNR where OfficerID = :officerId", nativeQuery = true)
    void deleteOfficer(String officerId);
}
