package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxAmountDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;

@Repository
public interface TrxAmountDetailRepository  extends JpaRepository<TrxAmountDetail, String> {
    TrxAmountDetail findByTransactionId(String transactionId);

    @Modifying(clearAutomatically = true)
    @Transactional
    @Query("update TrxAmountDetail as AD set AD.transactionId = ?1 where AD.transactionId=?2")
    void updateTransactionId(String newTransactionId,String oldTransactionId);
}
