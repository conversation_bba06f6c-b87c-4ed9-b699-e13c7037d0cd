package com.btpns.fin.repository;


import com.btpns.fin.model.ListOfficerModel;
import com.btpns.fin.model.OfficersDetailModel;
import com.btpns.fin.model.RolesModel;
import com.btpns.fin.model.UserOfficerModel;

import java.util.List;
import java.util.Set;

public interface OfficerInterface {
   List<RolesModel> getRoles(String nik);

   List<ListOfficerModel> getListOfficerByBranchIdAndRoleID(String branchId, Integer roleId);

   String getOfficerNameByNik(String nik, String branchId);

   UserOfficerModel getOfficerByNikAndRoleIdAndStatusCode(String nik, List<Integer> roleId, int status);

   List<OfficersDetailModel> getRoleOfficers(String branchId, List<String> roleId);

   List<ListOfficerModel> getByBranchIdAndRoleIDIn(String branchId, Set<Integer> roleId);

   List<UserOfficerModel> getOfficerName(List<String> nik);

   UserOfficerModel getOfficerByNikAndBranchAndRoleIdAndStatusCode(String nik, String branch, Integer roleId, int status);
}
