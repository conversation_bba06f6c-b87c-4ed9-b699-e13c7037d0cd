package com.btpns.fin.repository;

import com.btpns.fin.model.PendingHTTransactionModel;
import com.btpns.fin.model.PendingTransactionHTModel;
import com.btpns.fin.model.entity.TrxPendingHeadTeller;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface TrxPendingHeadTellerRepository extends JpaRepository<TrxPendingHeadTeller, String> {
    @Query(value = "select cast(max(SUBSTRING(TP.transactionId,length(TP.transactionId)-1 ,2)) as int) from TrxPendingHeadTeller TP where TP.period = ?3 AND TP.branchId =?1 And TP.type = ?2 ")
    Integer getLastTransactionId(String branchId,String type,LocalDate period);

    TrxPendingHeadTeller findTopByPeriodAndBranchIdAndTypeOrderByCreateDateTimeDesc(LocalDate period, String branchId, String type);

    @Query(value = "delete from TrxPendingHeadTeller where period= :date and branchId= :branchId and type in (:type) and requestId in (:requestId) ")
    @Modifying
    void deleteTrxPendingHeadTeller (LocalDate date,String branchId,List<String> type, List<String> requestId);
    @Modifying
    void deleteTrxPendingHeadTellersByPeriodAndBranchIdAndTypeIn(LocalDate period, String branchId, List<String> type);

    @Query(value = "Select new com.btpns.fin.model.PendingTransactionHTModel(TP.period,TP.createDateTime,TP.branchId,TP.transactionId,C.cabangDesc,TP.type" +
            ",SP.paramDetailDesc,TP.tellerId,TP.amountDetail) from TrxPendingHeadTeller TP " +
            " Join Cabang C on TP.branchId = C.cabangId " +
            " Join SystemParamDetail SP on TP.type = SP.paramDetailId and SP.paramId='001' " +
            " where TP.period =?1 and TP.branchId = ?2 and TP.type =?3 and TP.status =?4 ")
    List<PendingTransactionHTModel> listPending(LocalDate period, String branchId, String type, String status);

    List<TrxPendingHeadTeller> findAllByPeriodAndBranchIdAndType(LocalDate period,String branchId, String type);
    @Query("Select count(*) from TrxPendingHeadTeller PHT " +
            "where PHT.createDateTime between ?1 and ?2 and PHT.branchId = ?3 and PHT.inputer = ?4 and PHT.type = ?5 ")
    Integer checkRequestInterval(LocalDateTime startDate, LocalDateTime endDate, String branch, String nik, String type);
    
    @Query("select new com.btpns.fin.model.PendingHTTransactionModel(HT.type, Count(HT)) FROM TrxPendingHeadTeller HT " +
            " WHERE HT.period = :period and HT.branchId = :branchId and HT.type in (:type) GROUP BY HT.type ")
    List<PendingHTTransactionModel> getPendingTransactionHeadTeller(LocalDate period, String branchId , Set<String> type);
}
