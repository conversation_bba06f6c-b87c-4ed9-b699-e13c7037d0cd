package com.btpns.fin.repository;

import com.btpns.fin.model.entity.TrxTellerExchangeVault;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

@Repository
public interface TrxTellerExchangeVaultRepository extends JpaRepository<TrxTellerExchangeVault, Long> {

    TrxTellerExchangeVault findTopByPeriodAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(LocalDate period, String branchId, String type, String status );
    TrxTellerExchangeVault findTopByPeriodAndBranchIdAndTypeAndStatusInOrderByCreateDateTimeDesc(LocalDate period, String branchId, String type, Set<String> status );

    TrxTellerExchangeVault findByTransactionId(String transactionId);
    @Query(value = "select cast(max(SUBSTRING(TE.transactionId,length(TE.transactionId)-1 ,2)) as int) from TrxTellerExchangeVault TE where TE.period = ?3 AND TE.branchId =?1 And TE.type = ?2 ")
    Integer getLastTrxTellerExchangeVaultTransactionId(String branchId,String type,LocalDate period);

    TrxTellerExchangeVault findByRefId(String transactionId);

    @Query("Select count(*) from TrxTellerExchangeVault TE " +
            "where TE.createDateTime between ?1 and ?2 and TE.branchId = ?3 and TE.inputerNik = ?4 and TE.type = ?5 ")
    Integer checkRequestInterval(LocalDateTime startDate, LocalDateTime endDate, String branch, String nik, String type);

}
