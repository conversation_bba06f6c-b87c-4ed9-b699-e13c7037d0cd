package com.btpns.fin.repository;

import com.btpns.fin.model.entity.CashOpnameDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CashOpnameDetailModelRepository extends JpaRepository<CashOpnameDetail, Long> {

    @Query(value = "select C.RequestId , " +
            " C.TransactionId , " +
            " C.Period , C.BranchId , " +
            " CB.CabangDesc as branchName, " +
            " C.TotalBalance , " +
            " C.OldTotalBalance , " +
            " C.CarryBalance , " +
            " C.TotalPaperBalance , " +
            " C.TotalCoinBalance , " +
            " C.NikTeller ," +
            " C.NameTeller as nameTeller , " +
            " C.StatusVerificationTeller , " +
            " C.DateVerificationTeller  , " +
            " C.NikBOS , " +
            " C.NameBOS as nameBOS," +
            " C.StatusVerificationBOS , " +
            " C.DateVerificationBOS , " +
            " C.NikBOM , " +
            " C.NameBOM as nameBOM," +
            " C.StatusVerificationBOM , " +
            " C.DateVerificationBOM , " +
            " C.NikBM , " +
            " C.NameBM as nameBM," +
            " C.StatusVerificationBM , " +
            " C.DateVerificationBM,   " +
            " BMB.nik as nikBMBranch, " +
            " BMB.fullName as nameBMBranch,  " +
            " C.NikQA , " +
            " C.NameQA as nameQA, " +
            " C.StatusVerificationQA , " +
            " C.DateVerificationQA," +
            " C.NikNOM , " +
            " C.NameNOM as nameNOM, " +
            " C.StatusVerificationNOM , " +
            " C.DateVerificationNOM,  " +
            " C.NikODH , " +
            " C.NameODH as nameODH, " +
            " C.StatusVerificationODH , " +
            " C.DateVerificationODH,  " +
            " C.NikSKAI, " +
            " C.NameSKAI as nameSKAI, " +
            " C.StatusVerificationSKAI , " +
            " C.DateVerificationSKAI,  " +
            " C.NikAltTeller , " +
            " C.NameAltTeller as nameAltTeller, " +
            " C.StatusVerificationAltTeller , " +
            " C.DateVerificationAltTeller,  " +
            " C.NikQA2 , " +
            " C.NameQA2 as nameQA2, " +
            " C.StatusVerificationQA2 , " +
            " C.DateVerificationQA2,  " +
            " C.Status , " +
            " C.Reason , " +
            " C.AmountDetail as balanceDetails from TrxCashOpname C WITH (NOLOCK) " +
            "            Join MsCabang CB WITH (NOLOCK) on C.branchId = CB.cabangId  " +
            "            left join (select BMB.nik,BMB.fullname, EH.Location, BE.branchId  from MsEmployee BMB WITH (NOLOCK)" +
            "            Left Join MsEmployeeHierarchy EH WITH (NOLOCK) on EH.nik = BMB.nik " +
            "            Left Join MsBranchEmployee BE WITH (NOLOCK) on EH.location = BE.location" +
            "            where BE.branchId = :branchId and BMB.Occupation = case when :branchId = '0002' then 'AREA FUNDING BUSINESS HEAD' WHEN :branchId = '0002' THEN 'AUDITOR' else 'BRANCH MANAGER' end and (BMB.StatusEmployeeDesc is null or BMB.StatusEmployeeDesc = 'Active Assignment' ))" +
            "            AS BMB on BMB.branchId = C.branchId " +
            "            where C.transactionId = :transactionId",nativeQuery = true)
    CashOpnameDetail getCashOpnameDetailByTransactionId(String transactionId, String branchId);

}
