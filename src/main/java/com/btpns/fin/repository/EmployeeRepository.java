package com.btpns.fin.repository;

import com.btpns.fin.model.ListOfficerModel;
import com.btpns.fin.model.OfficersDetailModel;
import com.btpns.fin.model.RolesModel;
import com.btpns.fin.model.entity.Employee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface EmployeeRepository  extends JpaRepository<Employee, Long> {
    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(E.nik,E.fullName) FROM Employee E " +
            " WHERE E.occupation = ?1")
    List<ListOfficerModel> findAllByRoleID(String roleId);
    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(E.nik,<PERSON><PERSON>fullName) FROM Employee E " +
            "Join Employee<PERSON>ierarchy EH on E.nik = EH.nik " +
            "Join BranchEmployee BE on EH.location = BE.location " +
            " WHERE E.nik <> '' and (E.occupation = :roleIdBM and BE.branchId = :branchId ) or E.occupation = :roleIdFund ")
    List<ListOfficerModel> findAllByRoleIDBM(String roleIdBM, String roleIdFund, String branchId);

    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(E.nik,E.fullName) FROM Employee E " +
            "Join EmployeeHierarchy EH on E.nik = EH.nik " +
            "Join BranchEmployee BE on EH.location = BE.location " +
            " WHERE E.nik <> '' and E.occupation = :roleIdBM and BE.branchId = :branchId  ")
    List<ListOfficerModel> findAllByRoleIDBOS(String roleIdBM, String branchId);
    
    Employee findByNikAndStatusEmployeeDesc(String nik, String status);

    @Query(value = "SELECT new com.btpns.fin.model.OfficersDetailModel(E.occupation, E.nik,E.fullName) FROM Employee E " +
            " WHERE E.occupation in (?1) and E.nik <> '' ")
    List<OfficersDetailModel> getListRoleHO(List<String> roleId);
    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(E.nik,E.fullName) FROM Employee E " +
            " WHERE E.occupation in (?1)")
    List<ListOfficerModel> findAllQARole(Set<String> roleId);

    @Query(value = "select E from Employee E where E.nik in (:nik) ")
    List<Employee> getEmployeeName(List<String> nik);

    @Query(value = "Select new com.btpns.fin.model.RolesModel(E.occupation,E.occupation ,case when E.occupation = 'QUALITY ASSURANCE ASSESSMENT OFFICER' or E.occupation ='NETWORK OPERATION MANAGER' or E.occupation = 'OPERATION DISTRIBUTION HEAD' or E.occupation ='QUALITY ASSURANCE DEVELOPMENT MANAGER' or E.occupation = 'QUALITY ASSURANCE ASSESSMENT MANAGER' or E.occupation = 'AUDITOR' then 'HO' else BE.branchId end, E.fullName) from Employee E " +
            "join EmployeeHierarchy EH on E.nik = EH.nik " +
            "left join BranchEmployee BE on EH.location = BE.location " +
            "where E.nik = :nik and E.statusEmployeeDesc ='Active Assignment' ")
    List<RolesModel> getRolesEmployee(String nik);
    
}
