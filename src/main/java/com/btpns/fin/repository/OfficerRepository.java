package com.btpns.fin.repository;

import com.btpns.fin.model.ListOfficerModel;
import com.btpns.fin.model.OfficersDetailModel;
import com.btpns.fin.model.entity.Officer;
import com.btpns.fin.model.RolesModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface OfficerRepository extends JpaRepository<Officer, Long> {

    @Query(value = "Select new com.btpns.fin.model.RolesModel(cast(O.roleID as string ),SD.paramDetailDesc ,case when O.mmsCode like 'HO%' then 'HO' " +
            " else substring(O.mmsCode,2,length(O.mmsCode)) end, <PERSON><PERSON>) from Officer O " +
            "Join SystemParamDetail SD on cast(O.roleID as string) = SD.paramDetailId where O.nik =:nik and O.OfficerStatusDesc ='Active' and O.roleID IS NOT NULL ")
    List<RolesModel> getRoles(String nik);

    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(O.nik,O.OfficerName) FROM Officer O " +
            "join Cabang CB on substring(O.mmsCode,2,10)  = CB.cabangId WHERE O.nik <> '' and CB.cabangId = ?1 And O.roleID = ?2 and O.OfficerStatusDesc ='Active' ")
    List<ListOfficerModel> findAllByBranchIdAndRoleID(String branchId, Integer roleId);

    @Query(value = "Select TOP 1 O.OfficerName from MsOfficer O where O.nik = :nik and substring(O.mmsCode,2,10)  = :branchId and O.roleId is not null order by O.OfficerStatusCode", nativeQuery = true)
    String getOfficerNameByNik(String nik, String branchId);

    @Query(value = "select * from MsOfficer  where nik =:nik and roleID in (:roleId) and officerStatusCode = :status ", nativeQuery = true)
    Officer getOfficerByNikAndRoleIdAndStatusCode(String nik, List<Integer> roleId, int status);

    @Query(value = "SELECT new com.btpns.fin.model.OfficersDetailModel(cast(O.roleID as string), O.nik,O.OfficerName) FROM Officer O " +
            "join Cabang CB on substring(O.mmsCode,2,10)  = CB.cabangId WHERE O.nik <> '' and CB.cabangId = ?1 And cast(O.roleID as string) in (?2) and O.OfficerStatusDesc ='Active' ")
    List<OfficersDetailModel> getAllRoleOfficers(String branchId, List<String> roleId);

    @Query(value = "SELECT new com.btpns.fin.model.ListOfficerModel(O.nik,O.OfficerName) FROM Officer O " +
            "join Cabang CB on substring(O.mmsCode,2,10)  = CB.cabangId WHERE O.nik <> '' and CB.cabangId = ?1 And O.roleID in (?2) and O.OfficerStatusDesc ='Active' ")
    List<ListOfficerModel> findAllByBranchIdAndRoleIDIn(String branchId, Set<Integer> roleId);

    @Query(value = "select O from Officer O where O.nik in (:nik) or O.loginName in (:nik) order by O.OfficerStatusCode, O.nik")
    List<Officer> getOfficerName(List<String> nik);

    @Query(value = "select * from MsOfficer  where nik =:nik and mmsCode = :branch and roleID = :roleId and officerStatusCode = :status ", nativeQuery = true)
    Officer getOfficerByNikAndBranchAndRoleIdAndStatusCode(String nik, String branch, Integer roleId, int status);

}
