package com.btpns.fin.repository;

import com.btpns.fin.model.TellerExchangeListDetailModel;
import com.btpns.fin.model.TellerExchangeDetailModel;
import com.btpns.fin.model.TellerExchangeListPendingDetailModel;
import com.btpns.fin.model.entity.TrxTellerExchange;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface TrxTellerExchangeRepository extends JpaRepository<TrxTellerExchange, Long> {

    TrxTellerExchange findTopByPeriodAndBranchIdAndTypeAndStatusInAndInputerNikOrderByCreateDateTimeDesc(LocalDate period, String branchId, String type, Set<String> status, String inputerNik);
    TrxTellerExchange findByTransactionId(String transactionId);
    @Query(value = "select cast(max(SUBSTRING(TE.transactionId,length(TE.transactionId)-1 ,2)) as int) from TrxTellerExchange TE where TE.period = ?3 AND TE.branchId =?1 And TE.type = ?2 ")
    Integer getLastTrxTellerExchangeTransactionId(String branchId,String type,LocalDate period);

    @Query(value = "select new com.btpns.fin.model.TellerExchangeListDetailModel(TE.transactionId, TE.type, SD.paramDetailDesc, TE.createDateTime, TE.totalAmount, TE.inputerNik, TE.verificationNik, TE.status ) from TrxTellerExchange TE " +
            "Join Cabang CB on TE.branchId = CB.cabangId " +
            "Left Join SystemParamDetail SD on TE.type = SD.paramDetailId and SD.paramId ='001' " +
            "Where TE.period =:period and TE.branchId = :branchId and TE.status = coalesce(NULLIF(:status,'') ,TE.status) and TE.type = :type and TE.verificationNik = :nikUser")
    Page<TellerExchangeListDetailModel> getDetailListTellerExchange(LocalDate period, String branchId, String status,  String type, String nikUser ,Pageable pageable);

    @Query(value = "select new com.btpns.fin.model.TellerExchangeDetailModel(TE.requestId, TE.transactionId, TE.branchId, CB.cabangDesc, TE.period, TE.type, TE.fromName, TE.toName, TE.requestFlag, TE.depositFlag, TE.status,TE.inputerNik, TE.verificationNik, TE.totalAmount, TE.totalAmountSpelled, TE.sourceOfFund ,TE.amountDetail) from TrxTellerExchange TE " +
            "Join Cabang CB on TE.branchId = CB.cabangId " +
            "where TE.transactionId = :transactionId ")
    TellerExchangeDetailModel getDetailTellerExchangeByTransactionId(String transactionId);

    @Query(value = "select new com.btpns.fin.model.TellerExchangeListPendingDetailModel(TE.transactionId, TE.type, SD.paramDetailDesc, TE.inputerNik, TE.totalAmount , TE.amountDetail) from TrxTellerExchange TE " +
            "Join Cabang CB on TE.branchId = CB.cabangId " +
            "Left Join SystemParamDetail SD on TE.type = SD.paramDetailId and SD.paramId ='001' " +
            "Where TE.period =?1 and TE.branchId = ?2 and TE.status = coalesce(NULLIF(?3,'') ,TE.status) and TE.type =?4")
    List<TellerExchangeListPendingDetailModel> getDetailListPendingTellerExchange(LocalDate period, String branchId, String status, String type);

    @Query(value = "select Count(TE) from TrxTellerExchange TE " +
            "Join Cabang CB on TE.branchId = CB.cabangId " +
            "Left Join SystemParamDetail SD on TE.type = SD.paramDetailId and SD.paramId ='001' " +
            "Where TE.period =?1 and TE.branchId = ?2 and TE.status = coalesce(NULLIF(?3,'') ,TE.status) and TE.type =?4 and TE.verificationNik = ?5")
    Integer getAllListTransaction(LocalDate period, String branchId, String status, String type, String nikUser);

    @Query("Select count(*) from TrxTellerExchange TE " +
            "where TE.createDateTime between ?1 and ?2 and TE.branchId = ?3 and TE.inputerNik = ?4 and TE.type = ?5 ")
    Integer checkRequestInterval(LocalDateTime startDate, LocalDateTime endDate, String branch, String nik, String type);
}
