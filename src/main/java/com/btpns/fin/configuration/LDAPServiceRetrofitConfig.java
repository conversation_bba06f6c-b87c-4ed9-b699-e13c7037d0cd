package com.btpns.fin.configuration;

import com.btpns.fin.constant.CommonConstant;
import com.btpns.fin.repository.ILDAPServerRepository;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.util.concurrent.TimeUnit;

@Configuration
public class LDAPServiceRetrofitConfig {
    @Bean
    public LDAPServiceConfig ldapServiceConfig() {
        return new LDAPServiceConfig();
    }
    @Bean
    public OkHttpClient getLdapSvcOkHttpClient() {
        return new OkHttpClient.Builder()
                .readTimeout(30000, TimeUnit.MILLISECONDS)
                .writeTimeout(30000, TimeUnit.MILLISECONDS)
                .connectTimeout(30000, TimeUnit.MILLISECONDS)
                .addInterceptor(new LoggingInterceptor(CommonConstant.LOG_LEVEL_HEADERS))
                .build();
    }
    @Bean
    public Retrofit getLdapSvcRetrofit() {
        return new Retrofit.Builder()
                .baseUrl(ldapServiceConfig().getUri())
                .client(getLdapSvcOkHttpClient())
                .addConverterFactory(GsonConverterFactory.create())
                .build();
    }
    @Bean
    public ILDAPServerRepository ldapServerRepository() {
        return getLdapSvcRetrofit().create(ILDAPServerRepository.class);
    }
}
