package com.btpns.fin.configuration;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

public class AmountConfig {
    public String amount() {
        JSONObject jsonC50 = new JSONObject();
        jsonC50.put("id", "c50");
        jsonC50.put("type", "coin");
        jsonC50.put("denom", "50.00");
        JSONObject jsonC100 = new JSONObject();
        jsonC100.put("id", "c100");
        jsonC100.put("type", "coin");
        jsonC100.put("denom", "100.00");
        JSONObject jsonC200 = new JSONObject();
        jsonC200.put("id", "c200");
        jsonC200.put("type", "coin");
        jsonC200.put("denom", "200.00");
        JSONObject jsonC500 = new JSONObject();
        jsonC500.put("id", "c500");
        jsonC500.put("type", "coin");
        jsonC500.put("denom", "500.00");
        JSONObject jsonC1K = new JSONObject();
        jsonC1K.put("id", "c1.000");
        jsonC1K.put("type", "coin");
        jsonC1K.put("denom", "1000.00");
        JSONObject jsonP1K = new JSONObject();
        jsonP1K.put("id", "p1.000");
        jsonP1K.put("type", "paper");
        jsonP1K.put("denom", "1000.00");
        JSONObject jsonP2K = new JSONObject();
        jsonP2K.put("id", "p2.000");
        jsonP2K.put("type", "paper");
        jsonP2K.put("denom", "2000.00");
        JSONObject jsonP5K = new JSONObject();
        jsonP5K.put("id", "p5.000");
        jsonP5K.put("type", "paper");
        jsonP5K.put("denom", "5000.00");
        JSONObject jsonP10K = new JSONObject();
        jsonP10K.put("id", "p10.000");
        jsonP10K.put("type", "paper");
        jsonP10K.put("denom", "10000.00");
        JSONObject jsonP20K = new JSONObject();
        jsonP20K.put("id", "p20.000");
        jsonP20K.put("type", "paper");
        jsonP20K.put("denom", "20000.00");
        JSONObject jsonP50K = new JSONObject();
        jsonP50K.put("id", "p50.000");
        jsonP50K.put("type", "paper");
        jsonP50K.put("denom", "50000.00");
        JSONObject jsonP75K = new JSONObject();
        jsonP75K.put("id", "p75.000");
        jsonP75K.put("type", "paper");
        jsonP75K.put("denom", "75000.00");
        JSONObject jsonP100K = new JSONObject();
        jsonP100K.put("id", "p100.000");
        jsonP100K.put("type", "paper");
        jsonP100K.put("denom", "100000.00");
        JSONArray array = new JSONArray();
        array.add(jsonC50);
        array.add(jsonC100);
        array.add(jsonC200);
        array.add(jsonC500);
        array.add(jsonC1K);
        array.add(jsonP1K);
        array.add(jsonP2K);
        array.add(jsonP5K);
        array.add(jsonP10K);
        array.add(jsonP20K);
        array.add(jsonP50K);
        array.add(jsonP75K);
        array.add(jsonP100K);
        return array.toJSONString();
    }


}
