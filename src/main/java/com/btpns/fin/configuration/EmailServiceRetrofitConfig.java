package com.btpns.fin.configuration;
import com.btpns.fin.constant.CommonConstant;
import com.btpns.fin.repository.IEmailServerRepository;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import java.util.concurrent.TimeUnit;
@Configuration
public class EmailServiceRetrofitConfig {
    @Bean
    public EmailServiceConfig emailServiceConfig() {
        return new EmailServiceConfig();
    }
    @Bean
    public OkHttpClient getEmailSvcOkHttpClient() {
        return new OkHttpClient.Builder()
                .readTimeout(30000, TimeUnit.MILLISECONDS)
                .writeTimeout(30000, TimeUnit.MILLISECONDS)
                .connectTimeout(30000, TimeUnit.MILLISECONDS)
                .addInterceptor(new LoggingInterceptor(CommonConstant.LOG_LEVEL_BODY))
                .build();
    }
    @Bean
    public Retrofit getEmailSvcRetrofit() {
        return new Retrofit.Builder()
                .baseUrl(emailServiceConfig().getUri())
                .client(getEmailSvcOkHttpClient())
                .addConverterFactory(GsonConverterFactory.create())
                .build();
    }
    @Bean
    public IEmailServerRepository emailServerRepository() {
        return getEmailSvcRetrofit().create(IEmailServerRepository.class);
    }
}