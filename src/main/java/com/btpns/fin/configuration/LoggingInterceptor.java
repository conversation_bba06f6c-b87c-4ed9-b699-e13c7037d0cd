package com.btpns.fin.configuration;
import com.btpns.fin.constant.CommonConstant;
import okhttp3.*;
import okio.Buffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;

public class LoggingInterceptor implements Interceptor {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    private String logLevel;

    public LoggingInterceptor(String logLevel) {
        this.logLevel = logLevel;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Buffer requestBufferBody = new Buffer();

        if (request.body() != null) {
            request.body().writeTo(requestBufferBody);
        }
        if (logLevel.equals(CommonConstant.LOG_LEVEL_BODY)) {
            log.info("OkHttp --> Sending request {} Header: {} Body: {}", request.url(), request.headers().toString().replaceAll("\n", " "), requestBufferBody.readUtf8());
        } else if (logLevel.equals(CommonConstant.LOG_LEVEL_HEADERS)) {
            log.info("OkHttp --> Sending request {} Header: {} ", request.url(), request.headers().toString().replaceAll("\n", " "));
        }

        Response response = chain.proceed(request);
        MediaType contentType = response.body().contentType();
        String content = response.body().string();

        if (logLevel.equals(CommonConstant.LOG_LEVEL_BODY)) {
            log.info("OkHttp <-- Received response for {} Headers: {} Body: {}", response.request().url(), response.headers().toString().replaceAll("\n", " "), content);
        } else if (logLevel.equals(CommonConstant.LOG_LEVEL_HEADERS)) {
            log.info("OkHttp <-- Received response for {} Headers: {}", response.request().url(), response.headers().toString().replaceAll("\n", " "));
        }

        ResponseBody wrappedBody = ResponseBody.create(contentType, content);
        return response.newBuilder().body(wrappedBody).build();
    }
}