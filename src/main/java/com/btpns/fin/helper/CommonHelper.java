package com.btpns.fin.helper;

import com.btpns.fin.constant.CommonConstant;
import com.btpns.fin.constant.TrxStatus;
import com.btpns.fin.constant.TrxType;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.CashOpnameSubmitRequest;
import com.btpns.fin.repository.*;
import com.btpns.fin.repository.BranchBalanceRepository;
import com.btpns.fin.repository.HeadTellerBalanceRepository;
import com.btpns.fin.repository.TrxHTAmountDetailRepository;
import com.btpns.fin.repository.TrxHeadTellerRepository;
import com.google.gson.Gson;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.btpns.fin.constant.CommonConstant.*;

public class CommonHelper {

    public static String getProfile(Gson gson, Token token) {
        if (token != null && token.getProfile() != null && token.getProfile().getProfile() != null) {
            return gson.toJson(token.getProfile().getProfile());
        } else if (token != null && token.getProfile() != null && token.getProfile().getPreferred_username() != null) {
            return gson.toJson(token.getProfile().getPreferred_username());
        } else if (token != null && token.getProfile() != null) {
            return gson.toJson(token.getProfile());
        } else if (token != null) {
            return gson.toJson(token);
        }
        return "{Token not found}";
    }
    public static boolean validationDenom(List<AmountDetail> request) {
        if (request.size() == 13) {
            return validateDenom(request);
        }else {
            return false;
        }
    }
    public static boolean validationDenomAndVerification(List<AmountDetail> denom, String nikVerification) {
        if (denom.size() == 13 && !StringUtils.isEmpty(nikVerification)) {
            return validateDenom(denom);
        }else {
            return false;
        }
    }
    public static boolean validationDenomAndVerificationTellerExchange(List<AmountDetail> denom, String nikVerification, String nikInputer) {
        if (denom.size() == 13 && !StringUtils.isEmpty(nikVerification) && !StringUtils.isEmpty(nikInputer)) {
            return validateDenomTellerExchange(denom);
        }else {
            return false;
        }
    }

    public static boolean validationDenomCashOpname(CashOpnameSubmitRequest request) {
        if (request.getBalanceDetails().size() == 13
                && !StringUtils.isEmpty(request.getNikTeller())
                && request.getTotalBalance() != null
                && request.getCarryBalance() != null
                && request.getTotalPaperBalance() != null
                && request.getTotalCoinBalance() != null
                && request.getOldTotalBalance() != null
                && !StringUtils.isEmpty(request.getNikBOM())
                && !StringUtils.isEmpty(request.getBranchId())
                && !StringUtils.isEmpty(request.getPeriod())) {
            return validateDenom(request.getBalanceDetails());
        } else {
            return false;
        }
    }

    private static boolean validateDenom(List<AmountDetail> denom) {
        List<String> mandatoryDenom = Arrays.asList(COIN_50, COIN_100, COIN_200, COIN_500, COIN_1K, PAPER_1K, PAPER_2K, PAPER_5K, PAPER_10K, PAPER_20K, PAPER_50K, PAPER_75K, PAPER_100K);
        Map<String, AmountDetail> mapAmount = denom.stream().collect(Collectors.toMap(AmountDetail::getId, Function.identity()));
        for (String id : mandatoryDenom) {
            AmountDetail amountDetail = mapAmount.get(id);
            if (amountDetail.getType() == null || amountDetail.getDenom() == null || amountDetail.getCount() == null || amountDetail.getTotal() == null) {
                return false;
            }
        }
        return true;
    }
    private static boolean validateDenomTellerExchange(List<AmountDetail> denom) {
        List<String> mandatoryDenom = Arrays.asList(COIN_50, COIN_100, COIN_200, COIN_500, COIN_1K, PAPER_1K, PAPER_2K, PAPER_5K, PAPER_10K, PAPER_20K, PAPER_50K, PAPER_75K, PAPER_100K);
        Map<String, AmountDetail> mapAmount = denom.stream().collect(Collectors.toMap(AmountDetail::getId, Function.identity()));
        for (String id : mandatoryDenom) {
            AmountDetail amountDetail = mapAmount.get(id);
            if (amountDetail.getType() == null || amountDetail.getDenom() == null || amountDetail.getCount() == null || amountDetail.getTotal() == null) {
                return false;
            }
            if (!checkDenomAndTotalBalance(amountDetail.getDenom(), amountDetail.getCount(), amountDetail.getTotal())){
                return false;
            }
        }
        return true;
    }
    public static boolean validateVerification(String nikVerification, String userVerification) {
        return nikVerification != null && (!StringUtils.isEmpty(userVerification) && nikVerification.toUpperCase().equals(userVerification.toUpperCase()) || userVerification.equals(ADMIN_ROLE));
    }

    public static boolean validateDenomHTAmountDetail(TrxHTAmountDetail amountDetail, String verificationNik) {
        if (verificationNik != null &&
                amountDetail.getC50Count() != null &&
                amountDetail.getC50Amount() != null &&
                amountDetail.getC100Count() != null &&
                amountDetail.getC100Amount() != null &&
                amountDetail.getC200Count() != null &&
                amountDetail.getC200Amount() != null &&
                amountDetail.getC500Count() != null &&
                amountDetail.getC500Amount() != null &&
                amountDetail.getC1KCount() != null &&
                amountDetail.getC1KAmount() != null &&
                amountDetail.getP1KCount() != null &&
                amountDetail.getP1KAmount() != null &&
                amountDetail.getP2KCount() != null &&
                amountDetail.getP2KAmount() != null &&
                amountDetail.getP5KCount() != null &&
                amountDetail.getP5KAmount() != null &&
                amountDetail.getP10KCount() != null &&
                amountDetail.getP10KAmount() != null &&
                amountDetail.getP20KCount() != null &&
                amountDetail.getP20KAmount() != null &&
                amountDetail.getP50KCount() != null &&
                amountDetail.getP50KAmount() != null &&
                amountDetail.getP75KCount() != null &&
                amountDetail.getP75KAmount() != null &&
                amountDetail.getP100KCount() != null &&
                amountDetail.getP100KAmount() != null) {
            return true;
        }
        return false;
    }
    public static boolean validateInputHT2V(List<AmountDetail> request, String branchId, String period, Double totalAmount) {
        if (request.size() == 13 && !StringUtils.isEmpty(branchId) && !StringUtils.isEmpty(period)) {
            BalanceModel balance = new BalanceModel();
            balance.setBalanceModel(request);
            if (!balance.getBalance().equals(totalAmount)){
                return false;
            }
            return validateDenom(request);
        }else {
            return false;
        }
    }
    public static boolean validationAddPendingHeadTeller(List<AmountDetail> denom, String nikVerification, Double totalAmount, String period, String branchId) {
        if (denom.size() == 13 && !StringUtils.isEmpty(nikVerification) && !StringUtils.isEmpty(branchId) && !StringUtils.isEmpty(period)) {
            BalanceModel balance = new BalanceModel();
            balance.setBalanceModel(denom);
            if (!balance.getBalance().equals(totalAmount)){
                return false;
            }
            return validateDenom(denom);
        }else {
            return false;
        }
    }
    public static void checkBranchBalance(String branchId, BranchBalanceRepository branchBalanceRepository){
        BranchBalance balance = branchBalanceRepository.findByBranchId(branchId);
        LocalDateTime now = LocalDateTime.now();
        if (balance == null){
            BranchBalance branchBalance = new BranchBalance();
            branchBalance.setBranchId(branchId);
            branchBalance.setCreateDateTime(now);
            branchBalance.setUpdateDateTime(now);
            branchBalance.setC50Count(0);
            branchBalance.setC50Amount(0.0);
            branchBalance.setC100Count(0);
            branchBalance.setC100Amount(0.0);
            branchBalance.setC200Count(0);
            branchBalance.setC200Amount(0.0);
            branchBalance.setC500Count(0);
            branchBalance.setC500Amount(0.0);
            branchBalance.setC1KCount(0);
            branchBalance.setC1KAmount(0.0);
            branchBalance.setP1KCount(0);
            branchBalance.setP1KAmount(0.0);
            branchBalance.setP2KCount(0);
            branchBalance.setP2KAmount(0.0);
            branchBalance.setP5KCount(0);
            branchBalance.setP5KAmount(0.0);
            branchBalance.setP10KCount(0);
            branchBalance.setP10KAmount(0.0);
            branchBalance.setP20KCount(0);
            branchBalance.setP20KAmount(0.0);
            branchBalance.setP50KCount(0);
            branchBalance.setP50KAmount(0.0);
            branchBalance.setP75KCount(0);
            branchBalance.setP75KAmount(0.0);
            branchBalance.setP100KCount(0);
            branchBalance.setP100KAmount(0.0);
            branchBalanceRepository.save(branchBalance);
        }
    }
    public static void checkHTBranchBalance(String branchId, HeadTellerBalanceRepository headTellerBalanceRepository){
        HeadTellerBalance balance = headTellerBalanceRepository.findByBranchId(branchId);
        if (balance == null){
            HeadTellerBalance branchBalance = new HeadTellerBalance();
            branchBalance.setBranchId(branchId);
            branchBalance.setC50Count(0);
            branchBalance.setC50Amount(0.0);
            branchBalance.setC100Count(0);
            branchBalance.setC100Amount(0.0);
            branchBalance.setC200Count(0);
            branchBalance.setC200Amount(0.0);
            branchBalance.setC500Count(0);
            branchBalance.setC500Amount(0.0);
            branchBalance.setC1KCount(0);
            branchBalance.setC1KAmount(0.0);
            branchBalance.setP1KCount(0);
            branchBalance.setP1KAmount(0.0);
            branchBalance.setP2KCount(0);
            branchBalance.setP2KAmount(0.0);
            branchBalance.setP5KCount(0);
            branchBalance.setP5KAmount(0.0);
            branchBalance.setP10KCount(0);
            branchBalance.setP10KAmount(0.0);
            branchBalance.setP20KCount(0);
            branchBalance.setP20KAmount(0.0);
            branchBalance.setP50KCount(0);
            branchBalance.setP50KAmount(0.0);
            branchBalance.setP75KCount(0);
            branchBalance.setP75KAmount(0.0);
            branchBalance.setP100KCount(0);
            branchBalance.setP100KAmount(0.0);
            branchBalance.setTotalAmount(0.0);
            headTellerBalanceRepository.save(branchBalance);
        }
    }

    public static void checkExistingSaldoAkhirHeadTeller(LocalDate date, String branchId, TrxHeadTellerRepository trxHeadTellerRepository, TrxHTAmountDetailRepository trxHTAmountDetailRepository) {
        TrxHeadTeller checkSaldoAkhir = trxHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(date, branchId, TrxType.SKHT.getCode(), TrxStatus.SUCCESS.getValue());
        if (checkSaldoAkhir != null) {
            String newTransactionId = generateTransactionIdHeadTeller(branchId, TrxType.SA.getCode(), date, trxHeadTellerRepository);
            TrxHTAmountDetail detail = trxHTAmountDetailRepository.findByTransactionId(checkSaldoAkhir.getTransactionId());
            checkSaldoAkhir.setTransactionId(newTransactionId);
            checkSaldoAkhir.setType(TrxType.SA.getCode());
            detail.setTransactionId(newTransactionId);
            trxHeadTellerRepository.save(checkSaldoAkhir);
            trxHTAmountDetailRepository.save(detail);
        }
    }

    public static String generateTransactionIdHeadTeller(String branchId, String type, LocalDate period, TrxHeadTellerRepository trxHeadTellerRepository) {

        Integer getLastTransactionId;
        getLastTransactionId = trxHeadTellerRepository.getLastTrxHeadTellerTransactionId(branchId, type, period);
        String transactionNo = String.format("%02d", 1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        if (getLastTransactionId != null) {
            getLastTransactionId += 1;
            transactionNo = String.format("%02d", getLastTransactionId);
        }
        return type + formatter.format(period) + branchId + transactionNo;
    }
    
    public static boolean checkDenomAndTotalBalance(String denom, Integer count, Double total){
        double denomValue =Double.parseDouble(denom);
        double totalCount = denomValue * count;
        return totalCount == total;
    }

    public static void checkExistingSaldoAkhir(LocalDate date, String branchId, TrxKasBesarRepository trxKasBesarRepository, TrxAmountDetailRepository trxAmountDetailRepository) {
        TrxKasBesar checkSaldoAkhir = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeAndStatusOrderByCreateDateTimeDesc(date, branchId, TrxType.SAK.getCode(), TrxStatus.SUCCESS.getValue());
        if (checkSaldoAkhir != null) {
            String newTransactionId = getTransactionId(TrxType.SA.getCode(), date, branchId, trxKasBesarRepository);
            trxKasBesarRepository.updateTransactionId(newTransactionId, checkSaldoAkhir.getTransactionId(), TrxType.SA.getCode());
            trxAmountDetailRepository.updateTransactionId(newTransactionId, checkSaldoAkhir.getTransactionId());
        }
    }

    public static String getTransactionId(String trxType, LocalDate date, String branchId, TrxKasBesarRepository trxKasBesarRepository) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        Integer lastNo = trxKasBesarRepository.countTransaction(date, trxType, branchId);
        String  transactionNo = lastNo != null ? String.format("%02d", lastNo + 1) : null;
        if (transactionNo == null) {
            transactionNo = String.format("%02d", 1);
        }
        return trxType + formatter.format(date) + branchId + transactionNo;
    }


    public static PICModel enrichPICName(String inputerNIK, String tellerNIK, OfficerImpl officerImpl, OfficerNonProsperaRepository officerNonProsperaRepository) {
        PICModel picModel = new PICModel();
        Map<String, String> mapNIK = new HashMap<>();
        if (!StringUtils.isEmpty(inputerNIK)){
            mapNIK.putIfAbsent(inputerNIK.toUpperCase(), inputerNIK.toUpperCase());
        }
        if (!StringUtils.isEmpty(tellerNIK)){
            mapNIK.putIfAbsent(tellerNIK.toUpperCase(), tellerNIK.toUpperCase());
        }
        HashMap<String, String> map = getOfficerMap(new ArrayList(mapNIK.keySet()), officerImpl, officerNonProsperaRepository);
        if (!StringUtils.isEmpty(inputerNIK)){
            picModel.setInputerName(map.get(inputerNIK.toUpperCase()));
        }
        if (!StringUtils.isEmpty(tellerNIK)){
            picModel.setTellerName(map.get(tellerNIK.toUpperCase()));
        }
        return picModel;
    }

    public static HashMap<String, String> getOfficerMap(List<String> listNIK, OfficerImpl officerimpl, OfficerNonProsperaRepository officerNonProsperaRepository) {
        HashMap<String, String> map = new HashMap<>();
        if (!listNIK.isEmpty()) {
            List<OfficerNonProspera> officerNonProsperas = officerNonProsperaRepository.getOfficerName(listNIK);
            if (!officerNonProsperas.isEmpty()) {
                officerNonProsperas.forEach(officer -> {
                    map.putIfAbsent(officer.getNik().toUpperCase(), officer.getName());
                });
            }
            
            List<UserOfficerModel> officers = officerimpl.getOfficerName(listNIK);
            if (!officers.isEmpty()){
                officers.forEach(officer -> {
                    if (officer.getNik() == null) {
                        map.putIfAbsent(officer.getLoginName().toUpperCase(), officer.getOfficerName());
                    } else {
                        map.putIfAbsent(officer.getNik().toUpperCase(), officer.getOfficerName());
                    }
                });
            }
            
            

        }
        return map;
    }

    public static HashMap<String, Employee> getEmployeeMap(List<String> listNIK, EmployeeRepository employeeRepository) {
        HashMap<String, Employee> map = new HashMap<>();
        if (!listNIK.isEmpty()){
            List<Employee> employees = employeeRepository.getEmployeeName(listNIK);
            employees.forEach(employee -> {
                map.putIfAbsent(employee.getNik(), employee);
            });
        }
        return map;
    }

    public static CashOpname enrichCashOpnameOfficer(CashOpname cashOpname, OfficerImpl officerImpl, EmployeeRepository employeeRepository, OfficerNonProsperaRepository officerNonProsperaRepository) {
        Map<String, String> mapNIKOfficer = new HashMap<>();
        Map<String, String> mapNIKEmployee = new HashMap<>();
        mapNIKOfficer.putIfAbsent(cashOpname.getNikTeller(), cashOpname.getNikTeller());
        mapNIKOfficer.putIfAbsent(cashOpname.getNikBOM(), cashOpname.getNikBOM());
        if (cashOpname.getNikBOS() != null) {
            mapNIKOfficer.putIfAbsent(cashOpname.getNikBOS(), cashOpname.getNikBOS());
        }
        if (cashOpname.getNikAltTeller() != null) {
            mapNIKOfficer.putIfAbsent(cashOpname.getNikAltTeller(), cashOpname.getNikAltTeller());
        }
        if (cashOpname.getNikBM() != null) {
            mapNIKEmployee.putIfAbsent(cashOpname.getNikBM(), cashOpname.getNikBM());
        }
        if (cashOpname.getNikQA() != null) {
            mapNIKEmployee.putIfAbsent(cashOpname.getNikQA(), cashOpname.getNikQA());
        }
        if (cashOpname.getNikNOM() != null) {
            mapNIKEmployee.putIfAbsent(cashOpname.getNikNOM(), cashOpname.getNikNOM());
        }
        if (cashOpname.getNikODH() != null) {
            mapNIKEmployee.putIfAbsent(cashOpname.getNikODH(), cashOpname.getNikODH());
        }
        if (cashOpname.getNikSKAI() != null) {
            mapNIKEmployee.putIfAbsent(cashOpname.getNikSKAI(), cashOpname.getNikSKAI());
        }
        if (cashOpname.getNikQA2() != null){
            mapNIKEmployee.putIfAbsent(cashOpname.getNikQA2(), cashOpname.getNikQA2());
        }
        HashMap<String, String> map = getOfficerMap(new ArrayList(mapNIKOfficer.keySet()), officerImpl, officerNonProsperaRepository);
        if (cashOpname.getNikBOS() != null) {
            String officerName = map.get(cashOpname.getNikBOS());
            if (officerName != null) {
                cashOpname.setNameBOS(officerName);
            }
        }
        if (cashOpname.getNikAltTeller() != null) {
            String officerName = map.get(cashOpname.getNikAltTeller());
            if (officerName != null) {
                cashOpname.setNameAltTeller(officerName);
            }
        }
        String tellerName = map.get(cashOpname.getNikTeller());
        if (tellerName != null) {
            cashOpname.setNameTeller(tellerName);
        }
        String bomName = map.get(cashOpname.getNikBOM());
        if (bomName != null) {
            cashOpname.setNameBOM(bomName);
        }

        HashMap<String, Employee> mapEmployee = getEmployeeMap(new ArrayList(mapNIKEmployee.keySet()), employeeRepository);
        if (cashOpname.getNikBM() != null) {
            Employee employee = mapEmployee.get(cashOpname.getNikBM());
            if (employee != null) {
                cashOpname.setNameBM(employee.getFullName());
            }
        }
        if (cashOpname.getNikQA() != null) {
            Employee employee = mapEmployee.get(cashOpname.getNikQA());
            if (employee != null) {
                cashOpname.setNameQA(employee.getFullName());
            }
        }
        if (cashOpname.getNikNOM() != null) {
            Employee employee = mapEmployee.get(cashOpname.getNikNOM());
            if (employee != null) {
                cashOpname.setNameNOM(employee.getFullName());
            }
        }
        if (cashOpname.getNikODH() != null) {
            Employee employee = mapEmployee.get(cashOpname.getNikODH());
            if (employee != null) {
                cashOpname.setNameODH(employee.getFullName());
            }
        }
        if (cashOpname.getNikSKAI() != null) {
            Employee employee = mapEmployee.get(cashOpname.getNikSKAI());
            if (employee != null) {
                cashOpname.setNameSKAI(employee.getFullName());
            }
        }
        if (cashOpname.getNikQA2() != null) {
            Employee employee = mapEmployee.get(cashOpname.getNikQA2());
            if (employee != null) {
                cashOpname.setNameQA2(employee.getFullName());
            }
        }
        return cashOpname;
    }
    public static boolean validationPeriodRange(LocalDate startPeriod , LocalDate endPeriod) {
        if (!StringUtils.isEmpty(startPeriod) && !StringUtils.isEmpty(endPeriod) && startPeriod.isAfter(endPeriod)){
            return false;
        }
        return true;
    }

    public static boolean modeMaintenance(){


        return false;
    }
}
