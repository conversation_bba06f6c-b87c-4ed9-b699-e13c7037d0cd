package com.btpns.fin.helper;

import com.google.gson.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;

import java.lang.reflect.Type;

public class GsonUtil {

    private static GsonUtil sInstance;
    private Gson mGson;

    public static GsonUtil getInstance() {
        if (sInstance == null) {
            sInstance = new GsonUtil();
        }
        return sInstance;
    }

    private GsonUtil() {
        this.mGson = new GsonBuilder()
                .registerTypeAdapter(LocalDate.class, new LocalDateSerDe())
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeSerde())
                .registerTypeAdapter(DateTime.class, new DateTimeSerde())
                .serializeNulls()
                .create();
    }

    public Gson getGson() {
        return mGson;
    }

    private static class LocalDateSerDe implements JsonSerializer<LocalDate>, JsonDeserializer<LocalDate> {

        public LocalDateSerDe() {
        }

        private static final DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");

        @Override
        public LocalDate deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) {
            String value = json.getAsString();
            return LocalDate.parse(value, formatter);
        }

        @Override
        public JsonElement serialize(final LocalDate src, final Type typeOfSrc, final JsonSerializationContext context) {
            String retVal = (src == null) ? "" : formatter.print(src);
            return new JsonPrimitive(retVal);
        }
    }

    private static class LocalDateTimeSerde implements JsonSerializer<LocalDateTime>, JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
            String value = jsonElement.getAsString();
            return ISODateTimeFormat.dateTimeParser().parseDateTime(value).toLocalDateTime();
        }


        @Override
        public JsonElement serialize(LocalDateTime src, Type type, JsonSerializationContext jsonSerializationContext) {
            String retVal = (src == null) ? "" : src.toString();
            return new JsonPrimitive(retVal);
        }
    }

    private static class DateTimeSerde implements JsonSerializer<DateTime>, JsonDeserializer<DateTime> {
        @Override
        public DateTime deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
            String value = json.getAsString();
            return ISODateTimeFormat.dateTimeParser().withOffsetParsed().parseDateTime(value);
        }

        @Override
        public JsonElement serialize(DateTime dateTime, Type type, JsonSerializationContext jsonSerializationContext) {
            String retVal = (dateTime == null) ? "" : dateTime.toString();
            return new JsonPrimitive(retVal);
        }
    }

}
