package com.btpns.fin.helper;

import com.btpns.fin.model.Attachment;
import com.btpns.fin.model.BodyEmail;
import com.btpns.fin.model.entity.Cabang;
import com.btpns.fin.model.entity.CashOpname;
import com.btpns.fin.repository.CabangRepository;
import com.btpns.fin.repository.CashOpnameRepository;
import com.btpns.fin.repository.OfficerRepository;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.btpns.fin.constant.CommonConstant.ENV_PROD;
import static com.btpns.fin.constant.CommonConstant.TYPE_MESSAGING_EMAIL;

@Component
public class RequestHelper {
    private static final int UNIQUE_ID_SIZE = 15;

    private static final String HEADER_EMAIL_API_VERSION = "X-Api-Version";
    private static final String HEADER_CHANNEL_ID = "X-Channel-Id";
    private static final String HEADER_NODE = "X-Node";
    private static final String HEADER_CORRELATION_ID = "X-Correlation-Id";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_STAN_ID = "X-Stan-Id";
    private static final String HEADER_TRANSMISSION_DATE_TIME = "X-Transmission-Date-Time";
    private static final String HEADER_TERMINAL_ID = "X-Terminal-Id";
    private static final String HEADER_API_KEY = "X-Api-Key";
    private static final String DATE_NO_SPACE_PATTERN = "dd-MM-yyyy";
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";
    private static final String EMAIL_SUFFIX = "@mail.btpnsyariah.com";
    private static final String HEADER_TERMINAL_NAME = "X-Terminal-Name";
    private static final String HEADER_ACQ_ID = "X-Acq-Id";
    private static final String HEADER_ORGUNIT_ID = "X-orgUnit-Id";

    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_NO_SPACE_PATTERN);

    @Value("${mail.api.version}")
    private String mailApiVersion;

    @Value("${channel.id}")
    protected String xChannelId;
    @Value("${node}")
    protected String xNode;
    @Value("${terminal.id}")
    protected String terminalId;
    @Value("${stage}")
    private String envStage;


    @Value("${mail.sender}")
    private String emailFrom;
    @Value("${mail.target}")
    private String emailTarget;

    @Value("${mail.cc.enable}")
    private boolean isMailCCEnable;
    @Value("${mail.cc}")
    private String emailCc;

    @Value("${terminal.name}")
    protected String terminalName;
    @Value("${acq.id}")
    private String acqId;
    @Value("${orgunit.id}")
    private String orgunitId;
    @Value("${header.api_key}")
    private String apiKey;
    @Value("${cma.uri}")
    private String linkCMA;

    @Autowired
    CabangRepository cabangRepository;
    @Autowired
    OfficerRepository officerRepository;
    @Autowired
    CashOpnameRepository cashOpnameRepository;

    public Map<String, String> createEmailHeader() {
        Map<String, String> headerMap = createHeaderRequest(UUID.randomUUID().toString());
        headerMap.put(HEADER_EMAIL_API_VERSION, mailApiVersion);
        System.out.println("HEADER");
        return headerMap;
    }

    public Map<String, String> createHeaderRequest(String correlationId) {
        Map<String, String> cosHeader = new HashMap<>();
        cosHeader.put(HEADER_CHANNEL_ID, xChannelId);
        cosHeader.put(HEADER_NODE, xNode);
        cosHeader.put(HEADER_CORRELATION_ID, correlationId);
        cosHeader.put(HEADER_CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        cosHeader.put(HEADER_STAN_ID, generateUniqId());
        cosHeader.put(HEADER_TRANSMISSION_DATE_TIME, getTransactionTime());
        cosHeader.put(HEADER_TERMINAL_ID, terminalId);
        cosHeader.put(HEADER_TERMINAL_NAME, terminalName);
        cosHeader.put(HEADER_ACQ_ID, acqId);
        cosHeader.put(HEADER_ORGUNIT_ID, orgunitId);
        cosHeader.put(HEADER_API_KEY, apiKey);
        return cosHeader;
    }

    private String getTransactionTime() {
        return new LocalDateTime().toString(DATE_TIME_PATTERN);
    }

    private String generateUniqId() {
        return UniqId.newId(UNIQUE_ID_SIZE);
    }

    public BodyEmail createSendEmailOverlimit(String nik,String cabangId) {
        Cabang cabang = cabangRepository.findByCabangId(cabangId);
        BodyEmail bodyEmail = new BodyEmail();
        bodyEmail.setTo(cabang.getEmail());
        bodyEmail.setMessage(createMessage(cabang));
        bodyEmail.setType(TYPE_MESSAGING_EMAIL);
        bodyEmail.setFrom(emailFrom);
        List<Attachment> attachmentList = new ArrayList<Attachment>();
        bodyEmail.setAttachment(attachmentList);
        bodyEmail.setSubject(createSubject(cabang));
        bodyEmail.setCc("");
        bodyEmail.setBcc("");
        return bodyEmail;
    }

    public BodyEmail createSendEmailHTBalanceOverlimit(String branchId) {
        Cabang cabang = cabangRepository.findByCabangId(branchId);
        BodyEmail bodyEmail = new BodyEmail();
        bodyEmail.setTo(cabang.getEmail());
        bodyEmail.setMessage(createMessageEmailHTBalanceOverlimit(cabang));
        bodyEmail.setType(TYPE_MESSAGING_EMAIL);
        bodyEmail.setFrom(emailFrom);
        List<Attachment> attachmentList = new ArrayList<Attachment>();
        bodyEmail.setAttachment(attachmentList);
        bodyEmail.setSubject(createSubjectEmailHTBalanceOverlimit(cabang));
        bodyEmail.setCc("");
        bodyEmail.setBcc("");
        return bodyEmail;
    }

    private String createMessageEmailHTBalanceOverlimit(Cabang cabang) {
        StringBuilder sb = new StringBuilder();
        sb.append("Dear ")
                .append(cabang.getCabangDesc()).append(",").append("<br /><br />")
                .append("Saat ini saldo pada Cash Box anda sedang Overlimit, segera lakukan pengiriman email ke pihak Asuransi.")
                .append("<br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan dimohon untuk tidak melakukan reply.")
                .append("<br /><br />")
                .append("Terima Kasih")
                .append("<br />")
                .append("CMA (Cash Management Activity)");
        return sb.toString();
    }

    private String createSubjectEmailHTBalanceOverlimit(Cabang cabang) {
        LocalDate localDate = LocalDate.now();
        String date = dateTimeFormatter.format(localDate);
        StringBuilder sb = new StringBuilder();
        sb.append("Kelebihan Limit Cash Box ").append(date).append(" ").append(cabang.getCabangDesc());
        return sb.toString();
    }

    public BodyEmail createSendEmailAlternate(String nik, String name, String branchId, String startPeriod, String endPeriod) {
        Cabang cabang = cabangRepository.findByCabangId(branchId);
        BodyEmail bodyEmail = new BodyEmail();
        bodyEmail.setTo(getEmailUsingNik(nik));
        bodyEmail.setCc(emailCc);
        if (cabang != null) {
            bodyEmail.setCc(bodyEmail.getCc().concat(",".concat(cabang.getEmail())));
        }
        bodyEmail.setMessage(createMessageAlternate(name, nik, cabang.getCabangDesc(), startPeriod, endPeriod));
        bodyEmail.setType(TYPE_MESSAGING_EMAIL);
        bodyEmail.setFrom(emailFrom);
        List<Attachment> attachmentList = new ArrayList<Attachment>();
        bodyEmail.setAttachment(attachmentList);

        bodyEmail.setSubject(createSubjectAlternate());

        bodyEmail.setBcc("");
        return bodyEmail;
    }

    public BodyEmail createSendEmailVerification(String nik, String name, CashOpname cashOpname, boolean isBM) {
        Cabang cabang = cabangRepository.findByCabangId(cashOpname.getBranchId());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        String period = formatter.format(cashOpname.getPeriod());
        
        BodyEmail bodyEmail = new BodyEmail();
        bodyEmail.setTo(getEmailUsingNik(nik));
        bodyEmail.setMessage(createMessageVerification(name, cabang.getCabangDesc(), period, cashOpname.getTransactionId(), cashOpname.getStatus(), linkCMA));
        bodyEmail.setType(TYPE_MESSAGING_EMAIL);
        bodyEmail.setFrom(emailFrom);
        List<Attachment> attachmentList = new ArrayList<Attachment>();
        bodyEmail.setAttachment(attachmentList);

        bodyEmail.setSubject(createSubjectVerificationCashOpname());
        bodyEmail.setCc("");
        bodyEmail.setBcc("");
        return bodyEmail;
    }

    public String getEmailUsingNik(String nik) {
        return nik.concat(EMAIL_SUFFIX);
    }

    private String createSubject(Cabang cabang) {
        LocalDate localDate = LocalDate.now();
        String date = dateTimeFormatter.format(localDate);
        StringBuilder sb = new StringBuilder();
        sb.append("Kelebihan Limit Lemari Besi ").append(date).append(" ").append(cabang.getCabangDesc());
        return sb.toString();
    }

    private String createSubjectAlternate() {
        StringBuilder sb = new StringBuilder();
        if (notEnvProduction(envStage)){
            sb.append("[".concat(envStage.toUpperCase()).concat("] "));
        }
        sb.append("[CMA] Notifikasi Pengajuan User Alternate Cash Management Activity KC/KFO");
        return sb.toString();
    }

    private String createSubjectVerificationCashOpname() {
        StringBuilder sb = new StringBuilder();
        if (notEnvProduction(envStage)){
            sb.append("[".concat(envStage.toUpperCase()).concat("] "));
        }
        sb.append("Verifikasi Cash opname");
        return sb.toString();
    }

    private boolean notEnvProduction(String envStage){
        if (ENV_PROD.contains(envStage)){
            return false;
        }
        return true;
    }

    private String createMessage(Cabang cabang) {
        StringBuilder sb = new StringBuilder();
        sb.append("Dear ")
                .append(cabang.getCabangDesc()).append(",").append("<br /><br />")
                .append("Saat ini saldo pada Vault anda sedang Overlimit, segera lakukan pengiriman email ke pihak Asuransi.")
                .append("<br />")
                .append("Email ini merupakan pemberitahuan secara otomatis dan dimohon untuk tidak melakukan reply.")
                .append("<br /><br />")
                .append("Terima Kasih")
                .append("<br />")
                .append("CMA (Cash Management Activity)");
        return sb.toString();
    }

    private String createMessageAlternate(String name, String nik, String branch, String startPeriod, String endPeriod) {
        StringBuilder sb = new StringBuilder();
        sb.append("<b> Dear Rekan - Rekan KC/KFO, </b>")
                .append("<br /><br />")
                .append("Untuk request user alternate sudah dapat dicoba login kembali sesuai tanggal efektifnya.")
                .append("<br />")
                .append("<table>")
                .append("<tr><td>Nama</td><td>: ").append(name).append("</td></tr>")
                .append("<tr><td>NIK</td><td>: ").append(nik).append("</td></tr>")
                .append("<tr><td>Branch</td><td>: ").append(branch).append("</td></tr>")
                .append("<tr><td>Periode tanggal efektif</td><td>: ").append(startPeriod).append(" s/d ").append(endPeriod).append("</td></tr>")
                .append("</table><br /><br />")
                .append("Demikian disampaikan.")
                .append("<br />")
                .append("Terima Kasih")
                .append("<br />")
                .append("CMA Web KC/KFO")
                .append("<br /><br />")
                .append("<font size='2'> Note :")
                .append("<br />")
                .append("Untuk pengajuan mohon melengkapi data dengan mencantumkan Branch, NIK, NAMA, dan Periode Tanggal efektif yang akan dialternatekan guna untuk verifikasi data. Silahkan <NAME_EMAIL>, CC: grup cabang operationnya. </font>")
                .append("<br /><br />")
                .append("<i><font color='blue' size='2'> Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply </font></i>");
        return sb.toString();
    }

    private String createMessageVerification(String name, String branch, String period, String trxId, String trxStatus, String linkCMA) {
        StringBuilder sb = new StringBuilder();
        sb.append("<b> Dear ").append(name).append(", </b>")
                .append("<br /> <br />")
                .append("Berikut informasi transaksi Cash Opname yang telah dilakukan bersama pada ").append(period)
                .append(" dan membutuhkan verifikasi anda dengan detail :")
                .append("<br />")
                .append("<table>")
                .append("<tr><td>Branch name</td><td>: ").append(branch).append("</td></tr>")
                .append("<tr><td>Id Transaksi</td><td>: ").append(trxId).append("</td></tr>")
                .append("<tr><td>Status Verifikasi</td><td>: ").append(trxStatus).append("</td></tr>")
                .append("</table><br />")
                .append("Untuk melakukan verifikasi silahkan mengunjungi CMA Web melalui link berikut ")
                .append("<a href=").append(linkCMA).append(">").append("CMA Web").append("</a>").append(" . ")
                .append("<br />")
                .append("Terima kasih telah menggunakan layanan CMA KC/KFO BTPN Syariah.")
                .append("<br /> <br />")
                .append("Demikian disampaikan.")
                .append("<br />")
                .append("CMA Web KC/KFO.")
                .append("<br /><br />")
                .append("<font size='2'> Note :")
                .append("<br />")
                .append("Dimohon untuk melakukan verifikasi dihari yang sama melalui Menu Verifikasi pada Website CMA KC/KFO.")
                .append("<br />Website CMA hanya bisa di akses di perangkat yang terhubung ke jaringan KC/KFO. </font>")
                .append("<br /><br />")
                .append("<i><font color='blue' size='2'> Email ini merupakan pemberitahuan secara otomatis dan tidak untuk direply </font></i>");
        return sb.toString();        
    }
    
    public CashOpname getCashOpname (String transactionId){
        return cashOpnameRepository.findByTransactionId(transactionId);
    }
}
