package com.btpns.fin.service;

import com.btpns.fin.configuration.AmountConfig;
import com.btpns.fin.constant.*;
import com.btpns.fin.helper.RequestHelper;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.CancelHT2VRequest;
import com.btpns.fin.model.request.InputHT2VRequest;
import com.btpns.fin.model.request.InputHeadTellerPendingRequest;
import com.btpns.fin.model.request.SubmitPendingTransactionRequest;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.btpns.fin.constant.CommonConstant.*;
import static com.btpns.fin.constant.TrxStatus.PENDING;
import static com.btpns.fin.helper.CommonHelper.*;

@Service
public class HeadTellerService {
    private static final Logger logger = LoggerFactory.getLogger(HeadTellerService.class);

    @Autowired
    TrxKasBesarRepository trxKasBesarRepository;

    @Autowired
    TrxPendingHeadTellerRepository trxPendingHeadTellerRepository;

    @Autowired
    HeadTellerBalanceRepository headTellerBalanceRepository;

    @Autowired
    TrxHeadTellerRepository trxHeadTellerRepository;

    @Autowired
    CabangRepository cabangRepository;

    @Autowired
    TrxAmountDetailRepository trxAmountDetailRepository;

    @Autowired
    TrxAuditTrailRepository trxAuditTrailRepository;

    @Autowired
    TrxHTAmountDetailRepository trxHTAmountDetailRepository;

    @Autowired
    BranchBalanceRepository branchBalanceRepository;

    @Autowired
    TrxTellerExchangeVaultRepository trxTellerExchangeVaultRepository;
    @Autowired
    OfficerRepository officerRepository;
    @Autowired
    OfficerNonProsperaRepository officerNonProsperaRepository;
    @Autowired
    TrxOverlimitHTBalanceRepository trxOverlimitHTBalanceRepository;
    @Autowired
    EmailService emailService;
    @Autowired
    RequestHelper requestHelper;
    @Autowired
    OfficerImpl officerImpl;

    public CommonResponse<PendingHeadTellerTransactionModel> pendingTransaction(Profile profile,String branchId,String type){
        CommonResponse<PendingHeadTellerTransactionModel> response = new CommonResponse<>();
        response.setType(GET_PENDING_BALANCE);
        PendingHeadTellerTransactionModel pendingHeadTellerTransactionModel = new PendingHeadTellerTransactionModel();
        Cabang cabang = cabangRepository.findByCabangId(branchId);
        Gson gson = new Gson();
        pendingHeadTellerTransactionModel.setPeriod(LocalDate.now());
        pendingHeadTellerTransactionModel.setBranchId(branchId);
        pendingHeadTellerTransactionModel.setBranchName(cabang.getCabangDesc());
        pendingHeadTellerTransactionModel.setType(type);
        List<PendingTransactionHTModel> trxPendingHeadTeller = trxPendingHeadTellerRepository.listPending(LocalDate.now(),branchId,type,TrxStatus.PENDING.getValue());
        List<ListTransactionHeadTellerModel> listTransaction = new ArrayList<>();
        for(PendingTransactionHTModel pendingTransactionHTModel : trxPendingHeadTeller){
            TrxHTAmountDetail trxAmountDetail = gson.fromJson(pendingTransactionHTModel.getAmountDetail(), TrxHTAmountDetail.class);
            ListTransactionHeadTellerModel listTransactionHeadTellerModel = new ListTransactionHeadTellerModel();
            listTransactionHeadTellerModel.setTransactionId(pendingTransactionHTModel.getTransactionId());
            listTransactionHeadTellerModel.setTimestamp(pendingTransactionHTModel.getTimestamp());
            listTransactionHeadTellerModel.setType(pendingTransactionHTModel.getType());
            listTransactionHeadTellerModel.setTransactionType(pendingTransactionHTModel.getTypeDesc());
            listTransactionHeadTellerModel.setP100KAmount(trxAmountDetail.getP100KAmount());
            listTransactionHeadTellerModel.setP75KAmount(trxAmountDetail.getP75KAmount());
            listTransactionHeadTellerModel.setP50KAmount(trxAmountDetail.getP50KAmount());
            listTransactionHeadTellerModel.setP20KAmount(trxAmountDetail.getP20KAmount());
            listTransactionHeadTellerModel.setP10KAmount(trxAmountDetail.getP10KAmount());
            listTransactionHeadTellerModel.setP5KAmount(trxAmountDetail.getP5KAmount());
            listTransactionHeadTellerModel.setP2KAmount(trxAmountDetail.getP2KAmount());
            listTransactionHeadTellerModel.setP1KAmount(trxAmountDetail.getP1KAmount());
            listTransactionHeadTellerModel.setC1KAmount(trxAmountDetail.getC1KAmount());
            listTransactionHeadTellerModel.setC500Amount(trxAmountDetail.getC500Amount());
            listTransactionHeadTellerModel.setC200Amount(trxAmountDetail.getC200Amount());
            listTransactionHeadTellerModel.setC100Amount(trxAmountDetail.getC100Amount());
            listTransactionHeadTellerModel.setC50Amount(trxAmountDetail.getC50Amount());
            listTransactionHeadTellerModel.setTotalAmount(trxAmountDetail.getTotal());
            listTransactionHeadTellerModel.setTellerId(pendingTransactionHTModel.getTellerId());
            listTransaction.add(listTransactionHeadTellerModel);
        }
        pendingHeadTellerTransactionModel.setTransactions(listTransaction);
        response.setData(pendingHeadTellerTransactionModel);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }

    public CommonResponse<PendingHeadTellerModel> pendingBalance(Profile profile,String branchId,String type){
        CommonResponse<PendingHeadTellerModel> response = new CommonResponse<>();
        response.setType(GET_HEAD_TELLER_BALANCE);
        Gson gson = new Gson();
        PendingHeadTellerModel pendingHeadTellerTransactionModel = new PendingHeadTellerModel();
        String saldotype = getSaldoType(type);
        Cabang branch = cabangRepository.findByCabangId(branchId);
        pendingHeadTellerTransactionModel.setBranchId(branchId);
        pendingHeadTellerTransactionModel.setBranchName(branch.getCabangDesc());
        pendingHeadTellerTransactionModel.setPeriod(LocalDate.now());
        TrxPendingHeadTeller trxPendingHeadTeller = trxPendingHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeOrderByCreateDateTimeDesc(LocalDate.now(),branchId, saldotype);
        if (trxPendingHeadTeller==null){
            HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
            getPendingBalance(headTellerBalance,pendingHeadTellerTransactionModel);
        }else {
            TrxHTAmountDetail trxAmountDetail =  gson.fromJson(trxPendingHeadTeller.getAmountDetail(),TrxHTAmountDetail.class);
            getPendingHeadTellerBalance(trxAmountDetail,pendingHeadTellerTransactionModel,trxPendingHeadTeller);
        }
        response.setData(pendingHeadTellerTransactionModel);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }


    public void getPendingBalance(HeadTellerBalance headTellerBalance, PendingHeadTellerModel headTeller) {
        Gson gson = new Gson();
        AmountConfig amountConfig = new AmountConfig();
        double totalAmount = 0;
        List<BalanceDetailModel> balanceDetailModels = gson.fromJson(amountConfig.amount(), new TypeToken<List<BalanceDetailModel>>() {
        }.getType());
        for (BalanceDetailModel detailModel : balanceDetailModels) {
            switch (detailModel.getId()) {
                case COIN_50:
                    detailModel.setAmount(headTellerBalance.getC50Amount());
                    detailModel.setCount(headTellerBalance.getC50Count());
                    totalAmount += headTellerBalance.getC50Amount();
                    break;
                case COIN_100:
                    detailModel.setAmount(headTellerBalance.getC100Amount());
                    detailModel.setCount(headTellerBalance.getC100Count());
                    totalAmount += headTellerBalance.getC100Amount();
                    break;
                case COIN_200:
                    detailModel.setAmount(headTellerBalance.getC200Amount());
                    detailModel.setCount(headTellerBalance.getC200Count());
                    totalAmount += headTellerBalance.getC200Amount();
                    break;
                case COIN_500:
                    detailModel.setAmount(headTellerBalance.getC500Amount());
                    detailModel.setCount(headTellerBalance.getC500Count());
                    totalAmount += headTellerBalance.getC500Amount();
                    break;
                case COIN_1K:
                    detailModel.setAmount(headTellerBalance.getC1KAmount());
                    detailModel.setCount(headTellerBalance.getC1KCount());
                    totalAmount += headTellerBalance.getC1KAmount();
                    break;
                case PAPER_1K:
                    detailModel.setAmount(headTellerBalance.getP1KAmount());
                    detailModel.setCount(headTellerBalance.getP1KCount());
                    totalAmount += headTellerBalance.getP1KAmount();
                    break;
                case PAPER_2K:
                    detailModel.setAmount(headTellerBalance.getP2KAmount());
                    detailModel.setCount(headTellerBalance.getP2KCount());
                    totalAmount += headTellerBalance.getP2KAmount();
                    break;
                case PAPER_5K:
                    detailModel.setAmount(headTellerBalance.getP5KAmount());
                    detailModel.setCount(headTellerBalance.getP5KCount());
                    totalAmount += headTellerBalance.getP5KAmount();
                    break;
                case PAPER_10K:
                    detailModel.setAmount(headTellerBalance.getP10KAmount());
                    detailModel.setCount(headTellerBalance.getP10KCount());
                    totalAmount += headTellerBalance.getP10KAmount();
                    break;
                case PAPER_20K:
                    detailModel.setAmount(headTellerBalance.getP20KAmount());
                    detailModel.setCount(headTellerBalance.getP20KCount());
                    totalAmount += headTellerBalance.getP20KAmount();
                    break;
                case PAPER_50K:
                    detailModel.setAmount(headTellerBalance.getP50KAmount());
                    detailModel.setCount(headTellerBalance.getP50KCount());
                    totalAmount += headTellerBalance.getP50KAmount();
                    break;
                case PAPER_75K:
                    detailModel.setAmount(headTellerBalance.getP75KAmount());
                    detailModel.setCount(headTellerBalance.getP75KCount());
                    totalAmount += headTellerBalance.getP75KAmount();
                    break;
                case PAPER_100K:
                    detailModel.setAmount(headTellerBalance.getP100KAmount());
                    detailModel.setCount(headTellerBalance.getP100KCount());
                    totalAmount += headTellerBalance.getP100KAmount();
                    break;
            }
        }
        headTeller.setBalanceDetail(balanceDetailModels);
        headTeller.setTotalBalance(totalAmount);
    }

    public void getPendingHeadTellerBalance(TrxHTAmountDetail headTellerBalance, PendingHeadTellerModel headTeller,TrxPendingHeadTeller trxPendingHeadTeller) {
        Gson gson = new Gson();
        AmountConfig amountConfig = new AmountConfig();
        List<BalanceDetailModel> balanceDetailModels = gson.fromJson(amountConfig.amount(), new TypeToken<List<BalanceDetailModel>>() {
        }.getType());
        for (BalanceDetailModel detailModel : balanceDetailModels) {
            switch (detailModel.getId()) {
                case COIN_50:
                    detailModel.setAmount(headTellerBalance.getC50Amount());
                    detailModel.setCount(headTellerBalance.getC50Count());
                    break;
                case COIN_100:
                    detailModel.setAmount(headTellerBalance.getC100Amount());
                    detailModel.setCount(headTellerBalance.getC100Count());
                    break;
                case COIN_200:
                    detailModel.setAmount(headTellerBalance.getC200Amount());
                    detailModel.setCount(headTellerBalance.getC200Count());

                    break;
                case COIN_500:
                    detailModel.setAmount(headTellerBalance.getC500Amount());
                    detailModel.setCount(headTellerBalance.getC500Count());
                    break;
                case COIN_1K:
                    detailModel.setAmount(headTellerBalance.getC1KAmount());
                    detailModel.setCount(headTellerBalance.getC1KCount());
                    break;
                case PAPER_1K:
                    detailModel.setAmount(headTellerBalance.getP1KAmount());
                    detailModel.setCount(headTellerBalance.getP1KCount());
                    break;
                case PAPER_2K:
                    detailModel.setAmount(headTellerBalance.getP2KAmount());
                    detailModel.setCount(headTellerBalance.getP2KCount());
                    break;
                case PAPER_5K:
                    detailModel.setAmount(headTellerBalance.getP5KAmount());
                    detailModel.setCount(headTellerBalance.getP5KCount());
                    break;
                case PAPER_10K:
                    detailModel.setAmount(headTellerBalance.getP10KAmount());
                    detailModel.setCount(headTellerBalance.getP10KCount());
                    break;
                case PAPER_20K:
                    detailModel.setAmount(headTellerBalance.getP20KAmount());
                    detailModel.setCount(headTellerBalance.getP20KCount());
                    break;
                case PAPER_50K:
                    detailModel.setAmount(headTellerBalance.getP50KAmount());
                    detailModel.setCount(headTellerBalance.getP50KCount());
                    break;
                case PAPER_75K:
                    detailModel.setAmount(headTellerBalance.getP75KAmount());
                    detailModel.setCount(headTellerBalance.getP75KCount());
                    break;
                case PAPER_100K:
                    detailModel.setAmount(headTellerBalance.getP100KAmount());
                    detailModel.setCount(headTellerBalance.getP100KCount());
                    break;
            }
        }
        headTeller.setBalanceDetail(balanceDetailModels);
        headTeller.setTotalBalance(headTellerBalance.getTotal());
    }
    
    public CommonResponse<InputTransactionResponse> addPendingTransaction(Profile profile,InputHeadTellerPendingRequest req,String branchId, String type){
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(ADD_PENDING_HT_TRANSACTION);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        try {
            if (!validationAddPendingHeadTeller(req.getAmountDetails(), req.getNikTeller(), req.getTotalAmount(), req.getPeriod(), req.getBranchId())){
                inputTransactionResponse.setRequestId(req.getRequestId());
                inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
                inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
                response.setData(inputTransactionResponse);
                response.setStatus(ResponseStatus.FAILED.getCode());
                response.setStatusDesc(ResponseStatus.FAILED.getValue());
                return response;
            }
            inputTransactionResponse.setRequestId(req.getRequestId());
            detailAddPendingTransactionHT2T(branchId,profile.getPreferred_username(),req,type,inputTransactionResponse);
            inputTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
        }catch (Exception e) {
            logger.error("Error add Pending Transaction Head Teller " + e);
            inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
            inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
        }
        response.setData(inputTransactionResponse);
        return response;
    }
    
    public String generateTransactionId(String branchId, String type, LocalDate period,String process){

        Integer getLastTransactionId;
        if (process.equals("release")){
            getLastTransactionId = trxHeadTellerRepository.getLastTrxHeadTellerTransactionId(branchId,type,period);
        }else if (process.equals(TELLER_EXCHANGE_VAULT)){
            getLastTransactionId = trxTellerExchangeVaultRepository.getLastTrxTellerExchangeVaultTransactionId(branchId,type,period);
        }else{
            getLastTransactionId = trxPendingHeadTellerRepository.getLastTransactionId(branchId,type,period) ;

        }
        String transactionNo = String.format("%02d",1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        if (getLastTransactionId!=null){
            getLastTransactionId += 1;
            transactionNo =String.format("%02d",getLastTransactionId);
        }
        return type+formatter.format(period)+branchId+transactionNo;
        
    }

    public void detailAddPendingTransactionHT2T(String branchId,String inputer,InputHeadTellerPendingRequest request,String type,InputTransactionResponse inputTransactionResponse) {
        Gson gson =new Gson();
        TrxPendingHeadTeller lastBalance = trxPendingHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeOrderByCreateDateTimeDesc(LocalDate.now(),branchId,getSaldoType(type));
        HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
        BalanceModel balanceModel = new BalanceModel();
        if (lastBalance!=null){
            TrxHTAmountDetail trxAmountDetail = gson.fromJson(lastBalance.getAmountDetail(), TrxHTAmountDetail.class);
            balanceModel.setBalanceModel(trxAmountDetail);
            balanceModel.setBalance(trxAmountDetail.getTotal());
        }else {
            balanceModel.setBalanceModel(headTellerBalance);
            balanceModel.setBalance(headTellerBalance.getTotalAmount());
        }

        TrxHTAmountDetail trxAmountDetail = new TrxHTAmountDetail();
        TrxHTAmountDetail trxSaldoAmountDetail = new TrxHTAmountDetail();
        String trxHTtransactionId = generateTransactionId(branchId,type,LocalDate.parse(request.getPeriod()), ADD_HT);
        TrxPendingHeadTeller trxPendingHeadTeller = new TrxPendingHeadTeller();
        trxPendingHeadTeller.setRequestId(request.getRequestId());
        trxPendingHeadTeller.setBranchId(branchId);
        trxPendingHeadTeller.setPeriod(LocalDate.parse(request.getPeriod()));
        trxPendingHeadTeller.setTransactionId(trxHTtransactionId);
        trxPendingHeadTeller.setType(type);
        trxPendingHeadTeller.setStatus(TrxStatus.PENDING.getValue());
        trxPendingHeadTeller.setInputer(inputer);
        trxPendingHeadTeller.setTellerId(request.getNikTeller());
        trxPendingHeadTeller.setTotalAmount(request.getTotalAmount());
        trxPendingHeadTeller.setBalance(getAmount(balanceModel.getBalance(), request.getTotalAmount(), type));
        trxPendingHeadTeller.setCreateDateTime(LocalDateTime.now());
        trxPendingHeadTeller.setUpdateDateTime(LocalDateTime.now());
        TrxPendingHeadTeller trxPendingHeadTellerSaldo = new TrxPendingHeadTeller();
        trxPendingHeadTellerSaldo.setRequestId(request.getRequestId());
        trxPendingHeadTellerSaldo.setBranchId(branchId);
        trxPendingHeadTellerSaldo.setPeriod(LocalDate.parse(request.getPeriod()));
        String saldoType = getSaldoType(type);
        String trxHTSaldotransactionId = generateTransactionId(branchId,saldoType, LocalDate.parse(request.getPeriod()), ADD_HT);
        trxPendingHeadTellerSaldo.setTransactionId(trxHTSaldotransactionId);
        trxPendingHeadTellerSaldo.setType(saldoType);
        trxPendingHeadTellerSaldo.setStatus(TrxStatus.SUCCESS.getValue());
        trxPendingHeadTellerSaldo.setInputer(inputer);
        trxPendingHeadTellerSaldo.setTellerId(request.getNikTeller());
        trxPendingHeadTellerSaldo.setTotalAmount(getAmount(balanceModel.getBalance(), request.getTotalAmount(), type));
        trxPendingHeadTellerSaldo.setBalance(getAmount(balanceModel.getBalance(), request.getTotalAmount(), type));
        trxPendingHeadTellerSaldo.setCreateDateTime(LocalDateTime.now());
        trxPendingHeadTellerSaldo.setUpdateDateTime(LocalDateTime.now());
        trxAmountDetail.setTransactionId(trxHTtransactionId);
        trxSaldoAmountDetail.setTransactionId(trxHTSaldotransactionId);

        for (AmountDetail amountDetail : request.getAmountDetails()) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    trxAmountDetail.setC50Amount(amountDetail.getTotal());
                    trxAmountDetail.setC50Count(amountDetail.getCount());
                    trxSaldoAmountDetail.setC50Amount(getAmount(balanceModel.getC50Amount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setC50Count(getCount(balanceModel.getC50Count(),amountDetail.getCount(),type));
                    break;
                case COIN_100:
                    trxAmountDetail.setC100Amount(amountDetail.getTotal());
                    trxAmountDetail.setC100Count(amountDetail.getCount());
                    trxSaldoAmountDetail.setC100Amount(getAmount(balanceModel.getC100Amount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setC100Count(getCount(balanceModel.getC100Count(),amountDetail.getCount(),type));
                    break;
                case COIN_200:
                    trxAmountDetail.setC200Amount(amountDetail.getTotal());
                    trxAmountDetail.setC200Count(amountDetail.getCount());
                    trxSaldoAmountDetail.setC200Amount(getAmount(balanceModel.getC200Amount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setC200Count(getCount(balanceModel.getC200Count(),amountDetail.getCount(),type));
                    break;
                case COIN_500:
                    trxAmountDetail.setC500Amount(amountDetail.getTotal());
                    trxAmountDetail.setC500Count(amountDetail.getCount());
                    trxSaldoAmountDetail.setC500Amount(getAmount(balanceModel.getC500Amount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setC500Count(getCount(balanceModel.getC500Count(),amountDetail.getCount(),type));
                    break;
                case COIN_1K:
                    trxAmountDetail.setC1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setC1KCount(amountDetail.getCount());
                    trxSaldoAmountDetail.setC1KAmount(getAmount(balanceModel.getC1KAmount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setC1KCount(getCount(balanceModel.getC1KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_1K:
                    trxAmountDetail.setP1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP1KCount(amountDetail.getCount());
                    trxSaldoAmountDetail.setP1KAmount(getAmount(balanceModel.getP1KAmount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setP1KCount(getCount(balanceModel.getP1KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_2K:
                    trxAmountDetail.setP2KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP2KCount(amountDetail.getCount());
                    trxSaldoAmountDetail.setP2KAmount(getAmount(balanceModel.getP2KAmount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setP2KCount(getCount(balanceModel.getP2KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_5K:
                    trxAmountDetail.setP5KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP5KCount(amountDetail.getCount());
                    trxSaldoAmountDetail.setP5KAmount(getAmount(balanceModel.getP5KAmount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setP5KCount(getCount(balanceModel.getP5KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_10K:
                    trxAmountDetail.setP10KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP10KCount(amountDetail.getCount());
                    trxSaldoAmountDetail.setP10KAmount(getAmount(balanceModel.getP10KAmount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setP10KCount(getCount(balanceModel.getP10KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_20K:
                    trxAmountDetail.setP20KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP20KCount(amountDetail.getCount());
                    trxSaldoAmountDetail.setP20KAmount(getAmount(balanceModel.getP20KAmount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setP20KCount(getCount(balanceModel.getP20KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_50K:
                    trxAmountDetail.setP50KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP50KCount(amountDetail.getCount());
                    trxSaldoAmountDetail.setP50KAmount(getAmount(balanceModel.getP50KAmount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setP50KCount(getCount(balanceModel.getP50KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_75K:
                    trxAmountDetail.setP75KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP75KCount(amountDetail.getCount());
                    trxSaldoAmountDetail.setP75KAmount(getAmount(balanceModel.getP75KAmount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setP75KCount(getCount(balanceModel.getP75KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_100K:
                    trxAmountDetail.setP100KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP100KCount(amountDetail.getCount());
                    trxSaldoAmountDetail.setP100KAmount(getAmount(balanceModel.getP100KAmount(),amountDetail.getTotal(),type));
                    trxSaldoAmountDetail.setP100KCount(getCount(balanceModel.getP100KCount(),amountDetail.getCount(),type));
                    break;
            }
        }
        trxPendingHeadTeller.setAmountDetail(gson.toJson(trxAmountDetail));
        trxPendingHeadTellerSaldo.setAmountDetail(gson.toJson(trxSaldoAmountDetail));
        trxPendingHeadTellerRepository.save(trxPendingHeadTeller);
        trxPendingHeadTellerRepository.save(trxPendingHeadTellerSaldo);
        inputTransactionResponse.setTransactionId(trxPendingHeadTeller.getTransactionId());
    }

    @Transactional
    public CommonResponse<DeleteTransactionResponse> deleteTransactionHT(Profile profile,String branchId, String type, String transactionId){
        CommonResponse<DeleteTransactionResponse> response = new CommonResponse<>();
        response.setType(DELETE_PENDING_TRANSACTION);
        DeleteTransactionResponse deleteTransactionResponse = new DeleteTransactionResponse();
        Gson gson = new Gson();
        LocalDate today = LocalDate.now();
        HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
        Double currentBalance = headTellerBalance.getTotalAmount();
        String saldoType = getSaldoType(type);
        List<String> deleteType = new ArrayList<>();
        deleteType.add(type);
        deleteType.add(saldoType);
        try{
            List<TrxPendingHeadTeller> trxPendingHeadTeller = trxPendingHeadTellerRepository.findAllByPeriodAndBranchIdAndType(today,branchId,type);
            for (TrxPendingHeadTeller trxPendingHeadTellers : trxPendingHeadTeller){
                if (trxPendingHeadTellers.getTransactionId().equals(transactionId)){
                    trxPendingHeadTeller.remove(trxPendingHeadTellers);
                    break;
                }
            }
            trxPendingHeadTellerRepository.deleteTrxPendingHeadTellersByPeriodAndBranchIdAndTypeIn(today, branchId,deleteType);
            int seq = 1;
            TrxHTAmountDetail lastSaldo = new TrxHTAmountDetail();

            for (TrxPendingHeadTeller trxPendingHeadTellers : trxPendingHeadTeller) {

                TrxPendingHeadTeller trxPendingSaldo = new TrxPendingHeadTeller();
                trxPendingSaldo.setTransactionId(generateTransactionId(branchId, saldoType, LocalDate.now(), ADD_HT));
                trxPendingSaldo.setRequestId(trxPendingHeadTellers.getRequestId());
                trxPendingSaldo.setBranchId(branchId);
                trxPendingSaldo.setInputer(profile.getPreferred_username());
                trxPendingSaldo.setType(saldoType);
                trxPendingSaldo.setPeriod(trxPendingHeadTellers.getPeriod());
                trxPendingSaldo.setStatus(TrxStatus.SUCCESS.getValue());
                trxPendingSaldo.setInputer(trxPendingHeadTellers.getInputer());
                TrxHTAmountDetail trxAmountDetail = gson.fromJson(trxPendingHeadTellers.getAmountDetail(), TrxHTAmountDetail.class);
                trxAmountDetail.setTransactionId(trxPendingSaldo.getTransactionId());
                trxPendingSaldo.setTotalAmount(getAmount(currentBalance, trxPendingHeadTellers.getTotalAmount(),type));
                trxPendingSaldo.setBalance(getAmount(currentBalance , trxPendingHeadTellers.getTotalAmount(),type));
                    if (seq==1){
                        trxAmountDetail.setC50Amount(getAmount(headTellerBalance.getC50Amount() , trxAmountDetail.getC50Amount(),type));
                        trxAmountDetail.setC50Count(getCount(headTellerBalance.getC50Count(), trxAmountDetail.getC50Count(),type));
                        trxAmountDetail.setC100Amount(getAmount(headTellerBalance.getC100Amount(), trxAmountDetail.getC100Amount(),type));
                        trxAmountDetail.setC100Count(getCount(headTellerBalance.getC100Count(), trxAmountDetail.getC100Count(),type));
                        trxAmountDetail.setC200Amount(getAmount(headTellerBalance.getC200Amount(), trxAmountDetail.getC200Amount(),type));
                        trxAmountDetail.setC200Count(getCount(headTellerBalance.getC200Count(), trxAmountDetail.getC200Count(),type));
                        trxAmountDetail.setC500Amount(getAmount(headTellerBalance.getC500Amount(), trxAmountDetail.getC500Amount(),type));
                        trxAmountDetail.setC500Count(getCount(headTellerBalance.getC500Count(), trxAmountDetail.getC500Count(),type));
                        trxAmountDetail.setC1KAmount(getAmount(headTellerBalance.getC1KAmount(), trxAmountDetail.getC1KAmount(),type));
                        trxAmountDetail.setC1KCount(getCount(headTellerBalance.getC1KCount(), trxAmountDetail.getC1KCount(),type));
                        trxAmountDetail.setP1KAmount(getAmount(headTellerBalance.getP1KAmount(), trxAmountDetail.getP1KAmount(),type));
                        trxAmountDetail.setP1KCount(getCount(headTellerBalance.getP1KCount(), trxAmountDetail.getP1KCount(),type));
                        trxAmountDetail.setP2KAmount(getAmount(headTellerBalance.getP2KAmount(), trxAmountDetail.getP2KAmount(),type));
                        trxAmountDetail.setP2KCount(getCount(headTellerBalance.getP2KCount(), trxAmountDetail.getP2KCount(),type));
                        trxAmountDetail.setP5KAmount(getAmount(headTellerBalance.getP5KAmount(), trxAmountDetail.getP5KAmount(),type));
                        trxAmountDetail.setP5KCount(getCount(headTellerBalance.getP5KCount(), trxAmountDetail.getP5KCount(),type));
                        trxAmountDetail.setP10KAmount(getAmount(headTellerBalance.getP10KAmount(), trxAmountDetail.getP10KAmount(),type));
                        trxAmountDetail.setP10KCount(getCount(headTellerBalance.getP10KCount(), trxAmountDetail.getP10KCount(),type));
                        trxAmountDetail.setP20KAmount(getAmount(headTellerBalance.getP20KAmount(), trxAmountDetail.getP20KAmount(),type));
                        trxAmountDetail.setP20KCount(getCount(headTellerBalance.getP20KCount(), trxAmountDetail.getP20KCount(),type));
                        trxAmountDetail.setP50KAmount(getAmount(headTellerBalance.getP50KAmount(), trxAmountDetail.getP50KAmount(),type));
                        trxAmountDetail.setP50KCount(getCount(headTellerBalance.getP50KCount(), trxAmountDetail.getP50KCount(),type));
                        trxAmountDetail.setP75KAmount(getAmount(headTellerBalance.getP75KAmount(), trxAmountDetail.getP75KAmount(),type));
                        trxAmountDetail.setP75KCount(getCount(headTellerBalance.getP75KCount(), trxAmountDetail.getP75KCount(),type));
                        trxAmountDetail.setP100KAmount(getAmount(headTellerBalance.getP100KAmount(), trxAmountDetail.getP100KAmount(),type));
                        trxAmountDetail.setP100KCount(getCount(headTellerBalance.getP100KCount(), trxAmountDetail.getP100KCount(),type));
                        lastSaldo = trxAmountDetail;
                    }else {
                        trxAmountDetail.setC50Amount(getAmount(lastSaldo.getC50Amount() , trxAmountDetail.getC50Amount(),type));
                        trxAmountDetail.setC50Count(getCount(lastSaldo.getC50Count(), trxAmountDetail.getC50Count(),type));
                        trxAmountDetail.setC100Amount(getAmount(lastSaldo.getC100Amount(), trxAmountDetail.getC100Amount(),type));
                        trxAmountDetail.setC100Count(getCount(lastSaldo.getC100Count(), trxAmountDetail.getC100Count(),type));
                        trxAmountDetail.setC200Amount(getAmount(lastSaldo.getC200Amount(), trxAmountDetail.getC200Amount(),type));
                        trxAmountDetail.setC200Count(getCount(lastSaldo.getC200Count(), trxAmountDetail.getC200Count(),type));
                        trxAmountDetail.setC500Amount(getAmount(lastSaldo.getC500Amount(), trxAmountDetail.getC500Amount(),type));
                        trxAmountDetail.setC500Count(getCount(lastSaldo.getC500Count(), trxAmountDetail.getC500Count(),type));
                        trxAmountDetail.setC1KAmount(getAmount(lastSaldo.getC1KAmount(), trxAmountDetail.getC1KAmount(),type));
                        trxAmountDetail.setC1KCount(getCount(lastSaldo.getC1KCount(), trxAmountDetail.getC1KCount(),type));
                        trxAmountDetail.setP1KAmount(getAmount(lastSaldo.getP1KAmount(), trxAmountDetail.getP1KAmount(),type));
                        trxAmountDetail.setP1KCount(getCount(lastSaldo.getP1KCount(), trxAmountDetail.getP1KCount(),type));
                        trxAmountDetail.setP2KAmount(getAmount(lastSaldo.getP2KAmount(), trxAmountDetail.getP2KAmount(),type));
                        trxAmountDetail.setP2KCount(getCount(lastSaldo.getP2KCount(), trxAmountDetail.getP2KCount(),type));
                        trxAmountDetail.setP5KAmount(getAmount(lastSaldo.getP5KAmount(), trxAmountDetail.getP5KAmount(),type));
                        trxAmountDetail.setP5KCount(getCount(lastSaldo.getP5KCount(), trxAmountDetail.getP5KCount(),type));
                        trxAmountDetail.setP10KAmount(getAmount(lastSaldo.getP10KAmount(), trxAmountDetail.getP10KAmount(),type));
                        trxAmountDetail.setP10KCount(getCount(lastSaldo.getP10KCount(), trxAmountDetail.getP10KCount(),type));
                        trxAmountDetail.setP20KAmount(getAmount(lastSaldo.getP20KAmount(), trxAmountDetail.getP20KAmount(),type));
                        trxAmountDetail.setP20KCount(getCount(lastSaldo.getP20KCount(), trxAmountDetail.getP20KCount(),type));
                        trxAmountDetail.setP50KAmount(getAmount(lastSaldo.getP50KAmount(), trxAmountDetail.getP50KAmount(),type));
                        trxAmountDetail.setP50KCount(getCount(lastSaldo.getP50KCount(), trxAmountDetail.getP50KCount(),type));
                        trxAmountDetail.setP75KAmount(getAmount(lastSaldo.getP75KAmount(), trxAmountDetail.getP75KAmount(),type));
                        trxAmountDetail.setP75KCount(getCount(lastSaldo.getP75KCount(), trxAmountDetail.getP75KCount(),type));
                        trxAmountDetail.setP100KAmount(getAmount(lastSaldo.getP100KAmount(), trxAmountDetail.getP100KAmount(),type));
                        trxAmountDetail.setP100KCount(getCount(lastSaldo.getP100KCount(), trxAmountDetail.getP100KCount(),type));
                        lastSaldo = trxAmountDetail;
                    }
                trxPendingHeadTellers.setBalance(trxPendingSaldo.getBalance());
                trxPendingSaldo.setAmountDetail(gson.toJson(trxAmountDetail));
                trxPendingSaldo.setCreateDateTime(trxPendingHeadTellers.getCreateDateTime().plusSeconds(1));
                trxPendingSaldo.setUpdateDateTime(trxPendingHeadTellers.getUpdateDateTime().plusSeconds(1));
                currentBalance = trxPendingSaldo.getBalance();
                trxPendingHeadTellerRepository.save(trxPendingHeadTellers);
                trxPendingHeadTellerRepository.save(trxPendingSaldo);
                seq++;
            }

            deleteTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            deleteTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
        }catch (Exception e){
            deleteTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
            deleteTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
        }
        response.setData(deleteTransactionResponse);
        return response;
    }

    @Transactional
    public CommonResponse<SubmitPendingTransactionResponse> submitPendingTransaction(Profile profile, String branchId, String type, SubmitPendingTransactionRequest request) {
        CommonResponse<SubmitPendingTransactionResponse> response = new CommonResponse<>();
        response.setType(SUBMIT_PENDING_TRANSACTION);
        SubmitPendingTransactionResponse submitPendingTransactionResponse = new SubmitPendingTransactionResponse();
        submitPendingTransactionResponse.setRequestId(request.getRequestId());
        Gson gson = new Gson();
        LocalDateTime now = LocalDateTime.now();
        boolean overlimitHT = false;
        try {
            if (checkPendingHT2V(branchId, LocalDate.parse(request.getPeriod()))){
                submitPendingTransactionResponse.setStatus(ResponseStatus.HEAD_TELLER_SUBMIT_PENDING_HT2V.getCode());
                submitPendingTransactionResponse.setStatusDesc(ResponseStatus.HEAD_TELLER_SUBMIT_PENDING_HT2V.getValue());
                response.setStatus(ResponseStatus.HEAD_TELLER_SUBMIT_PENDING_HT2V.getCode());
                response.setStatusDesc(ResponseStatus.HEAD_TELLER_SUBMIT_PENDING_HT2V.getValue());
                response.setData(submitPendingTransactionResponse);
                return response;
            }
            
            List<TrxPendingHeadTeller> trxPendingHeadTeller = trxPendingHeadTellerRepository.findAllByPeriodAndBranchIdAndType(LocalDate.parse(request.getPeriod()), branchId, type);
            HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
            BalanceModel htbalance = new BalanceModel();
            List<String> deleteType = new ArrayList<>();
            deleteType.add(type);
            deleteType.add(getSaldoType(type));
            List<String> deleteRequestId = new ArrayList<>();
            if (headTellerBalance == null){
                headTellerBalance = new HeadTellerBalance();
                headTellerBalance.setNikHT(profile.getPreferred_username());
                headTellerBalance.setBranchId(branchId);
            }
            htbalance.setBalanceModel(headTellerBalance);
            for (TrxPendingHeadTeller trxPendingHT : trxPendingHeadTeller) {
                TrxHTAmountDetail trxAmountDetail = gson.fromJson(trxPendingHT.getAmountDetail(), TrxHTAmountDetail.class);
                if (!validateDenomHTAmountDetail(trxAmountDetail, trxPendingHT.getTellerId())){
                    submitPendingTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
                    submitPendingTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
                    response.setData(submitPendingTransactionResponse);
                    response.setStatus(ResponseStatus.FAILED.getCode());
                    response.setStatusDesc(ResponseStatus.FAILED.getValue());
                    return response;
                }
                if (type.equals(TrxType.HT2T.getCode())) {
                    if (!checkBalanceTransaction(htbalance, trxAmountDetail)) {
                        submitPendingTransactionResponse.setStatus(ResponseStatus.HEAD_TELLER_SUBMIT_BALANCE_INSUFFICIENT.getCode());
                        submitPendingTransactionResponse.setStatusDesc(ResponseStatus.HEAD_TELLER_SUBMIT_BALANCE_INSUFFICIENT.getValue());
                        response.setStatus(ResponseStatus.HEAD_TELLER_SUBMIT_BALANCE_INSUFFICIENT.getCode());
                        response.setStatusDesc(ResponseStatus.HEAD_TELLER_SUBMIT_BALANCE_INSUFFICIENT.getValue());

                        if (!deleteRequestId.isEmpty()){
                            trxPendingHeadTellerRepository.deleteTrxPendingHeadTeller(LocalDate.now(), branchId, deleteType, deleteRequestId);
                        }
                        response.setData(submitPendingTransactionResponse);
                        return response;
                    }
                }
                TrxHeadTeller trxHeadTeller = new TrxHeadTeller();
                Double balance = getAmount(headTellerBalance.getTotalAmount(), trxPendingHT.getTotalAmount(), trxPendingHT.getType());
                trxHeadTeller.setHeadTeller(generateTransactionId(branchId, type, trxPendingHT.getPeriod(), RELEASE_HT), trxPendingHT.getBranchId(), trxPendingHT.getPeriod(), trxPendingHT.getType(), TrxStatus.SUCCESS.getValue(), trxPendingHT.getInputer(), trxPendingHT.getTellerId(), trxPendingHT.getTotalAmount(), balance, now, now, null);
                PICModel pic = enrichPICName(trxHeadTeller.getInputer(), trxHeadTeller.getTellerId(), officerImpl, officerNonProsperaRepository);
                trxHeadTeller.setInputerName(pic.getInputerName());
                trxHeadTeller.setTellerName(pic.getTellerName());
                
                TrxHeadTeller trxHeadTellerSaldo = new TrxHeadTeller();
                checkExistingSaldoAkhirHeadTeller(LocalDate.now(),branchId,trxHeadTellerRepository, trxHTAmountDetailRepository);
                trxHeadTellerSaldo.setHeadTeller(generateTransactionId(branchId, TrxType.SKHT.getCode(), trxPendingHT.getPeriod(), RELEASE_HT),trxPendingHT.getBranchId(), trxPendingHT.getPeriod(), TrxType.SKHT.getCode(), TrxStatus.SUCCESS.getValue(), trxPendingHT.getInputer(), null, balance, balance, now, now, null);
                trxHeadTellerSaldo.setInputerName(pic.getInputerName());
                
                headTellerBalance.setC50Amount(getAmount(headTellerBalance.getC50Amount(), trxAmountDetail.getC50Amount(), type));
                headTellerBalance.setC50Count(getCount(headTellerBalance.getC50Count(), trxAmountDetail.getC50Count(), type));
                headTellerBalance.setC100Amount(getAmount(headTellerBalance.getC100Amount(), trxAmountDetail.getC100Amount(), type));
                headTellerBalance.setC100Count(getCount(headTellerBalance.getC100Count(), trxAmountDetail.getC100Count(), type));
                headTellerBalance.setC200Amount(getAmount(headTellerBalance.getC200Amount(), trxAmountDetail.getC200Amount(), type));
                headTellerBalance.setC200Count(getCount(headTellerBalance.getC200Count(), trxAmountDetail.getC200Count(), type));
                headTellerBalance.setC500Amount(getAmount(headTellerBalance.getC500Amount(), trxAmountDetail.getC500Amount(), type));
                headTellerBalance.setC500Count(getCount(headTellerBalance.getC500Count(), trxAmountDetail.getC500Count(), type));
                headTellerBalance.setC1KAmount(getAmount(headTellerBalance.getC1KAmount(), trxAmountDetail.getC1KAmount(), type));
                headTellerBalance.setC1KCount(getCount(headTellerBalance.getC1KCount(), trxAmountDetail.getC1KCount(), type));
                headTellerBalance.setP1KAmount(getAmount(headTellerBalance.getP1KAmount(), trxAmountDetail.getP1KAmount(), type));
                headTellerBalance.setP1KCount(getCount(headTellerBalance.getP1KCount(), trxAmountDetail.getP1KCount(), type));
                headTellerBalance.setP2KAmount(getAmount(headTellerBalance.getP2KAmount(), trxAmountDetail.getP2KAmount(), type));
                headTellerBalance.setP2KCount(getCount(headTellerBalance.getP2KCount(), trxAmountDetail.getP2KCount(), type));
                headTellerBalance.setP5KAmount(getAmount(headTellerBalance.getP5KAmount(), trxAmountDetail.getP5KAmount(), type));
                headTellerBalance.setP5KCount(getCount(headTellerBalance.getP5KCount(), trxAmountDetail.getP5KCount(), type));
                headTellerBalance.setP10KAmount(getAmount(headTellerBalance.getP10KAmount(), trxAmountDetail.getP10KAmount(), type));
                headTellerBalance.setP10KCount(getCount(headTellerBalance.getP10KCount(), trxAmountDetail.getP10KCount(), type));
                headTellerBalance.setP20KAmount(getAmount(headTellerBalance.getP20KAmount(), trxAmountDetail.getP20KAmount(), type));
                headTellerBalance.setP20KCount(getCount(headTellerBalance.getP20KCount(), trxAmountDetail.getP20KCount(), type));
                headTellerBalance.setP50KAmount(getAmount(headTellerBalance.getP50KAmount(), trxAmountDetail.getP50KAmount(), type));
                headTellerBalance.setP50KCount(getCount(headTellerBalance.getP50KCount(), trxAmountDetail.getP50KCount(), type));
                headTellerBalance.setP75KAmount(getAmount(headTellerBalance.getP75KAmount(), trxAmountDetail.getP75KAmount(), type));
                headTellerBalance.setP75KCount(getCount(headTellerBalance.getP75KCount(), trxAmountDetail.getP75KCount(), type));
                headTellerBalance.setP100KAmount(getAmount(headTellerBalance.getP100KAmount(), trxAmountDetail.getP100KAmount(), type));
                headTellerBalance.setP100KCount(getCount(headTellerBalance.getP100KCount(), trxAmountDetail.getP100KCount(), type));
                headTellerBalance.setTotalAmount(getAmount(headTellerBalance.getTotalAmount(), trxAmountDetail.getTotal(), type));
                trxAmountDetail.setTransactionId(trxHeadTeller.getTransactionId());
                TrxHTAmountDetail trxHTAmountDetailSaldo = new TrxHTAmountDetail();
                trxHTAmountDetailSaldo.setTransactionId(trxHeadTellerSaldo.getTransactionId());
                trxHTAmountDetailSaldo.setAmount(headTellerBalance);
                headTellerBalanceRepository.save(headTellerBalance);
                trxHeadTellerRepository.save(trxHeadTeller);
                trxHeadTellerRepository.save(trxHeadTellerSaldo);
                trxHTAmountDetailRepository.save(trxAmountDetail);
                trxHTAmountDetailRepository.save(trxHTAmountDetailSaldo);
                htbalance.setBalanceModel(headTellerBalance);
                deleteRequestId.add(trxPendingHT.getRequestId());
                if (type.equals(TrxType.HT2T.getCode())) {
                    insertAuditTrail(profile.getPreferred_username(), Action.RELEASE_HT2T.getValue(), trxHeadTeller.getTransactionId(), trxHeadTeller.getBranchId());
                } else if (type.equals(TrxType.T2HT.getCode())) {
                    insertAuditTrail(profile.getPreferred_username(), Action.RELEASE_T2HT.getValue(), trxHeadTeller.getTransactionId(), trxHeadTeller.getBranchId());
                }
                overlimitHT = checkHTBalanceOverlimit(headTellerBalance.getTotalAmount(), trxHeadTeller.getTransactionId(), branchId);
                if (overlimitHT){
                    sendEmailHTBalanceOverlimit(branchId);

                    submitPendingTransactionResponse.setStatus(ResponseStatus.SUCCESS_WITH_OVERLIMIT.getCode());
                    submitPendingTransactionResponse.setStatusDesc(ResponseStatus.SUCCESS_WITH_OVERLIMIT.getValue());
                    response.setData(submitPendingTransactionResponse);
                }
            }
            submitPendingTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            submitPendingTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
            if (overlimitHT){
                response.setStatus(ResponseStatus.SUCCESS_WITH_OVERLIMIT.getCode());
                response.setStatusDesc(ResponseStatus.SUCCESS_WITH_OVERLIMIT.getValue());
            }
            trxPendingHeadTellerRepository.deleteTrxPendingHeadTeller(LocalDate.now(), branchId, deleteType, deleteRequestId);
            response.setData(submitPendingTransactionResponse);
        }catch (Exception e){
            submitPendingTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
            submitPendingTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setData(submitPendingTransactionResponse);
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
            return response;
        }
        return response;
    }

    private void sendEmailHTBalanceOverlimit(String branchId) {
        emailService.sendEmail(requestHelper.createEmailHeader(),requestHelper.createSendEmailHTBalanceOverlimit(branchId));
    }

    private boolean checkHTBalanceOverlimit(Double totalAmountHTBalance, String transactionId, String branchId) {
        if (!branchId.equals("HO")) {
            Cabang cabang = cabangRepository.findByCabangId(branchId);
            if (cabang != null) {
                if (totalAmountHTBalance > cabang.getTotalBalanceHT()) {
                    TrxOverlimitHTBalance overlimit = new TrxOverlimitHTBalance();
                    overlimit.setBranchId(branchId);
                    overlimit.setTransactionId(transactionId);
                    overlimit.setTotalBalance(totalAmountHTBalance);
                    overlimit.setPeriod(LocalDate.now());
                    overlimit.setCreateDateTime(LocalDateTime.now());
                    trxOverlimitHTBalanceRepository.save(overlimit);
                    return true;
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    private boolean checkPendingHT2V(String branchId, LocalDate period){
        return trxHeadTellerRepository.findTopByPeriodGreaterThanEqualAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc((period.minusDays(14)),branchId,TrxType.HT2V.getCode(),TrxStatus.PENDING.getValue()) != null;
    }
    private boolean checkBalanceTransaction(BalanceModel balance, TrxHTAmountDetail detail) {
        if (balance.getC50Count() < detail.getC50Count() ||
                balance.getC50Amount() < detail.getC50Amount() ||
                balance.getC100Count() < detail.getC100Count() ||
                balance.getC100Amount() < detail.getC100Amount() ||
                balance.getC200Count() < detail.getC200Count() ||
                balance.getC200Amount() < detail.getC200Amount() ||
                balance.getC500Count() < detail.getC500Count() ||
                balance.getC500Amount() < detail.getC500Amount() ||
                balance.getC1KCount() < detail.getC1KCount() ||
                balance.getC1KAmount() < detail.getC1KAmount() ||
                balance.getP1KCount() < detail.getP1KCount() ||
                balance.getP1KAmount() < detail.getP1KAmount() ||
                balance.getP2KCount() < detail.getP2KCount() ||
                balance.getP2KAmount() < detail.getP2KAmount() ||
                balance.getP5KCount() < detail.getP5KCount() ||
                balance.getP5KAmount() < detail.getP5KAmount() ||
                balance.getP10KCount() < detail.getP10KCount() ||
                balance.getP10KAmount() < detail.getP10KAmount() ||
                balance.getP20KCount() < detail.getP20KCount() ||
                balance.getP20KAmount() < detail.getP20KAmount() ||
                balance.getP50KCount() < detail.getP50KCount() ||
                balance.getP50KAmount() < detail.getP50KAmount() ||
                balance.getP75KCount() < detail.getP75KCount() ||
                balance.getP75KAmount() < detail.getP75KAmount() ||
                balance.getP100KCount() < detail.getP100KCount() ||
                balance.getP100KAmount() < detail.getP100KAmount()) {
            return false;
        }
        return true;
    }
    public void insertAuditTrail(String nik, String action, String transactionId, String branchId){
        TrxAuditTrail trxAuditTrail = new TrxAuditTrail();
        trxAuditTrail.setNik(nik);
        trxAuditTrail.setAction(action);
        trxAuditTrail.setCreateDateTime(LocalDateTime.now());
        trxAuditTrail.setTransactionId(transactionId);
        trxAuditTrail.setBranchId(branchId);
        trxAuditTrailRepository.save(trxAuditTrail);
    }

    public Double getAmount(Double firstValue,Double lastValue,String type){
        Double amount = 0.0;
        if (type.equals(TrxType.HT2T.getCode())){
            amount = firstValue - lastValue;
        }else if(type.equals(TrxType.T2HT.getCode())) {
            amount = firstValue + lastValue;
        }
        return amount;
    }

    public Integer getCount(Integer firstValue,Integer lastValue,String type){
        Integer amount = 0;
        if (type.equals(TrxType.HT2T.getCode())){
            amount = firstValue - lastValue;
        }else if(type.equals(TrxType.T2HT.getCode())) {
            amount = firstValue + lastValue;
        }
        return amount;
    }

    public CommonResponse<LastHTBranchBalanceModel> getLastHTBalance(Profile profile,String branchId){
        CommonResponse<LastHTBranchBalanceModel> response = new CommonResponse<>();
        response.setType(GET_LAST_BRANCH_BALANCE);
        LastHTBranchBalanceModel lastHTBranchBalanceModel = new LastHTBranchBalanceModel();
        LocalDate now = LocalDate.now();
        BalanceModel balance = new BalanceModel();
        TrxHeadTeller pendingHT2V = trxHeadTellerRepository.findByPeriodGreaterThanEqualAndBranchIdAndTypeAndStatus(now.minusDays(14),branchId,TrxType.HT2V.getCode(),TrxStatus.PENDING.getValue());
        HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
        lastHTBranchBalanceModel.setTotalBeginBalance(headTellerBalance.getTotalAmount());
        lastHTBranchBalanceModel.setPeriod(now);
        lastHTBranchBalanceModel.setBranchId(branchId);

        if (pendingHT2V==null) {
            TrxHeadTeller trxHeadTeller = trxHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeInAndStatusOrderByIdDesc(LocalDate.now(), branchId, Set.of(TrxType.SKHT.getCode(),TrxType.SA.getCode()),TrxStatus.SUCCESS.getValue());
            if (trxHeadTeller==null){
                balance.setBalanceModel(headTellerBalance);
                lastHTBranchBalanceModel.setTotalOutBalance(headTellerBalance.getTotalAmount());
            }else {
                TrxHTAmountDetail detail = trxHTAmountDetailRepository.findByTransactionId(trxHeadTeller.getTransactionId());
                balance.setBalanceModel(detail);
                lastHTBranchBalanceModel.setTransactionId(trxHeadTeller.getTransactionId());
                lastHTBranchBalanceModel.setTotalOutBalance(trxHeadTeller.getBalance());
            }
            lastHTBranchBalanceModel.setIsPending(false);
            lastHTBranchBalanceModel.setTotalRemainingBalance(lastHTBranchBalanceModel.getTotalBeginBalance() - lastHTBranchBalanceModel.getTotalOutBalance());
            getHeadTellerAmountDetail(lastHTBranchBalanceModel, headTellerBalance, balance);
        }else {

            TrxHTAmountDetail pendingDetail = trxHTAmountDetailRepository.findByTransactionId(pendingHT2V.getTransactionId());
            balance.setBalanceModel(pendingDetail);
            lastHTBranchBalanceModel.setTransactionId(pendingHT2V.getTransactionId());
            lastHTBranchBalanceModel.setTotalOutBalance(pendingHT2V.getTotalAmount());
            lastHTBranchBalanceModel.setIsPending(true);
            lastHTBranchBalanceModel.setTotalRemainingBalance(lastHTBranchBalanceModel.getTotalBeginBalance() - lastHTBranchBalanceModel.getTotalOutBalance());
            getHeadTellerAmountDetail(lastHTBranchBalanceModel, headTellerBalance, balance);

        }
        response.setData(lastHTBranchBalanceModel);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());

        return response;
    }
    public void getHeadTellerAmountDetail(LastHTBranchBalanceModel lastHTBranchBalanceModel,HeadTellerBalance beginBalance,BalanceModel trxAmountDetail){
        Gson gson = new Gson();
        AmountConfig amountConfig = new AmountConfig();
        List<HeadTellerAmountDetail> headTellerAmountDetail= gson.fromJson(amountConfig.amount(), new TypeToken<List<HeadTellerAmountDetail>>() {
        }.getType());
        for (HeadTellerAmountDetail amountDetail : headTellerAmountDetail) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    amountDetail.setAmountCount(trxAmountDetail.getC50Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC50Amount());
                    amountDetail.setBeginBalance(beginBalance.getC50Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_100:
                    amountDetail.setAmountCount(trxAmountDetail.getC100Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC100Amount());
                    amountDetail.setBeginBalance(beginBalance.getC100Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_200:
                    amountDetail.setAmountCount(trxAmountDetail.getC200Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC200Amount());
                    amountDetail.setBeginBalance(beginBalance.getC200Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_500:
                    amountDetail.setAmountCount(trxAmountDetail.getC500Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC500Amount());
                    amountDetail.setBeginBalance(beginBalance.getC500Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_1K:
                    amountDetail.setAmountCount(trxAmountDetail.getC1KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getC1KAmount());
                    amountDetail.setBeginBalance(beginBalance.getC1KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_1K:
                    amountDetail.setAmountCount(trxAmountDetail.getP1KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP1KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP1KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_2K:
                    amountDetail.setAmountCount(trxAmountDetail.getP2KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP2KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP2KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_5K:
                    amountDetail.setAmountCount(trxAmountDetail.getP5KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP5KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP5KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_10K:
                    amountDetail.setAmountCount(trxAmountDetail.getP10KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP10KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP10KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_20K:
                    amountDetail.setAmountCount(trxAmountDetail.getP20KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP20KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP20KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_50K:
                    amountDetail.setAmountCount(trxAmountDetail.getP50KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP50KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP50KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_75K:
                    amountDetail.setAmountCount(trxAmountDetail.getP75KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP75KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP75KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_100K:
                    amountDetail.setAmountCount(trxAmountDetail.getP100KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP100KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP100KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
            }
        }
        lastHTBranchBalanceModel.setAmountDetails(headTellerAmountDetail);
    }

    public CommonResponse<InputTransactionResponse> inputHT2V(Profile profile, InputHT2VRequest req, String branchId){
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(SUBMIT_HT2V);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        inputTransactionResponse.setTransactionId(req.getTransactionId());
        inputTransactionResponse.setRequestId(req.getRequestId());

        try {
            if (!validateInputHT2V(req.getAmountDetails(), req.getBranchId(), req.getPeriod(),req.getTotalAmount())) {
                inputTransactionResponse.setRequestId(req.getRequestId());
                inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
                inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
                response.setData(inputTransactionResponse);
                response.setStatus(TrxStatus.FAILED.getCode());
                response.setStatusDesc(TrxStatus.FAILED.getValue());
                logger.error("Input HT2V failed Request Not Complete");
                return response;
            }
            TrxKasBesar checkMHT = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeAndStatusOrderByCreateDateTimeDesc(LocalDate.parse(req.getPeriod()),branchId,TrxType.MHT.getCode(), PENDING.getValue());
            if (checkMHT !=null){
                inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
                inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
                response.setData(inputTransactionResponse);
                response.setStatus(TrxStatus.FAILED.getCode());
                response.setStatusDesc(TrxStatus.FAILED.getValue());
                return response;
            }
            TrxHeadTeller checkRequestId = trxHeadTellerRepository.findByPeriodAndRequestIdAndType(LocalDate.parse(req.getPeriod()), req.getRequestId(), TrxType.HT2V.getCode());
            if (checkRequestId != null) {
                inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
                inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
                response.setData(inputTransactionResponse);
                response.setStatus(TrxStatus.FAILED.getCode());
                response.setStatusDesc(TrxStatus.FAILED.getValue());
                return response;
            }
            BalanceModel balance = new BalanceModel();
            HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
            balance.setBalanceModel(req.getAmountDetails());
            checkExistingSaldoAkhir(LocalDate.now(),branchId);
            TrxHeadTeller trxHT2V = new TrxHeadTeller();
            trxHT2V.setTransactionId(generateTransactionId(branchId,TrxType.HT2V.getCode(),LocalDate.now(),RELEASE_HT));
            trxHT2V.setBranchId(branchId);
            trxHT2V.setInputer(profile.getPreferred_username());
            trxHT2V.setPeriod(LocalDate.now());
            trxHT2V.setTotalAmount(balance.getBalance());
            trxHT2V.setBalance(headTellerBalance.getTotalAmount() - balance.getBalance());
            trxHT2V.setStatus(TrxStatus.PENDING.getValue());
            trxHT2V.setType(TrxType.HT2V.getCode());
            trxHT2V.setCreateDateTime(LocalDateTime.now());
            trxHT2V.setUpdateDateTime(LocalDateTime.now());
            trxHT2V.setRequestId(req.getRequestId());
            PICModel pic = enrichPICName(trxHT2V.getInputer(), trxHT2V.getTellerId(), officerImpl, officerNonProsperaRepository);
            trxHT2V.setInputerName(pic.getInputerName());
            
            TrxHeadTeller trxHT2VSA = new TrxHeadTeller();
            trxHT2VSA.setTransactionId(generateTransactionId(branchId,TrxType.SKHT.getCode(),LocalDate.now(),RELEASE_HT));
            trxHT2VSA.setBranchId(branchId);
            trxHT2VSA.setType(TrxType.SKHT.getCode());
            trxHT2VSA.setTotalAmount(trxHT2V.getBalance());
            trxHT2VSA.setBalance(trxHT2V.getBalance());
            trxHT2VSA.setPeriod(LocalDate.now());
            trxHT2VSA.setStatus(TrxStatus.SUCCESS.getValue());
            trxHT2VSA.setInputer(profile.getPreferred_username());
            trxHT2VSA.setCreateDateTime(LocalDateTime.now());
            trxHT2VSA.setUpdateDateTime(LocalDateTime.now());
            trxHT2VSA.setRequestId(req.getRequestId());
            trxHT2VSA.setInputerName(pic.getInputerName());
            
            TrxHTAmountDetail trxHTAmountDetail = new TrxHTAmountDetail();
            trxHTAmountDetail.setTransactionId(trxHT2V.getTransactionId());
            trxHTAmountDetail.setC50Amount(balance.getC50Amount());
            trxHTAmountDetail.setC50Count(balance.getC50Count());
            trxHTAmountDetail.setC100Amount(balance.getC100Amount());
            trxHTAmountDetail.setC100Count(balance.getC100Count());
            trxHTAmountDetail.setC200Amount(balance.getC200Amount());
            trxHTAmountDetail.setC200Count(balance.getC200Count());
            trxHTAmountDetail.setC500Amount(balance.getC500Amount());
            trxHTAmountDetail.setC500Count(balance.getC500Count());
            trxHTAmountDetail.setC1KAmount(balance.getC1KAmount());
            trxHTAmountDetail.setC1KCount(balance.getC1KCount());
            trxHTAmountDetail.setP1KAmount(balance.getP1KAmount());
            trxHTAmountDetail.setP1KCount(balance.getP1KCount());
            trxHTAmountDetail.setP2KAmount(balance.getP2KAmount());
            trxHTAmountDetail.setP2KCount(balance.getP2KCount());
            trxHTAmountDetail.setP5KAmount(balance.getP5KAmount());
            trxHTAmountDetail.setP5KCount(balance.getP5KCount());
            trxHTAmountDetail.setP10KAmount(balance.getP10KAmount());
            trxHTAmountDetail.setP10KCount(balance.getP10KCount());
            trxHTAmountDetail.setP20KAmount(balance.getP20KAmount());
            trxHTAmountDetail.setP20KCount(balance.getP20KCount());
            trxHTAmountDetail.setP50KAmount(balance.getP50KAmount());
            trxHTAmountDetail.setP50KCount(balance.getP50KCount());
            trxHTAmountDetail.setP75KAmount(balance.getP75KAmount());
            trxHTAmountDetail.setP75KCount(balance.getP75KCount());
            trxHTAmountDetail.setP100KAmount(balance.getP100KAmount());
            trxHTAmountDetail.setP100KCount(balance.getP100KCount());
            TrxHTAmountDetail saldoDetail = new TrxHTAmountDetail();
            saldoDetail.setTransactionId(trxHT2VSA.getTransactionId());
            saldoDetail.setC50Amount(headTellerBalance.getC50Amount() - balance.getC50Amount());
            saldoDetail.setC50Count(headTellerBalance.getC50Count() - balance.getC50Count());
            saldoDetail.setC100Amount(headTellerBalance.getC100Amount() - balance.getC100Amount());
            saldoDetail.setC100Count(headTellerBalance.getC100Count() - balance.getC100Count());
            saldoDetail.setC200Amount(headTellerBalance.getC200Amount() - balance.getC200Amount());
            saldoDetail.setC200Count(headTellerBalance.getC200Count() - balance.getC200Count());
            saldoDetail.setC500Amount(headTellerBalance.getC500Amount() - balance.getC500Amount());
            saldoDetail.setC500Count(headTellerBalance.getC500Count() - balance.getC500Count());
            saldoDetail.setC1KAmount(headTellerBalance.getC1KAmount() - balance.getC1KAmount());
            saldoDetail.setC1KCount(headTellerBalance.getC1KCount() - balance.getC1KCount());
            saldoDetail.setP1KAmount(headTellerBalance.getP1KAmount() - balance.getP1KAmount());
            saldoDetail.setP1KCount(headTellerBalance.getP1KCount() - balance.getP1KCount());
            saldoDetail.setP2KAmount(headTellerBalance.getP2KAmount() - balance.getP2KAmount());
            saldoDetail.setP2KCount(headTellerBalance.getP2KCount() - balance.getP2KCount());
            saldoDetail.setP5KAmount(headTellerBalance.getP5KAmount() - balance.getP5KAmount());
            saldoDetail.setP5KCount(headTellerBalance.getP5KCount() - balance.getP5KCount());
            saldoDetail.setP10KAmount(headTellerBalance.getP10KAmount() - balance.getP10KAmount());
            saldoDetail.setP10KCount(headTellerBalance.getP10KCount() - balance.getP10KCount());
            saldoDetail.setP20KAmount(headTellerBalance.getP20KAmount() - balance.getP20KAmount());
            saldoDetail.setP20KCount(headTellerBalance.getP20KCount() - balance.getP20KCount());
            saldoDetail.setP50KAmount(headTellerBalance.getP50KAmount() - balance.getP50KAmount());
            saldoDetail.setP50KCount(headTellerBalance.getP50KCount() - balance.getP50KCount());
            saldoDetail.setP75KAmount(headTellerBalance.getP75KAmount() - balance.getP75KAmount());
            saldoDetail.setP75KCount(headTellerBalance.getP75KCount() - balance.getP75KCount());
            saldoDetail.setP100KAmount(headTellerBalance.getP100KAmount() - balance.getP100KAmount());
            saldoDetail.setP100KCount(headTellerBalance.getP100KCount() - balance.getP100KCount());
            trxHeadTellerRepository.save(trxHT2V);
            trxHeadTellerRepository.save(trxHT2VSA);
            trxHTAmountDetailRepository.save(trxHTAmountDetail);
            trxHTAmountDetailRepository.save(saldoDetail);

            submitTellerExchangeHT2V(trxHT2V,trxHTAmountDetail);
            insertAuditTrail(profile.getPreferred_username(),Action.SUBMIT_HT2V.getValue(),req.getTransactionId(), req.getBranchId());
            inputTransactionResponse.setTransactionId(trxHT2V.getTransactionId());
            inputTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
        } catch (Exception e) {
            inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
        }
        response.setData(inputTransactionResponse);
        return response;
    }
    
    public CommonResponse<HeadTellerInModel> getHT2VPending(Profile profile, String branchId){
        CommonResponse<HeadTellerInModel> response = new CommonResponse<>();
        response.setType(GET_HT2V_PENDING);
        HeadTellerInModel headTellerInModel = new HeadTellerInModel();
        LocalDate period = LocalDate.now();
        TrxHeadTeller trxHeadTeller = trxHeadTellerRepository.findTopByPeriodGreaterThanEqualAndBranchIdAndTypeAndStatusAndRefIdIsNullOrderByCreateDateTimeDesc((period.minusDays(14)),branchId,TrxType.HT2V.getCode(),TrxStatus.PENDING.getValue());
        if (trxHeadTeller == null){
            response.setStatus(ResponseStatus.DATA_NOT_FOUND.getCode());
            response.setStatusDesc(ResponseStatus.DATA_NOT_FOUND.getValue());
            return response;
        }
        TrxHTAmountDetail htDetail = trxHTAmountDetailRepository.findByTransactionId(trxHeadTeller.getTransactionId());
        BalanceModel balanceModel = new BalanceModel();
        TrxKasBesar beginBalance = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeInAndStatusInOrderByCreateDateTimeDesc(period,branchId, Set.of(TrxType.SA.getCode(), TrxType.SAW.getCode() ,TrxType.SAK.getCode(), TrxType.CC.getCode(), TrxType.CASHOPNAME.getCode()), Set.of(TrxStatus.SUCCESS.getValue(), TrxStatus.APPROVED.getValue()));
        if (beginBalance==null){
            BranchBalance balance = branchBalanceRepository.findByBranchId(branchId);
            balanceModel.setBalanceModel(balance);
            headTellerInModel.setTotalBeginBalance(balance.getTotal());
        } else {
            TrxAmountDetail details = trxAmountDetailRepository.findByTransactionId(beginBalance.getTransactionId());
            balanceModel.setBalanceModel(details);
            headTellerInModel.setTotalBeginBalance(beginBalance.getBalanceAmount());
        }
        headTellerInModel.setPeriod(period.toString());
        headTellerInModel.setBranchId(branchId);
        headTellerInModel.setTransactionId(trxHeadTeller.getTransactionId());
        headTellerInModel.setTotalInBalance(trxHeadTeller.getTotalAmount());
        headTellerInModel.setTotalRemainingBalance(headTellerInModel.getTotalBeginBalance() + headTellerInModel.getTotalInBalance());
        getHT2VPendingDetail(headTellerInModel, balanceModel, htDetail);
        response.setData(headTellerInModel);
        response.setStatus(ResponseStatus.SUCCESS.getCode());
        response.setStatusDesc(ResponseStatus.SUCCESS.getValue());
        return response;
    }

    public void getHT2VPendingDetail(HeadTellerInModel headTellerInModel,BalanceModel beginBalance,TrxHTAmountDetail inBalance){
        Gson gson = new Gson();
        AmountConfig amountConfig = new AmountConfig();
        List<HeadTellerAmountDetail> headTellerAmountDetail= gson.fromJson(amountConfig.amount(), new TypeToken<List<HeadTellerAmountDetail>>() {
        }.getType());
        for (HeadTellerAmountDetail amountDetail : headTellerAmountDetail) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    amountDetail.setAmountCount(inBalance.getC50Count());
                    amountDetail.setAmountTotal(inBalance.getC50Amount());
                    amountDetail.setBeginBalance(beginBalance.getC50Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case COIN_100:
                    amountDetail.setAmountCount(inBalance.getC100Count());
                    amountDetail.setAmountTotal(inBalance.getC100Amount());
                    amountDetail.setBeginBalance(beginBalance.getC100Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case COIN_200:
                    amountDetail.setAmountCount(inBalance.getC200Count());
                    amountDetail.setAmountTotal(inBalance.getC200Amount());
                    amountDetail.setBeginBalance(beginBalance.getC200Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case COIN_500:
                    amountDetail.setAmountCount(inBalance.getC500Count());
                    amountDetail.setAmountTotal(inBalance.getC500Amount());
                    amountDetail.setBeginBalance(beginBalance.getC500Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case COIN_1K:
                    amountDetail.setAmountCount(inBalance.getC1KCount());
                    amountDetail.setAmountTotal(inBalance.getC1KAmount());
                    amountDetail.setBeginBalance(beginBalance.getC1KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case PAPER_1K:
                    amountDetail.setAmountCount(inBalance.getP1KCount());
                    amountDetail.setAmountTotal(inBalance.getP1KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP1KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case PAPER_2K:
                    amountDetail.setAmountCount(inBalance.getP2KCount());
                    amountDetail.setAmountTotal(inBalance.getP2KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP2KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case PAPER_5K:
                    amountDetail.setAmountCount(inBalance.getP5KCount());
                    amountDetail.setAmountTotal(inBalance.getP5KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP5KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case PAPER_10K:
                    amountDetail.setAmountCount(inBalance.getP10KCount());
                    amountDetail.setAmountTotal(inBalance.getP10KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP10KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case PAPER_20K:
                    amountDetail.setAmountCount(inBalance.getP20KCount());
                    amountDetail.setAmountTotal(inBalance.getP20KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP20KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case PAPER_50K:
                    amountDetail.setAmountCount(inBalance.getP50KCount());
                    amountDetail.setAmountTotal(inBalance.getP50KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP50KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case PAPER_75K:
                    amountDetail.setAmountCount(inBalance.getP75KCount());
                    amountDetail.setAmountTotal(inBalance.getP75KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP75KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
                case PAPER_100K:
                    amountDetail.setAmountCount(inBalance.getP100KCount());
                    amountDetail.setAmountTotal(inBalance.getP100KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP100KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()+amountDetail.getAmountTotal());
                    break;
            }
        }
        headTellerInModel.setAmountDetails(headTellerAmountDetail);
    }

    public String getSaldoType(String type){
        if (type.equals(TrxType.T2HT.getCode())){
            return TrxType.SAT2HT.getCode();
        }else {
            return TrxType.SAHT2T.getCode();
        }
    }

    @Transactional
    public CommonResponse<InputTransactionResponse> cancelHT2V(Profile profile,CancelHT2VRequest request,String branchId){
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(CANCEL_HT2V);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        inputTransactionResponse.setRequestId(request.getRequestId());
        try {
            TransactionHeadTeller headTeller = trxHeadTellerRepository.transactionHeadTeller(request.getTransactionId());
            if (!headTeller.getTrxHeadTeller().getStatus().equals(TrxStatus.CANCEL.getValue())){
                TrxHeadTeller lastBalance = trxHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeInAndStatusOrderByCreateDateTimeDesc(headTeller.getTrxHeadTeller().getPeriod(),headTeller.getTrxHeadTeller().getBranchId(),Set.of(TrxType.SA.getCode()),TrxStatus.SUCCESS.getValue());
                TrxHTAmountDetail detail = trxHTAmountDetailRepository.findByTransactionId(lastBalance.getTransactionId());
                TrxHeadTeller cancelHT2V = new TrxHeadTeller();
                headTeller.getTrxHeadTeller().setStatus(TrxStatus.CANCEL.getValue());
                TrxHeadTeller saldoAkhirHT2V = trxHeadTellerRepository.findByPeriodAndRequestIdAndType(headTeller.getTrxHeadTeller().getPeriod(),headTeller.getTrxHeadTeller().getRequestId(),TrxType.SKHT.getCode());
                saldoAkhirHT2V.setStatus(TrxStatus.CANCEL.getValue());

                cancelHT2V.setTransactionId(TrxType.Cancel.getCode().concat(request.getTransactionId()));
                cancelHT2V.setStatus(TrxStatus.CANCEL.getValue());
                cancelHT2V.setType(TrxType.Cancel.getCode());
                cancelHT2V.setInputer(profile.getPreferred_username());
                cancelHT2V.setBranchId(request.getBranchId());
                cancelHT2V.setPeriod(LocalDate.parse(request.getPeriod()));
                cancelHT2V.setCreateDateTime(LocalDateTime.now());
                cancelHT2V.setUpdateDateTime(LocalDateTime.now());
                cancelHT2V.setTotalAmount(headTeller.getTrxHeadTeller().getTotalAmount());
                cancelHT2V.setBalance(headTeller.getTrxHeadTeller().getBalance()+headTeller.getTrxHeadTeller().getTotalAmount());
                cancelHT2V.setReason(encodeIfnotNull(request.getReason()));
                PICModel pic = enrichPICName(cancelHT2V.getInputer(), cancelHT2V.getTellerId(), officerImpl, officerNonProsperaRepository);
                cancelHT2V.setInputerName(pic.getInputerName());
                
                TrxHTAmountDetail detailCancel = new TrxHTAmountDetail();
                detailCancel.setTransactionId(cancelHT2V.getTransactionId());
                detailCancel.setAmount(headTeller.getTrxHTAmountDetail());

                TrxHeadTeller saldoCancel = new TrxHeadTeller();
                saldoCancel.setTransactionId(generateTransactionId(branchId,TrxType.SCHT.getCode(),headTeller.getTrxHeadTeller().getPeriod(),RELEASE_HT));
                saldoCancel.setStatus(TrxStatus.CANCEL.getValue());
                saldoCancel.setType(TrxType.SCHT.getCode());
                saldoCancel.setInputer(profile.getPreferred_username());
                saldoCancel.setBranchId(request.getBranchId());
                saldoCancel.setPeriod(LocalDate.parse(request.getPeriod()));
                saldoCancel.setCreateDateTime(LocalDateTime.now());
                saldoCancel.setUpdateDateTime(LocalDateTime.now());
                saldoCancel.setTotalAmount(cancelHT2V.getBalance());
                saldoCancel.setBalance(cancelHT2V.getBalance());
                saldoCancel.setInputerName(pic.getInputerName());

                TrxHTAmountDetail balanceCancel = new TrxHTAmountDetail();
                balanceCancel.setTransactionId(saldoCancel.getTransactionId());
                balanceCancel.setC50Amount(detail.getC50Amount() + headTeller.getTrxHTAmountDetail().getC50Amount());
                balanceCancel.setC50Count(detail.getC50Count() +headTeller.getTrxHTAmountDetail().getC50Count());
                balanceCancel.setC100Amount(detail.getC100Amount() +headTeller.getTrxHTAmountDetail().getC100Amount());
                balanceCancel.setC100Count(detail.getC100Count() +headTeller.getTrxHTAmountDetail().getC100Count());
                balanceCancel.setC200Amount(detail.getC200Amount() +headTeller.getTrxHTAmountDetail().getC200Amount());
                balanceCancel.setC200Count(detail.getC200Count() +headTeller.getTrxHTAmountDetail().getC200Count());
                balanceCancel.setC500Amount(detail.getC500Amount() +headTeller.getTrxHTAmountDetail().getC500Amount());
                balanceCancel.setC500Count(detail.getC500Count() +headTeller.getTrxHTAmountDetail().getC500Count());
                balanceCancel.setC1KAmount(detail.getC1KAmount() +headTeller.getTrxHTAmountDetail().getC1KAmount());
                balanceCancel.setC1KCount(detail.getC1KCount() +headTeller.getTrxHTAmountDetail().getC1KCount());
                balanceCancel.setP1KAmount(detail.getP1KAmount() +headTeller.getTrxHTAmountDetail().getP1KAmount());
                balanceCancel.setP1KCount(detail.getP1KCount() +headTeller.getTrxHTAmountDetail().getP1KCount());
                balanceCancel.setP2KAmount(detail.getP2KAmount() +headTeller.getTrxHTAmountDetail().getP2KAmount());
                balanceCancel.setP2KCount(detail.getP2KCount() +headTeller.getTrxHTAmountDetail().getP2KCount());
                balanceCancel.setP5KAmount(detail.getP5KAmount() +headTeller.getTrxHTAmountDetail().getP5KAmount());
                balanceCancel.setP5KCount(detail.getP5KCount() +headTeller.getTrxHTAmountDetail().getP5KCount());
                balanceCancel.setP10KAmount(detail.getP10KAmount() +headTeller.getTrxHTAmountDetail().getP10KAmount());
                balanceCancel.setP10KCount(detail.getP10KCount() +headTeller.getTrxHTAmountDetail().getP10KCount());
                balanceCancel.setP20KAmount(detail.getP20KAmount() +headTeller.getTrxHTAmountDetail().getP20KAmount());
                balanceCancel.setP20KCount(detail.getP20KCount() +headTeller.getTrxHTAmountDetail().getP20KCount());
                balanceCancel.setP50KAmount(detail.getP50KAmount() +headTeller.getTrxHTAmountDetail().getP50KAmount());
                balanceCancel.setP50KCount(detail.getP50KCount() +headTeller.getTrxHTAmountDetail().getP50KCount());
                balanceCancel.setP75KAmount(detail.getP75KAmount() +headTeller.getTrxHTAmountDetail().getP75KAmount());
                balanceCancel.setP75KCount(detail.getP75KCount() +headTeller.getTrxHTAmountDetail().getP75KCount());
                balanceCancel.setP100KAmount(detail.getP100KAmount() +headTeller.getTrxHTAmountDetail().getP100KAmount());
                balanceCancel.setP100KCount(detail.getP100KCount() +headTeller.getTrxHTAmountDetail().getP100KCount());
                trxHeadTellerRepository.save(cancelHT2V);
                trxHeadTellerRepository.save(headTeller.getTrxHeadTeller());
                trxHeadTellerRepository.save(saldoAkhirHT2V);
                trxHTAmountDetailRepository.save(detailCancel);
                trxHeadTellerRepository.save(saldoCancel);
                trxHTAmountDetailRepository.save(balanceCancel);
                inputTransactionResponse.setTransactionId(cancelHT2V.getTransactionId());
                insertAuditTrail(profile.getPreferred_username(),Action.CANCEL_HT2V.getValue(),headTeller.getTrxHeadTeller().getTransactionId(), headTeller.getTrxHeadTeller().getBranchId());
                TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findByRefId(headTeller.getTrxHeadTeller().getTransactionId());
                if (vault!=null){
                    vault.setStatus(TrxStatus.CANCEL.getValue());
                    trxTellerExchangeVaultRepository.save(vault);
                }
            }
            inputTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
        }catch (Exception e){
            inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
        }
        response.setData(inputTransactionResponse);
        return response;
    }

    public void checkExistingSaldoAkhir(LocalDate date, String branchId) {
        try {
            TrxHeadTeller checkSaldoAkhir = trxHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(date, branchId, TrxType.SKHT.getCode(), TrxStatus.SUCCESS.getValue());
            if (checkSaldoAkhir != null) {
                String newTransactionId = generateTransactionId(branchId, TrxType.SA.getCode(), date, RELEASE_HT);
                TrxHTAmountDetail detail = trxHTAmountDetailRepository.findByTransactionId(checkSaldoAkhir.getTransactionId());
                checkSaldoAkhir.setTransactionId(newTransactionId);
                checkSaldoAkhir.setType(TrxType.SA.getCode());
                detail.setTransactionId(newTransactionId);
                trxHeadTellerRepository.save(checkSaldoAkhir);
                trxHTAmountDetailRepository.save(detail);
            }
        } catch (Exception e) {
            logger.error("Error check Existing Saldo Akhir Head Teller" + e);
        }
    }

    public void submitTellerExchangeHT2V(TrxHeadTeller headTeller, TrxHTAmountDetail amountDetail){
        TrxTellerExchangeVault tellerExchangeVault = new TrxTellerExchangeVault();
        Gson gson = new Gson();
        String branchId = headTeller.getBranchId();
        LocalDate period = headTeller.getPeriod();
        tellerExchangeVault.setRequestId(headTeller.getRequestId());
        tellerExchangeVault.setTransactionId(generateTransactionId(branchId, TrxType.TEHT2V.getCode(), period,TELLER_EXCHANGE_VAULT));
        tellerExchangeVault.setBranchId(branchId);
        tellerExchangeVault.setPeriod(period);
        tellerExchangeVault.setToName(TO_NAME_KHASANAH);
        tellerExchangeVault.setFromName(FROM_NAME_HEAD_TELLER);
        tellerExchangeVault.setStatus(TrxStatus.PENDING.getValue());
        tellerExchangeVault.setType(TrxType.TEHT2V.getCode());
        tellerExchangeVault.setInputerNik(headTeller.getInputer());
        tellerExchangeVault.setDepositFlag(true);
        tellerExchangeVault.setTotalAmount(headTeller.getTotalAmount());
        tellerExchangeVault.setCreateDateTime(LocalDateTime.now());
        tellerExchangeVault.setUpdateDateTime(LocalDateTime.now());
        tellerExchangeVault.setAmountDetail(gson.toJson(amountDetail));
        tellerExchangeVault.setRefId(headTeller.getTransactionId());
        trxTellerExchangeVaultRepository.save(tellerExchangeVault);
    }
    private static String encodeIfnotNull(String data) {
        if (data != null) {
            return StringEscapeUtils.escapeHtml4(data);
        }
        return null;
    }
    public boolean existInIntervalPendingHeadTeller(String nik, int interval, String branch, String type) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusSeconds(interval);
        return trxPendingHeadTellerRepository.checkRequestInterval(startDate, endDate, branch, nik, type) > 0;
    }

    public CommonResponse<TotalBranchBalanceResponse> totalBranchBalance(Profile nik, String branchId){
        CommonResponse<TotalBranchBalanceResponse> response = new CommonResponse<>();
        response.setType(GET_BRANCH_TOTAL_HT_BALANCE);
        TotalBranchBalanceResponse totalBranchBalance = new TotalBranchBalanceResponse();
        Cabang cabang = cabangRepository.findByCabangId(branchId);

        if (cabang!=null){
            BranchBalance branchBalance = branchBalanceRepository.findByBranchId(cabang.getCabangId());
            HeadTellerBalance htBalance = headTellerBalanceRepository.findByBranchId(cabang.getCabangId());
            totalBranchBalance.setBranchId(branchId);
            totalBranchBalance.setBranchName(cabang.getCabangDesc());
            totalBranchBalance.setBranchBalance(branchBalance.getTotal());
            totalBranchBalance.setHtBalance(htBalance.getTotalAmount());
            response.setData(totalBranchBalance);
        }
        return response;
    }
    public CommonResponse<PendingHeadTellerTransactionResponse> pendingHeadTellerTransaction(String branchId){
        CommonResponse<PendingHeadTellerTransactionResponse> response = new CommonResponse<>();
        PendingHeadTellerTransactionResponse pendingHT = new PendingHeadTellerTransactionResponse();
        response.setType(GET_PENDING_HEAD_TELLER_TRANSACTION);
        LocalDate period = LocalDate.now();
        pendingHT.setPeriod(period.toString());
        pendingHT.setBranchId(branchId);
        List<PendingHTTransactionModel> trxPendingHeadTeller = trxPendingHeadTellerRepository.getPendingTransactionHeadTeller(period, branchId, Set.of(TrxType.HT2T.getCode(), TrxType.T2HT.getCode()));
        if (!trxPendingHeadTeller.isEmpty()){
           trxPendingHeadTeller.forEach(ht -> {
               if (ht.getType().equals(TrxType.HT2T.getCode())){
                   pendingHT.setHt2t(ht.getCount());
               }else if (ht.getType().equals(TrxType.T2HT.getCode())){
                   pendingHT.setT2ht(ht.getCount());
               }
           });
        }
        response.setData(pendingHT);
        return response;
    }
}
