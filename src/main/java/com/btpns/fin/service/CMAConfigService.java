package com.btpns.fin.service;

import com.btpns.fin.model.CommonResponse;
import com.btpns.fin.model.entity.CMAConfig;
import com.btpns.fin.repository.CMAConfigRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.btpns.fin.constant.CommonConstant.*;

@Service
public class CMAConfigService {
    private static final Logger logger = LoggerFactory.getLogger(CMAConfigService.class);
    @Autowired
    CMAConfigRepository cmaConfigRepository;

    public boolean checkModeMaintenance(){
        try {
            CMAConfig cmaConfig = cmaConfigRepository.findCMAConfigById(Long.valueOf(1));
            if (cmaConfig.getStatus().equals(CMA_CONFIG_MAINTENANCE)){
                return true;
            }
        }catch (Exception e){
            logger.error("Error check mode maintenance " + e);
            return false;
        }
        return false;
    }

    public CommonResponse<String> submitModeMaintenance(String status){
        CommonResponse<String> response = new CommonResponse<>();
        response.setType(SUBMIT_MODE_MAINTENANCE);
        try {
            if (status.equals(CMA_CONFIG_ONLINE) || status.equals(CMA_CONFIG_MAINTENANCE)){
                response.setData(status);
                CMAConfig cmaConfig = cmaConfigRepository.findCMAConfigById(Long.valueOf(1))    ;
                if (cmaConfig != null){
                    cmaConfig.setStatus(status);
                    cmaConfigRepository.save(cmaConfig);
                }else {
                    CMAConfig config = new CMAConfig();
                    config.setId(Long.valueOf(1));
                    config.setStatus(status);
                    cmaConfigRepository.save(config);
                }
            }else {
                response.setData(status);
                return response;
            }
        }catch (Exception e){
            logger.error("Fail to submit mode maintenance ", e);
            return response;
        }
        return response;
    }

}
