package com.btpns.fin.service;

import com.btpns.fin.constant.Action;
import com.btpns.fin.constant.ResponseStatus;
import com.btpns.fin.constant.RoleConstant;
import com.btpns.fin.constant.TrxStatus;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.ManagementAlternateRequest;
import com.btpns.fin.model.request.ManagementOfficerRequest;
import com.btpns.fin.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static com.btpns.fin.constant.CommonConstant.*;

@Service
public class ManagementOfficerService {
    private static final Logger logger = LoggerFactory.getLogger(ManagementOfficerService.class);
    @Autowired
    OfficerRepository officerRepository;
    @Autowired
    OfficerNRRepository officerNRRepository;
    @Autowired
    UserAdminRepository userAdminRepository;
    @Autowired
    CabangRepository cabangRepository;
    @Autowired
    EmployeeRepository employeeRepository;
    @Autowired
    AlternateRoleRepository alternateRoleRepository;
    @Autowired
    OfficerNonProsperaRepository officerNonProsperaRepository;
    @Autowired
    TrxAuditTrailRepository trxAuditTrailRepository;
    @Autowired
    OfficerImpl officerImpl;
    @Value("${officer.database}")
    private String officerDatabase;

    @Transactional
    public CommonResponse<String> submitManagementOfficer(String nik, ManagementOfficerRequest request) {
        CommonResponse<String> response = new CommonResponse<>();
        try {
            response.setType(SUBMIT_MANAGEMENT_OFFICER);
            OfficerNR officerNR = officerNRRepository.findByOfficerID(Integer.parseInt(request.getOfficer().getOfficerId()));
            if (request.getMode().equals(REQUEST_TYPE_NEW)) {
                officerNRRepository.newOfficer(request.getOfficer().getMsOfficerNRId(), request.getOfficer().getOfficerId(), request.getOfficer().getOfficerCode(), request.getOfficer().getOfficerName(), request.getOfficer().getNik(), request.getOfficer().getRoleId(), request.getOfficer().getRoleName(), request.getOfficer().getLoginName(), request.getOfficer().getEmailName(), request.getOfficer().getOfficerStatusCode(), request.getOfficer().getOfficerStatusDesc(), request.getOfficer().getKfoCode(), request.getOfficer().getKfoName(), request.getOfficer().getKcsCode(), request.getOfficer().getKcsName(), request.getOfficer().getAmtApprovalLimit());
                response.setData("Success Create Data");
            } else if (request.getMode().equals(REQUEST_TYPE_UPDATE)) {
                if (officerNR != null) {
                    officerNRRepository.save(buildUpdateOfficer(officerNR, request.getOfficer()));
                    response.setData("Success Update Data");
                }
            } else if (request.getMode().equals(REQUEST_TYPE_DELETE)) {
                if (officerNR != null) {
                    officerNRRepository.deleteOfficer(request.getOfficer().getOfficerId());
                    response.setData("Success Delete Data");
                }
            }
            insertAuditTrail(nik, SUBMIT_MANAGEMENT_OFFICER, request.getOfficer().getOfficerId(), getBranchValueFromReq(request.getOfficer(), officerNR));
        } catch (Exception e) {
            logger.error("Failed submit management officer " + e.getMessage());
            response.setData("Failed Submit Data");
        }
        return response;
    }

    private OfficerNR buildUpdateOfficer(OfficerNR officerNR, ManagementOfficerModel request) {
        officerNR.setNik(request.getNik() != null ? request.getNik() : officerNR.getNik());
        officerNR.setRoleID(request.getRoleId() != null ? Integer.parseInt(request.getRoleId()) : officerNR.getRoleID());
        officerNR.setRoleName(request.getRoleName() != null ? request.getRoleName() : officerNR.getRoleName());
        officerNR.setOfficerStatusCode(request.getOfficerStatusCode() != null ? request.getOfficerStatusCode() : officerNR.getOfficerStatusCode());
        officerNR.setOfficerStatusDesc(request.getOfficerStatusDesc() != null ? request.getOfficerStatusDesc() : officerNR.getOfficerStatusDesc());
        officerNR.setKfoCode(request.getKfoCode() != null ? request.getKfoCode() : officerNR.getKfoCode());
        officerNR.setKfoName(request.getKfoName() != null ? request.getKfoName() : officerNR.getKfoName());
        officerNR.setKcsCode(request.getKcsCode() != null ? request.getKcsCode() : officerNR.getKcsCode());
        officerNR.setKcsName(request.getKcsName() != null ? request.getKcsName() : officerNR.getKcsName());
        return officerNR;
    }

    private String getBranchValueFromReq(ManagementOfficerModel request, OfficerNR officerNR) {
        String branchId = "";
        if (request.getKfoCode() == null && request.getKcsCode() == null){
            if (officerNR.getMmsCode() != null){
                branchId = officerNR.getMmsCode();
            }

            if (officerNR.getKfoCode() != null){
                branchId = officerNR.getKfoCode();
            }

            if (officerNR.getKcsCode() != null){
                branchId = officerNR.getKcsCode();
            }
        }else {
            if (request.getKfoCode() != null){
                branchId = request.getKfoCode();
            }

            if (request.getKcsCode() != null){
                branchId = request.getKcsCode();
            }
        }
        return branchId;
    }

    public boolean checkUserAdmin(String nik, String role) {
        UserAdmin userAdmin = new UserAdmin();
        if (StringUtils.isEmpty(role)) {
            userAdmin = userAdminRepository.findByNikAndActiveFlag(nik, "1");
        } else if (role.equalsIgnoreCase(ADMIN_ROLE)) {
            userAdmin = userAdminRepository.findByNikAndRoleAndActiveFlag(nik, ADMIN_ROLE, "1");
        }
        return userAdmin != null;
    }

    public boolean checkUserBranch(String nik, String branch) {
        if (checkUserRoleHO(nik)){
            return true;
        }
        HashMap<String, String> map = getBranch(nik);
        if (map.get(branch) != null){
            return true;
        }
        
        return false;
    }

    private HashMap<String, String> getBranch(String nik){
        HashMap<String, String> map = new HashMap<>();
        List<Cabang> cabang = cabangRepository.getCabangUser(nik, officerDatabase);
        if (cabang.isEmpty()){
            cabang = cabangRepository.getCabangUserEmployee(nik);
            if (!cabang.isEmpty()){
                cabang.forEach(c-> {
                    map.putIfAbsent(c.getCabangId(), c.getCabangId());
                });
            }
        }else {
            cabang.forEach(c-> {
                map.putIfAbsent(c.getCabangId(), c.getCabangId());
            });
        }
        
        return map;
    }
    
    public boolean checkOfficer(String nik){
        OfficerNR officer = officerNRRepository.findByOfficerID(Integer.parseInt(nik));
        if (officer !=null) {
            return true;
        }else {
            return false;
        }
    }

    public boolean checkUserBranchByNIK(String nikInputer, String nik) {
        if (checkUserRoleHO(nikInputer)){
            return true;
        }
        List<Cabang> cabangInputer = cabangRepository.getCabangUser(nikInputer, officerDatabase);
        if (cabangInputer.isEmpty()){
            cabangInputer = cabangRepository.getCabangUserEmployee(nikInputer);
        }
        List<Cabang> cabangNik = cabangRepository.getCabangUser(nik, officerDatabase);
        if (cabangNik.isEmpty()){
            cabangNik = cabangRepository.getCabangUserEmployee(nik);
        }
        if (!cabangInputer.isEmpty() && !cabangNik.isEmpty()){
            for (Cabang c : cabangInputer) {
                for (Cabang cabang : cabangNik) {
                    if (c.getCabangId().equalsIgnoreCase(cabang.getCabangId())){
                        return true;
                    }
                }
            }    
        }

        return false;
    }

    private boolean checkUserRoleHO(String nik){
        UserAdmin userAdmin = userAdminRepository.findByNikAndActiveFlag(nik,"1");
        List<Integer> hoRole = Arrays.asList(HO_ROLE, PAYMENT_INPUTER, PAYMENT_OTORISATOR);
        if (userAdmin != null && (userAdmin.getRole().equals(RoleConstant.VIEWER.getValue()) || userAdmin.getRole().equals(RoleConstant.ADMIN.getValue()))){
            return true;
        }else if (officerImpl.getOfficerByNikAndRoleIdAndStatusCode(nik,hoRole,1) != null){
               return true;
           }else {
            Employee employee = employeeRepository.findByNikAndStatusEmployeeDesc(nik,"Active Assignment");
            if (employee != null && (employee.getOccupation().equals(RoleConstant.QA.getValue()) || employee.getOccupation().equals(RoleConstant.NOM.getValue()) || employee.getOccupation().equals(RoleConstant.ODH.getValue()) || employee.getOccupation().equals(RoleConstant.QAD.getValue()) || employee.getOccupation().equals(RoleConstant.QAM.getValue()) || employee.getOccupation().equals(RoleConstant.SKAI.getValue()))){
                return true;
            }
        }
        return false;
    }

   @Transactional
    public CommonResponse<String> submitAlternateOfficer(String nik,ManagementAlternateRequest request) {
        CommonResponse<String> response = new CommonResponse<>();
        try {
            response.setType(SUBMIT_MANAGEMENT_ALTERNATE);
            String branchId = request.getAlternate().getBranchId();
            if (request.getMode().equals(REQUEST_TYPE_NEW)) {
                if (StringUtils.isEmpty(request.getAlternate().getNik()) ||
                        StringUtils.isEmpty(request.getAlternate().getStartPeriod()) ||
                        StringUtils.isEmpty(request.getAlternate().getEndPeriod()) ||
                        StringUtils.isEmpty(request.getAlternate().getBranchId()) ||
                        StringUtils.isEmpty(request.getAlternate().getAlternateRoleId()) ||
                        StringUtils.isEmpty(request.getAlternate().getAlternateRoleName()) ||
                        StringUtils.isEmpty(request.getAlternate().getInfo())) {
                    response.setData("Failed To Input Alternate, Bad Request");
                    response.setStatus(ResponseStatus.FAILED.getCode());
                    response.setStatusDesc(ResponseStatus.FAILED.getValue());
                    return response;
                }
                AlternateRole alternateRole = new AlternateRole();
                alternateRole.setAlternateRoleId(request.getAlternate().getAlternateRoleId());
                alternateRole.setAlternateRoleName(request.getAlternate().getAlternateRoleName());
                alternateRole.setNik(request.getAlternate().getNik());
                alternateRole.setName(request.getAlternate().getName());
                alternateRole.setBranchId(request.getAlternate().getBranchId());
                alternateRole.setStartPeriod(request.getAlternate().getStartPeriod());
                alternateRole.setEndPeriod(request.getAlternate().getEndPeriod());
                alternateRole.setInfo(request.getAlternate().getInfo());
                alternateRoleRepository.save(alternateRole);
                response.setData("Success Create Data Alternate");
                insertAuditTrail(nik, SUBMIT_MANAGEMENT_ALTERNATE_NEW.concat(ACTION_ADMIN), request.getAlternate().getNik(), branchId);
            } else if (request.getMode().equals(REQUEST_TYPE_UPDATE)) {
                if (!StringUtils.isEmpty(request.getAlternate().getId())) {
                    AlternateRole alternate = alternateRoleRepository.findById(request.getAlternate().getId());
                    if (alternate != null) {
                        alternate.setAlternateRoleId(StringUtils.isEmpty(request.getAlternate().getAlternateRoleId()) ? alternate.getAlternateRoleId() : request.getAlternate().getAlternateRoleId());
                        alternate.setAlternateRoleName(StringUtils.isEmpty(request.getAlternate().getAlternateRoleName()) ? alternate.getAlternateRoleName() : request.getAlternate().getAlternateRoleName());
                        alternate.setNik(StringUtils.isEmpty(request.getAlternate().getNik()) ? alternate.getNik() : request.getAlternate().getNik());
                        alternate.setName(StringUtils.isEmpty(request.getAlternate().getName()) ? alternate.getName() : request.getAlternate().getName());
                        alternate.setBranchId(StringUtils.isEmpty(request.getAlternate().getBranchId()) ? alternate.getBranchId() : request.getAlternate().getBranchId());
                        alternate.setStartPeriod(StringUtils.isEmpty(request.getAlternate().getStartPeriod()) ? alternate.getStartPeriod() : request.getAlternate().getStartPeriod());
                        alternate.setEndPeriod(StringUtils.isEmpty(request.getAlternate().getEndPeriod())? alternate.getStartPeriod() : request.getAlternate().getEndPeriod());
                        alternate.setInfo(StringUtils.isEmpty(request.getAlternate().getInfo()) ? alternate.getAlternateRoleId() : request.getAlternate().getInfo());
                        alternateRoleRepository.save(alternate);
                        response.setData("Success Update Data Alternate");
                        insertAuditTrail(nik, SUBMIT_MANAGEMENT_ALTERNATE_UPDATE.concat(ACTION_ADMIN), request.getAlternate().getNik(), branchId);
                    }else {
                        response.setData("Failed Update Data, Id not found");
                    }
                }else {
                    response.setData("Failed To Input Alternate, Bad Request");
                    response.setStatus(ResponseStatus.FAILED.getCode());
                    response.setStatusDesc(ResponseStatus.FAILED.getValue());
                    return response;
                }
            } else if (request.getMode().equals(REQUEST_TYPE_DELETE)) {
                if (!StringUtils.isEmpty(request.getAlternate().getId())) {
                    AlternateRole alternate = alternateRoleRepository.findById(request.getAlternate().getId());
                    if (alternate != null){
                        alternateRoleRepository.delete(alternate);
                    }
                }
                insertAuditTrail(nik, SUBMIT_MANAGEMENT_ALTERNATE_DELETE.concat(ACTION_ADMIN), request.getAlternate().getNik(), branchId);
            }
            
            response.setStatus(ResponseStatus.SUCCESS.getCode());
            response.setStatusDesc(ResponseStatus.SUCCESS.getValue());
        } catch (Exception e) {
            logger.error("Failed submit management alternate " + e.getMessage());
            response.setData("Failed Submit Data");
        }
        return response;
    }

    public CommonResponse<String> submitOfficerNonProspera(String nik, ManagementAlternateRequest request) {
        CommonResponse<String> response = new CommonResponse<>();
        try {
            response.setType(SUBMIT_MANAGEMENT_ALTERNATE);
            String branchId = request.getAlternate().getBranchId();
            if (request.getMode().equals(REQUEST_TYPE_NEW)) {
                if (StringUtils.isEmpty(request.getAlternate().getNik()) ||
                        StringUtils.isEmpty(request.getAlternate().getName()) ||
                        StringUtils.isEmpty(request.getAlternate().getRoleId()) ||
                        StringUtils.isEmpty(request.getAlternate().getBranchId()) ||
                        StringUtils.isEmpty(request.getAlternate().getRoleName())) {
                    response.setData("Failed To Input Officer Non Prospera, Bad Request");
                    response.setStatus(ResponseStatus.FAILED.getCode());
                    response.setStatusDesc(ResponseStatus.FAILED.getValue());
                    return response;
                }
                OfficerNonProspera officers = new OfficerNonProspera();
                officers.setNik(request.getAlternate().getNik());
                officers.setName(request.getAlternate().getName());
                officers.setBranchId(request.getAlternate().getBranchId());
                officers.setRoleId(request.getAlternate().getRoleId());
                officers.setRoleName(request.getAlternate().getRoleName());
                officers.setActiveFlag(true);
                LocalDateTime now = LocalDateTime.now();
                officers.setCreateDateTime(now);
                officers.setUpdateDateTime(now);
                officerNonProsperaRepository.save(officers);
                response.setData("Success Create Data Officer Non Prospera");
                insertAuditTrail(nik, SUBMIT_MANAGEMENT_NONPROS_NEW.concat(ACTION_ADMIN), request.getAlternate().getNik(), branchId);
            } else if (request.getMode().equals(REQUEST_TYPE_UPDATE)) {
                if (!StringUtils.isEmpty(request.getAlternate().getId())) {
                    OfficerNonProspera officers = officerNonProsperaRepository.findOfficerNonProsperaById(request.getAlternate().getId());
                    if (officers != null) {
                        officers.setNik(StringUtils.isEmpty(request.getAlternate().getNik()) ? officers.getNik() : request.getAlternate().getNik());
                        officers.setName(StringUtils.isEmpty(request.getAlternate().getName()) ? officers.getName() : request.getAlternate().getName());
                        officers.setBranchId(StringUtils.isEmpty(request.getAlternate().getBranchId()) ? officers.getBranchId() : request.getAlternate().getBranchId());
                        officers.setRoleId(StringUtils.isEmpty(request.getAlternate().getRoleId()) ? officers.getRoleId() : request.getAlternate().getRoleId());
                        officers.setRoleName(StringUtils.isEmpty(request.getAlternate().getRoleName()) ? officers.getRoleName() : request.getAlternate().getRoleName());
                        officers.setUpdateDateTime(LocalDateTime.now());
                        officers.setActiveFlag(StringUtils.isEmpty(request.getAlternate().getActiveFlag()) ? officers.getActiveFlag() : request.getAlternate().getActiveFlag());
                        officerNonProsperaRepository.save(officers);
                        response.setData("Success Update Data Officer Non Prospera");
                        insertAuditTrail(nik, SUBMIT_MANAGEMENT_NONPROS_UPDATE.concat(ACTION_ADMIN), request.getAlternate().getNik(), branchId);
                    }else {
                        response.setData("Failed Update Data, Id not found");
                    }
                }else {
                    response.setData("Failed To Input Officer Non Prospera, Bad Request");
                    response.setStatus(ResponseStatus.FAILED.getCode());
                    response.setStatusDesc(ResponseStatus.FAILED.getValue());
                    return response;
                }
            } else if (request.getMode().equals(REQUEST_TYPE_DELETE)) {
                if (!StringUtils.isEmpty(request.getAlternate().getId())) {
                    OfficerNonProspera officers = officerNonProsperaRepository.findOfficerNonProsperaById(request.getAlternate().getId());
                    if (officers != null) {
                        officerNonProsperaRepository.delete(officers);
                        insertAuditTrail(nik, SUBMIT_MANAGEMENT_NONPROS_DELETE.concat(ACTION_ADMIN), request.getAlternate().getNik(), branchId);
                    }
                }
            }
            response.setStatus(ResponseStatus.SUCCESS.getCode());
            response.setStatusDesc(ResponseStatus.SUCCESS.getValue());
        } catch (Exception e) {
            logger.error("Failed submit management alternate " + e.getMessage());
            response.setData("Failed Submit Data");
        }
        return response;
    }

    public CommonResponse<AlternateListModel> getListAlternate(String nik,  String page, String limit){
        CommonResponse<AlternateListModel> response = new CommonResponse<>();
        AlternateListModel alternateListModel = new AlternateListModel();
        try {
            response.setType(GET_LIST_ALTERNATE_ROLE);
            int pageableLimit = alternateListModel.getLimit();
            int pageablePage =   alternateListModel.getPage();
            if (limit != null && limit.equals(LIMIT_ALL)){
                Integer count = alternateRoleRepository.getCountAlternate(nik);
                pageableLimit = count == 0 ? pageableLimit : count;
            }else if (limit != null && page != null){
                pageablePage = Integer.parseInt(page);
                pageableLimit = Integer.parseInt(limit);
            }
            Pageable pageable = PageRequest.of(pageablePage, pageableLimit).previous();
            Page<AlternateDetailModel> listAlternate = alternateRoleRepository.getListAlternate(nik, pageable);
            alternateListModel.setNik(nik);
            if (!listAlternate.isEmpty()){
                alternateListModel.setLimit(pageableLimit);
                alternateListModel.setPage(pageablePage);
                alternateListModel.setTotalItems(listAlternate.getTotalElements());
                alternateListModel.setTotalPages(listAlternate.getTotalPages());
                alternateListModel.setDetails(listAlternate.getContent());
            }
            response.setData(alternateListModel);
        }catch (Exception e){
            logger.error("Failed to get List Alternate Role ", e);
            response.setStatusDesc(ResponseStatus.FAILED.getValue());
            response.setStatus(ResponseStatus.FAILED.getCode());
        }
        return response;
    }

    public CommonResponse<OfficerNonProsperaListModel> getListNonProspera(String nik, String page, String limit){
        CommonResponse<OfficerNonProsperaListModel> response = new CommonResponse<>();
        OfficerNonProsperaListModel officerNonProsperaListModel = new OfficerNonProsperaListModel();
        try {
            response.setType(GET_LIST_OFFICER_NON_PROSPERA);
            int pageableLimit = officerNonProsperaListModel.getLimit();
            int pageablePage =   officerNonProsperaListModel.getPage();
            if (limit != null && limit.equals(LIMIT_ALL)){
                Integer count = officerNonProsperaRepository.getCountOfficer(nik);
                pageableLimit = count == 0 ? pageableLimit : count;
            }else if (limit != null && page != null){
                pageablePage = Integer.parseInt(page);
                pageableLimit = Integer.parseInt(limit);
            }
            Pageable pageable = PageRequest.of(pageablePage, pageableLimit).previous();
            Page<OfficerNonProsperaDetailModel> listOfficer = officerNonProsperaRepository.getListOfficer(nik, pageable);
            officerNonProsperaListModel.setNik(nik);
            if (!listOfficer.isEmpty()){
                officerNonProsperaListModel.setLimit(pageableLimit);
                officerNonProsperaListModel.setPage(pageablePage);
                officerNonProsperaListModel.setTotalItems(listOfficer.getTotalElements());
                officerNonProsperaListModel.setTotalPages(listOfficer.getTotalPages());
                officerNonProsperaListModel.setDetails(listOfficer.getContent());
            }
            response.setData(officerNonProsperaListModel);
        }catch (Exception e){
            logger.error("Failed to get List Alternate Role ", e);
            response.setStatusDesc(ResponseStatus.FAILED.getValue());
            response.setStatus(ResponseStatus.FAILED.getCode());
        }
        return response;
    }
    
    public CommonResponse<UserLoginModel> userLogin(String nik, String branchId) {
        CommonResponse<UserLoginModel> response = new CommonResponse<>();
        response.setType(USER_LOGIN);
        UserLoginModel userLoginModel = new UserLoginModel();
        insertAuditTrail(nik, Action.USER_LOGIN.getValue(), null, branchId);
        userLoginModel.setStatus(TrxStatus.SUCCESS.getCode());
        userLoginModel.setStatusDesc(TrxStatus.SUCCESS.getValue());
        response.setData(userLoginModel);
        return response;
    }
    public void insertAuditTrail(String nik, String action, String transactionId, String branchId){
        TrxAuditTrail trxAuditTrail = new TrxAuditTrail();
        trxAuditTrail.setNik(nik);
        trxAuditTrail.setAction(action);
        trxAuditTrail.setCreateDateTime(LocalDateTime.now());
        trxAuditTrail.setTransactionId(transactionId);
        trxAuditTrail.setBranchId(branchId != null ? branchId : "");
        trxAuditTrailRepository.save(trxAuditTrail);
    }
}
