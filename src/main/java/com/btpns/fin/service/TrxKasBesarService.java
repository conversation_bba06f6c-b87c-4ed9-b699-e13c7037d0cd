package com.btpns.fin.service;

import com.btpns.fin.configuration.AmountConfig;
import com.btpns.fin.constant.*;
import com.btpns.fin.helper.RequestHelper;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.*;
import com.btpns.fin.model.response.InputTransactionResponse;
import com.btpns.fin.model.response.BalanceStatusResponse;
import com.btpns.fin.model.response.ProgressStatusResponse;
import com.btpns.fin.repository.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.transaction.Transactional;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.constant.CommonConstant.*;
import static com.btpns.fin.helper.CommonHelper.*;

@Service
public class TrxKasBesarService {

    private static final Logger logger = LoggerFactory.getLogger(TrxKasBesarService.class);

    @Autowired
    TrxKasBesarRepository trxKasBesarRepository;

    @Autowired
    SystemParamRepository systemParamRepository;

    @Autowired
    CabangRepository cabangRepository;

    @Autowired
    OfficerRepository officerRepository;

    @Autowired
    BranchBalanceRepository branchBalanceRepository;

    @Autowired
    TrxAmountDetailRepository trxAmountDetailRepository;

    @Autowired
    RoleMenuRepository roleMenuRepository;

    @Autowired
    TrxAuditTrailRepository trxAuditTrailRepository;

    @Autowired
    HeadTellerBalanceRepository headTellerBalanceRepository;

    @Autowired
    TrxHeadTellerRepository trxHeadTellerRepository;

    @Autowired
    TrxHTAmountDetailRepository trxHTAmountDetailRepository;

    @Autowired
    TrxTellerExchangeVaultRepository trxTellerExchangeVaultRepository;

    @Autowired
    TrxPendingHeadTellerRepository trxPendingHeadTellerRepository;

    @Autowired
    EmployeeRepository employeeRepository;

    @Autowired
    UserAdminRepository userAdminRepository;
    @Autowired
    AlternateRoleRepository alternateRoleRepository;
    @Autowired
    TrxOverlimitBranchBalanceRepository trxOverlimitBranchBalanceRepository;
    @Autowired
    TrxOverlimitHTBalanceRepository trxOverlimitHTBalanceRepository;
    @Autowired
    CashOpnameRepository cashOpnameRepository;
    @Autowired
    OfficerNonProsperaRepository officerNonProsperaRepository;
    @Autowired
    EmailService emailService;
    @Autowired
    RequestHelper requestHelper;
    @Autowired
    OfficerImpl officerImpl;

    public CommonResponse<PendingApprovalModel> getPendingApproval(String nik, String branchId) {
        CommonResponse<PendingApprovalModel> response = new CommonResponse<>();
        response.setType(GET_PENDING_APPROVAL);
        PendingApprovalModel pendingApprovalModel = new PendingApprovalModel();
        LocalDate period = LocalDate.now();
            List<TrxKasBesar> listPending = trxKasBesarRepository.findAllByStatusAndCheckerNikOrInputerNik(period.minusDays(14), branchId,Set.of(TrxType.KHT.getCode(), TrxType.MHT.getCode(), TrxType.SAW.getCode(), TrxType.SAK.getCode(), TrxType.CC.getCode(), TrxType.RVKHT.getCode(), TrxType.RVMHT.getCode(),TrxType.RVCC.getCode(),TrxType.RVSAK.getCode(),TrxType.RVSAW.getCode()), TrxStatus.PENDING.getValue(), nik, nik);
            if (!listPending.isEmpty()) {
                listPending.forEach(lp -> {
                    if (lp.getId() != null) {
                        if (lp.getTrxType().equals(TrxType.KHT.getCode()) && lp.getPeriod().equals(period)) {
                            pendingApprovalModel.setKht(pendingApprovalModel.getKht() + 1);
                        } else if (lp.getTrxType().equals(TrxType.MHT.getCode()) && lp.getPeriod().equals(period)) {
                            pendingApprovalModel.setMht(pendingApprovalModel.getMht() + 1);
                        } else if (lp.getTrxType().startsWith(TrxType.RV_DASH.getCode()) && lp.getPeriod().equals(period)) {
                            pendingApprovalModel.setRv(pendingApprovalModel.getRv() + 1);
                        } else if (lp.getTrxType().equals(TrxType.MHT.getCode()) && lp.getPeriod().isBefore(period)) {
                            pendingApprovalModel.setMhtYesterday(pendingApprovalModel.getMhtYesterday() + 1);
                        } else if ((lp.getTrxType().equals(TrxType.SAW.getCode()) || (lp.getTrxType().equals(TrxType.SAK.getCode()) || (lp.getTrxType().equals(TrxType.CC.getCode())))) && lp.getPeriod().equals(period)) {
                            pendingApprovalModel.setCc(pendingApprovalModel.getCc() + 1);
                        }
                    }
                });
            }
            Integer cashOpnamePending = cashOpnameRepository.cashOpnamePendingVerification(period, nik);
            if(cashOpnamePending != 0){
                pendingApprovalModel.setCop(cashOpnamePending);
            }
            response.setData(pendingApprovalModel);
            response.setStatus(TrxStatus.SUCCESS.getCode());
            response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }

    public CommonResponse<HeadTellerTransactionModel> getHeadTellerTransaction(Profile profile, String branch, String status, String type, String transactionId) {
        CommonResponse<HeadTellerTransactionModel> response = new CommonResponse<>();
        response.setType(GET_LIST_TRANSACTION_KAS_BESAR);
        HeadTellerTransactionModel headTellerTransactionModel = new HeadTellerTransactionModel();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date period = new Date();
        LocalDate localDate = LocalDate.now();
        headTellerTransactionModel.setPeriod(sdf.format(period));
        headTellerTransactionModel.setBranchId(branch);
        SystemParam systemParam = systemParamRepository.findByParamDesc("TrxType");
        LocalDate endDate = localDate;

        if (type != null && (type.equalsIgnoreCase(TYPE_VERIFY) || (type.equalsIgnoreCase(TYPE_PENDING_TRANSACTION)))) {
            endDate = localDate.minusDays(14);
        }else {
            type="All";
        }
        List<HeadTellerTransactionDetailModel> details = trxKasBesarRepository.headTellerdetail(systemParam.getParamId(), branch, localDate, status,endDate, type, profile.getPreferred_username(), transactionId);
        headTellerTransactionModel.setDetails(details);
        response.setData(headTellerTransactionModel);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }

    public CommonResponse<SaldoAwalModel> getSaldoAwal(Profile profile, String branchId) {
        CommonResponse<SaldoAwalModel> response = new CommonResponse<>();
        response.setType(GET_SALDO_AWAL);
        SaldoAwalModel saldoAwalModel = new SaldoAwalModel();
        LocalDate localDate = LocalDate.now();
        Cabang branch = cabangRepository.findByCabangId(branchId);
        if (branch != null) {
            BranchBalance branchBalance = branchBalanceRepository.findByBranchId(branch.getCabangId());
            saldoAwalModel.setPeriod(localDate.toString());
            saldoAwalModel.setBranchId(branch.getCabangId());
            saldoAwalModel.setBranchName(branch.getCabangDesc());
            if (branchBalance != null) {
                getBeginBalanceDetail(branchBalance, saldoAwalModel);
            } else {
                saldoAwalModel.setBalanceDetail(new ArrayList<>());
                saldoAwalModel.setTotalBalance(0.0);
            }
        }
        response.setData(saldoAwalModel);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }

    public void getBeginBalanceDetail(BranchBalance branchBalance,SaldoAwalModel saldoAwal) {
        Gson gson = new Gson();
        AmountConfig amountConfig = new AmountConfig();
        double totalAmount = 0;
        List<BalanceDetailModel> balanceDetailModels = gson.fromJson(amountConfig.amount(), new TypeToken<List<BalanceDetailModel>>() {
        }.getType());
        for (BalanceDetailModel detailModel : balanceDetailModels) {
            switch (detailModel.getId()) {
                case COIN_50:
                    detailModel.setAmount(branchBalance.getC50Amount());
                    detailModel.setCount(branchBalance.getC50Count());
                    totalAmount += branchBalance.getC50Amount();
                    break;
                case COIN_100:
                    detailModel.setAmount(branchBalance.getC100Amount());
                    detailModel.setCount(branchBalance.getC100Count());
                    totalAmount += branchBalance.getC100Amount();
                    break;
                case COIN_200:
                    detailModel.setAmount(branchBalance.getC200Amount());
                    detailModel.setCount(branchBalance.getC200Count());
                    totalAmount += branchBalance.getC200Amount();
                    break;
                case COIN_500:
                    detailModel.setAmount(branchBalance.getC500Amount());
                    detailModel.setCount(branchBalance.getC500Count());
                    totalAmount += branchBalance.getC500Amount();
                    break;
                case COIN_1K:
                    detailModel.setAmount(branchBalance.getC1KAmount());
                    detailModel.setCount(branchBalance.getC1KCount());
                    totalAmount += branchBalance.getC1KAmount();
                    break;
                case PAPER_1K:
                    detailModel.setAmount(branchBalance.getP1KAmount());
                    detailModel.setCount(branchBalance.getP1KCount());
                    totalAmount += branchBalance.getP1KAmount();
                    break;
                case PAPER_2K:
                    detailModel.setAmount(branchBalance.getP2KAmount());
                    detailModel.setCount(branchBalance.getP2KCount());
                    totalAmount += branchBalance.getP2KAmount();
                    break;
                case PAPER_5K:
                    detailModel.setAmount(branchBalance.getP5KAmount());
                    detailModel.setCount(branchBalance.getP5KCount());
                    totalAmount += branchBalance.getP5KAmount();
                    break;
                case PAPER_10K:
                    detailModel.setAmount(branchBalance.getP10KAmount());
                    detailModel.setCount(branchBalance.getP10KCount());
                    totalAmount += branchBalance.getP10KAmount();
                    break;
                case PAPER_20K:
                    detailModel.setAmount(branchBalance.getP20KAmount());
                    detailModel.setCount(branchBalance.getP20KCount());
                    totalAmount += branchBalance.getP20KAmount();
                    break;
                case PAPER_50K:
                    detailModel.setAmount(branchBalance.getP50KAmount());
                    detailModel.setCount(branchBalance.getP50KCount());
                    totalAmount += branchBalance.getP50KAmount();
                    break;
                case PAPER_75K:
                    detailModel.setAmount(branchBalance.getP75KAmount());
                    detailModel.setCount(branchBalance.getP75KCount());
                    totalAmount += branchBalance.getP75KAmount();
                    break;
                case PAPER_100K:
                    detailModel.setAmount(branchBalance.getP100KAmount());
                    detailModel.setCount(branchBalance.getP100KCount());
                    totalAmount += branchBalance.getP100KAmount();
                    break;
            }
        }
        saldoAwal.setBalanceDetail(balanceDetailModels);
        saldoAwal.setTotalBalance(totalAmount);
    }

    @Transactional
    public CommonResponse<InputTransactionResponse> inputBeginBalance(InputBeginBalanceRequest request, Profile profile) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(INPUT_BEGIN_BALANCE);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        String transactionId = getTransactionId(TrxType.SAW.getCode(),LocalDate.now(),request.getBranchId(),KAS_BESAR);
        inputTransactionResponse.setRequestId(request.getRequestId());
        inputTransactionResponse.setTransactionId(transactionId);
        try {
            TrxKasBesar trxKasBesar = new TrxKasBesar();
            LocalDateTime now = LocalDateTime.now();
            checkBranchBalance(request.getBranchId(),branchBalanceRepository);
            checkHTBranchBalance(request.getBranchId(),headTellerBalanceRepository);
            PICModel pic = enrichPICName(profile.getPreferred_username(), null, officerImpl, officerNonProsperaRepository);
            trxKasBesar.setKasBesar(transactionId, LocalDate.parse(request.getPeriod()), request.getBranchId(),TrxStatus.SUCCESS.getValue(), TrxType.SAW.getCode(), profile.getPreferred_username(), null, request.getTotalBalance(), request.getTotalBalance(), now, now, null, pic.getInputerName(), pic.getTellerName() );
            trxKasBesarRepository.save(trxKasBesar);
            saveTrxDetail(request.getBalanceDetails(), request.getBranchId(), transactionId);
            inputTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
            insertAuditTrail(profile.getPreferred_username(),Action.INPUT_SALDO_AWAL.getValue(),transactionId, request.getBranchId());
        } catch (Exception e) {
            inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
        }
        response.setData(inputTransactionResponse);
        return response;
    }

    public void saveTrxDetail(List<AmountDetail> amountDetails,String branchId, String transactionId) {
        BranchBalance branchBalance = new BranchBalance();
        TrxAmountDetail trxAmountDetail = new TrxAmountDetail();
        branchBalance.setBranchId(branchId);
        trxAmountDetail.setTransactionId(transactionId);

        for (AmountDetail amountDetail : amountDetails) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    branchBalance.setC50Amount(amountDetail.getTotal());
                    branchBalance.setC50Count(amountDetail.getCount());
                    trxAmountDetail.setC50Amount(amountDetail.getTotal());
                    trxAmountDetail.setC50Count(amountDetail.getCount());
                    break;
                case COIN_100:
                    branchBalance.setC100Amount(amountDetail.getTotal());
                    branchBalance.setC100Count(amountDetail.getCount());
                    trxAmountDetail.setC100Amount(amountDetail.getTotal());
                    trxAmountDetail.setC100Count(amountDetail.getCount());
                    break;
                case COIN_200:
                    branchBalance.setC200Amount(amountDetail.getTotal());
                    branchBalance.setC200Count(amountDetail.getCount());
                    trxAmountDetail.setC200Amount(amountDetail.getTotal());
                    trxAmountDetail.setC200Count(amountDetail.getCount());
                    break;
                case COIN_500:
                    branchBalance.setC500Amount(amountDetail.getTotal());
                    branchBalance.setC500Count(amountDetail.getCount());
                    trxAmountDetail.setC500Amount(amountDetail.getTotal());
                    trxAmountDetail.setC500Count(amountDetail.getCount());
                    break;
                case COIN_1K:
                    branchBalance.setC1KAmount(amountDetail.getTotal());
                    branchBalance.setC1KCount(amountDetail.getCount());
                    trxAmountDetail.setC1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setC1KCount(amountDetail.getCount());
                    break;
                case PAPER_1K:
                    branchBalance.setP1KAmount(amountDetail.getTotal());
                    branchBalance.setP1KCount(amountDetail.getCount());
                    trxAmountDetail.setP1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP1KCount(amountDetail.getCount());
                    break;
                case PAPER_2K:
                    branchBalance.setP2KAmount(amountDetail.getTotal());
                    branchBalance.setP2KCount(amountDetail.getCount());
                    trxAmountDetail.setP2KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP2KCount(amountDetail.getCount());
                    break;
                case PAPER_5K:
                    branchBalance.setP5KAmount(amountDetail.getTotal());
                    branchBalance.setP5KCount(amountDetail.getCount());
                    trxAmountDetail.setP5KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP5KCount(amountDetail.getCount());
                    break;
                case PAPER_10K:
                    branchBalance.setP10KAmount(amountDetail.getTotal());
                    branchBalance.setP10KCount(amountDetail.getCount());
                    trxAmountDetail.setP10KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP10KCount(amountDetail.getCount());
                    break;
                case PAPER_20K:
                    branchBalance.setP20KAmount(amountDetail.getTotal());
                    branchBalance.setP20KCount(amountDetail.getCount());
                    trxAmountDetail.setP20KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP20KCount(amountDetail.getCount());
                    break;
                case PAPER_50K:
                    branchBalance.setP50KAmount(amountDetail.getTotal());
                    branchBalance.setP50KCount(amountDetail.getCount());
                    trxAmountDetail.setP50KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP50KCount(amountDetail.getCount());
                    break;
                case PAPER_75K:
                    branchBalance.setP75KAmount(amountDetail.getTotal());
                    branchBalance.setP75KCount(amountDetail.getCount());
                    trxAmountDetail.setP75KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP75KCount(amountDetail.getCount());
                    break;
                case PAPER_100K:
                    branchBalance.setP100KAmount(amountDetail.getTotal());
                    branchBalance.setP100KCount(amountDetail.getCount());
                    trxAmountDetail.setP100KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP100KCount(amountDetail.getCount());
                    break;
            }
        }
        branchBalance.setCreateDateTime(LocalDateTime.now());
        branchBalance.setUpdateDateTime(LocalDateTime.now());
        branchBalanceRepository.save(branchBalance);
        trxAmountDetailRepository.save(trxAmountDetail);
    }

    public void saveHeadTellerTrxDetail(List<AmountDetail> amountDetails,String branchId, String transactionId) {
        TrxAmountDetail trxAmountDetail = new TrxAmountDetail();
        trxAmountDetail.setTransactionId(transactionId);

        for (AmountDetail amountDetail : amountDetails) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    trxAmountDetail.setC50Amount(amountDetail.getTotal());
                    trxAmountDetail.setC50Count(amountDetail.getCount());
                    break;
                case COIN_100:
                    trxAmountDetail.setC100Amount(amountDetail.getTotal());
                    trxAmountDetail.setC100Count(amountDetail.getCount());
                    break;
                case COIN_200:
                    trxAmountDetail.setC200Amount(amountDetail.getTotal());
                    trxAmountDetail.setC200Count(amountDetail.getCount());
                    break;
                case COIN_500:
                    trxAmountDetail.setC500Amount(amountDetail.getTotal());
                    trxAmountDetail.setC500Count(amountDetail.getCount());
                    break;
                case COIN_1K:
                    trxAmountDetail.setC1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setC1KCount(amountDetail.getCount());
                    break;
                case PAPER_1K:
                    trxAmountDetail.setP1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP1KCount(amountDetail.getCount());
                    break;
                case PAPER_2K:
                    trxAmountDetail.setP2KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP2KCount(amountDetail.getCount());
                    break;
                case PAPER_5K:
                    trxAmountDetail.setP5KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP5KCount(amountDetail.getCount());
                    break;
                case PAPER_10K:
                    trxAmountDetail.setP10KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP10KCount(amountDetail.getCount());
                    break;
                case PAPER_20K:
                    trxAmountDetail.setP20KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP20KCount(amountDetail.getCount());
                    break;
                case PAPER_50K:
                    trxAmountDetail.setP50KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP50KCount(amountDetail.getCount());
                    break;
                case PAPER_75K:
                    trxAmountDetail.setP75KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP75KCount(amountDetail.getCount());
                    break;
                case PAPER_100K:
                    trxAmountDetail.setP100KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP100KCount(amountDetail.getCount());
                    break;
            }
        }
        trxAmountDetailRepository.save(trxAmountDetail);
    }

    public CommonResponse<OfficerModel> getOfficer(String nik) {
        CommonResponse<OfficerModel> response = new CommonResponse<>();
        response.setType(GET_OFFICER);
        OfficerModel officerModel = new OfficerModel();
        Comparator<RolesModel> comparator = Comparator.<RolesModel, Boolean>comparing(s -> s.getRoleId().equals(RoleConstant.BOS.getRoleId())).
                thenComparing(s -> s.getRoleId().equals(BOM_ROLE.toString())).
                thenComparing(s -> s.getRoleId().equals(TELLER_ROLE.toString())).
                thenComparing(s -> s.getRoleId().equals(PAYMENT_INPUTER.toString())).
                thenComparing(s -> s.getRoleId().equals(PAYMENT_OTORISATOR.toString())).
                thenComparing(s -> s.getRoleId().equals(ODH_ROLE)).
                thenComparing(s -> s.getRoleId().equals(QA_ROLE)).
                thenComparing(s -> s.getRoleId().equals(QAD_ROLE)).
                thenComparing(s -> s.getRoleId().equals(QAM_ROLE)).
                thenComparing(s -> s.getRoleId().equals(NOM_ROLE)).
                thenComparing(s -> s.getRoleId().equals(BM_ROLE)).
                thenComparing(s -> s.getRoleId().equals(SKAI_ROLE)).
                thenComparing(s -> s.getRoleId().equals(BO_ROLE.toString())).
                thenComparing(s -> s.getRoleId().equals(HO_ROLE.toString())).
                thenComparing(s -> s.getRoleId().equals(RoleConstant.VIEWER.getRoleId())).reversed();
        LocalDate period = LocalDate.now();
        try {
            List<RolesModel> rolesModels =officerImpl.getRoles(nik);
            rolesModels.addAll(employeeRepository.getRolesEmployee(nik));
            rolesModels.addAll(officerNonProsperaRepository.getRoles(nik));

            List<RolesModel> checkBOSRole = rolesModels.stream()
                    .filter(s -> s.getRoleId().equals(RoleConstant.BOS.getRoleId()))
                    .collect(Collectors.toList());
            if (!checkBOSRole.isEmpty()){
                AlternateRole alternateRole = alternateRoleRepository.findByNikAndStartPeriodLessThanEqualAndEndPeriodGreaterThanEqual(nik, period, period);
                if (alternateRole != null){
                    RolesModel alternateBOM = new RolesModel(BOM_ROLE.toString(), "SCS/BOM", alternateRole.getBranchId(),checkBOSRole.get(0).getName());
                    rolesModels.add(alternateBOM);
                    comparator = Comparator.<RolesModel, Boolean>comparing(s -> s.getRoleId().equals(BOM_ROLE.toString())).reversed();
                }
            }else {
                List<RolesModel> checkRoleUser = rolesModels.stream()
                        .filter(s -> s.getRoleId().equals(BOM_ROLE.toString()) ||
                                s.getRoleId().equals(TELLER_ROLE.toString()) ||
                                s.getRoleId().equals(RoleConstant.BM.getRoleId()) ||
                                s.getRoleId().equals(RoleConstant.NOM.getRoleId()) ||
                                s.getRoleId().equals(RoleConstant.QA.getRoleId()) ||
                                s.getRoleId().equals(RoleConstant.QAD.getRoleId()) ||
                                s.getRoleId().equals(RoleConstant.QAM.getRoleId()) ||
                                s.getRoleId().equals(RoleConstant.BOS.getRoleId()))
                        .collect(Collectors.toList());
                if (checkRoleUser.isEmpty()){
                    rolesModels.addAll(userAdminRepository.getViewer(nik));
                }
            }
            List<BranchModel> listBranch = new ArrayList<>();
            rolesModels.forEach(r -> {
                if (r.getRoleId().equalsIgnoreCase(BOM_ROLE.toString())) {
                    if (listBranch.stream().noneMatch(b -> b.getBranchId().equals(r.getBranchId()))) {
                        BranchModel branchModel = new BranchModel();
                        Cabang cabang = cabangRepository.findByCabangId(r.getBranchId());
                        if (!StringUtils.isEmpty(cabang)) {
                            branchModel.setBranchId(r.getBranchId());
                            branchModel.setBranchName(cabang.getCabangDesc());
                            listBranch.add(branchModel);
                        }
                    }
                }
            });
            listBranch.sort(Comparator.comparing(BranchModel::getBranchId));
            officerModel.setBranches(listBranch);
            officerModel.setRoles(rolesModels);
            officerModel.setNik(nik);
            officerModel.getRoles().sort(comparator);
            officerModel.setName(rolesModels.get(0).getName());
            List<String> roleMenu = roleMenuRepository.findByRoleId(officerModel.getRoles().get(0).getRoleId());
            officerModel.setMenu(roleMenu);
            response.setData(officerModel);
            response.setStatus(TrxStatus.SUCCESS.getCode());
            response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        }catch (Exception e){
            logger.error("Failed to get Officer ", e);
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
        }
        
        return response;
    }

    public CommonResponse<BranchOfficerModel> getBranchOfficer(String roleId, String branchId) {
        CommonResponse<BranchOfficerModel> response = new CommonResponse<>();
        response.setType(GET_BRANCH_OFFICER);
        BranchOfficerModel branchOfficerModel = new BranchOfficerModel();
        try {
            Cabang cabang = cabangRepository.findByCabangId(branchId);
            branchOfficerModel.setBranchName(cabang.getCabangDesc());
            branchOfficerModel.setBranchId(branchId);
            branchOfficerModel.setRoleId(roleId);
            if (roleId.equals(TELLER_ROLE.toString()) ||roleId.equals(BO_ROLE.toString())||roleId.equals(BOM_ROLE.toString())){
                branchOfficerModel.setOfficers(officerImpl.getListOfficerByBranchIdAndRoleID(branchId,Integer.parseInt(roleId)));
            }else {
                switch (roleId) {
                    case BM_ROLE:
                        branchOfficerModel.setOfficers(employeeRepository.findAllByRoleIDBM(RoleConstant.BM.getValue(), RoleConstant.AREAFUND.getValue(),branchId));
                        break;
                    case QA_ROLE:
                        branchOfficerModel.setOfficers(employeeRepository.findAllQARole(Set.of(RoleConstant.QA.getValue(), RoleConstant.QAD.getValue(), RoleConstant.QAM.getValue())));
                        break;
                    case BOS_ROLE:
                        List<ListOfficerModel> officer = new ArrayList<>();
                        officer.addAll(employeeRepository.findAllByRoleIDBOS(RoleConstant.BOS.getValue(),branchId));
                        officer.addAll(officerImpl.getListOfficerByBranchIdAndRoleID(branchId,BO_ROLE));
                        branchOfficerModel.setOfficers(officer);
                        break;
                    case NOM_ROLE:
                        branchOfficerModel.setOfficers(employeeRepository.findAllByRoleID(RoleConstant.NOM.getValue()));
                        break;
                }
            }
            if (branchOfficerModel.getOfficers() == null){
                branchOfficerModel.setOfficers(officerNonProsperaRepository.findAllByBranchIdAndRoleID(branchId, roleId));
            }else {
                branchOfficerModel.getOfficers().addAll(officerNonProsperaRepository.findAllByBranchIdAndRoleID(branchId, roleId));
            }
            response.setStatus(TrxStatus.SUCCESS.getCode());
            response.setStatusDesc(TrxStatus.SUCCESS.getValue());
            response.setData(branchOfficerModel);
        }catch (Exception e) {
            logger.error("Failed to get Branch Officer ", e);
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
        }

        return response;
    }

    @Transactional
    public CommonResponse<InputTransactionResponse> inputKeluarHeadTeller(InputHeadTellerRequest request, Profile profile) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(CommonConstant.SUBMIT_HEADTELLER_OUT);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        inputTransactionResponse.setRequestId(request.getRequestId());
        if (!validationDenomAndVerification(request.getAmountDetails(), request.getNikVerification())){
            logger.error("Error Submit KHT, Request not complete ");
            inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
            inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
            response.setData(inputTransactionResponse);
            inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
            inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
            return response;
        }
        LocalDate date = LocalDate.now();
        String transactionId = getTransactionId(TrxType.KHT.getCode(),date,request.getBranchId(),KAS_BESAR);
        inputTransactionResponse.setTransactionId(transactionId);
        try {
            TrxKasBesar trxKasBesar = new TrxKasBesar();
            LocalDateTime now = LocalDateTime.now();
            PICModel pic = enrichPICName(profile.getPreferred_username(), request.getNikVerification(), officerImpl, officerNonProsperaRepository);
            trxKasBesar.setKasBesar(transactionId, LocalDate.parse(request.getPeriod()), request.getBranchId(),TrxStatus.PENDING.getValue(), TrxType.KHT.getCode(), profile.getPreferred_username(), request.getNikVerification(), request.getTotalAmount(), request.getTotalAmount(), now, now, request.getRequestId(), pic.getInputerName(), pic.getTellerName());
            trxKasBesarRepository.save(trxKasBesar);
            saveHeadTellerTrxDetail(request.getAmountDetails(), request.getBranchId(), transactionId);
            if (!StringUtils.isEmpty(request.getTrxIdTEVault())) {
                TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findByTransactionId(request.getTrxIdTEVault());
                if (vault != null) {
                    vault.setRefId(trxKasBesar.getTransactionId());
                    trxTellerExchangeVaultRepository.save(vault);
                }
            }
            inputTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
            insertAuditTrail(profile.getPreferred_username(),Action.INPUT_KHT.getValue(),transactionId, request.getBranchId());
        } catch (Exception e) {
            inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
        }
        response.setData(inputTransactionResponse);
        return response;
    }

    public String getTransactionId(String trxType,LocalDate date,String branchId,String process) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        String transactionNo = null;
        if (process.equals(KAS_BESAR)) {
            Integer lastNo = trxKasBesarRepository.countTransaction(date, trxType, branchId);
            transactionNo = lastNo != null ? String.format("%02d", lastNo + 1) : null;
        }else if (process.equals(HEAD_TELLER)){
            Integer lastHeadTeller = trxHeadTellerRepository.getLastTrxHeadTellerTransactionId(branchId, trxType,date);
            if (lastHeadTeller!=null){
                transactionNo = String.format("%02d",  lastHeadTeller+ 1);
            }
        }else if (process.equals(PENDING_HEAD_TELLER)){
            Integer trxNo = trxPendingHeadTellerRepository.getLastTransactionId(branchId,trxType,date) ;
            transactionNo = trxNo != null ? String.format("%02d", trxNo + 1) : null;
        }
        if (transactionNo==null){
            transactionNo=String.format("%02d",1);
        }
        return trxType+formatter.format(date)+branchId+transactionNo;
    }

    public CommonResponse<HeadTellerOutDetail> getHeadTellerOutDetail(String transactionId,Profile profile){
        CommonResponse<HeadTellerOutDetail> response = new CommonResponse<>();
        response.setType(GET_DETAIL_KHT);
        
        HeadTellerOutDetail headTellerOutDetail = new HeadTellerOutDetail();
        TransactionKasBesar kasBesar = trxKasBesarRepository.kasBesar(transactionId);
        Cabang cabang = cabangRepository.findByCabangId(kasBesar.getTrxKasBesar().getBranchId());
        TrxKasBesar beginBalance = trxKasBesarRepository.findFirstByPeriodAndBranchIdAndTrxTypeInAndCreateDateTimeLessThanAndStatusInOrderByCreateDateTimeDesc(kasBesar.getTrxKasBesar().getPeriod(),kasBesar.getTrxKasBesar().getBranchId(),Set.of(TrxType.SA.getCode(),TrxType.SAW.getCode(),TrxType.SAK.getCode(), TrxType.CC.getCode(), TrxType.CASHOPNAME.getCode()),kasBesar.getTrxKasBesar().getCreateDateTime(),Set.of(TrxStatus.SUCCESS.getValue(),TrxStatus.APPROVED.getValue()));
        TrxAmountDetail details = trxAmountDetailRepository.findByTransactionId(beginBalance.getTransactionId());
        headTellerOutDetail.setTransactionId(transactionId);
        headTellerOutDetail.setPeriod(kasBesar.getTrxKasBesar().getPeriod().toString());
        headTellerOutDetail.setBranch(cabang.getCabangId().concat(" - ").concat(cabang.getCabangDesc()));
        headTellerOutDetail.setNikVerification(kasBesar.getTrxKasBesar().getChecker());
        headTellerOutDetail.setNameVerification(kasBesar.getTrxKasBesar().getCheckerName());
        headTellerOutDetail.setStatus(kasBesar.getTrxKasBesar().getStatus());
        headTellerOutDetail.setReason(kasBesar.getTrxKasBesar().getReason());
        headTellerOutDetail.setTotalBeginBalance(beginBalance.getBalanceAmount());
        headTellerOutDetail.setTotalOutBalance(kasBesar.getTrxKasBesar().getBalanceAmount());
        headTellerOutDetail.setTotalRemainingBalance(headTellerOutDetail.getTotalBeginBalance()-headTellerOutDetail.getTotalOutBalance());
        getHeadTellerAmountDetail(headTellerOutDetail,details,kasBesar.getTrxAmountDetail());
        response.setData(headTellerOutDetail);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }

    public CommonResponse<HeadTellerInDetail> getHeadTellerInDetail(String transactionId, Profile profile){
        CommonResponse<HeadTellerInDetail> response = new CommonResponse<>();
        response.setType(GET_DETAIL_MHT);
        HeadTellerInDetail headTellerInDetail = new HeadTellerInDetail();
        TransactionKasBesar headTellerDetail = trxKasBesarRepository.kasBesar(transactionId);
        Cabang cabang = cabangRepository.findByCabangId(headTellerDetail.getTrxKasBesar().getBranchId());
        Set<String> status = Set.of(TrxStatus.SUCCESS.getValue(), TrxStatus.APPROVED.getValue()) ;
        Set<String> type = Set.of(TrxType.SA.getCode(), TrxType.SAW.getCode(), TrxType.SAK.getCode(), TrxType.CC.getCode(), TrxType.CASHOPNAME.getCode());
        TrxKasBesar beginBalance = trxKasBesarRepository.findFirstByPeriodGreaterThanEqualAndCreateDateTimeLessThanAndBranchIdAndTrxTypeInAndStatusInOrderByCreateDateTimeDesc(
                headTellerDetail.getTrxKasBesar().getPeriod().minusDays(14), headTellerDetail.getTrxKasBesar().getCreateDateTime(), headTellerDetail.getTrxKasBesar().getBranchId(), type , status);
        TrxAmountDetail detail = trxAmountDetailRepository.findByTransactionId(beginBalance.getTransactionId());
        headTellerInDetail.setTransactionId(transactionId);
        headTellerInDetail.setPeriod(headTellerDetail.getTrxKasBesar().getPeriod().toString());
        headTellerInDetail.setBranch(cabang.getCabangId().concat(" - ").concat(cabang.getCabangDesc()));
        headTellerInDetail.setNikVerification(headTellerDetail.getTrxKasBesar().getChecker());
        headTellerInDetail.setNameVerification(headTellerDetail.getTrxKasBesar().getCheckerName());
        headTellerInDetail.setStatus(headTellerDetail.getTrxKasBesar().getStatus());
        headTellerInDetail.setReason(headTellerDetail.getTrxKasBesar().getReason());
        headTellerInDetail.setTotalBeginBalance(beginBalance.getBalanceAmount());
        headTellerInDetail.setTotalOutBalance(headTellerDetail.getTrxKasBesar().getBalanceAmount());
        headTellerInDetail.setTotalRemainingBalance(headTellerInDetail.getTotalBeginBalance() + headTellerInDetail.getTotalOutBalance());
        getHeadTellerAmountDetail(headTellerInDetail, detail, headTellerDetail.getTrxAmountDetail());
        response.setData(headTellerInDetail);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }

    public CommonResponse<PendingHeadTellerOut> getHeadTellerOutPending(String branch, Profile profile){
        CommonResponse<PendingHeadTellerOut> response = new CommonResponse<>();
        response.setType(GET_PENDING_KHT);
        LocalDate period = LocalDate.now();
        PendingHeadTellerOut pendingHeadTellerOut = new PendingHeadTellerOut();

        TrxKasBesar kasBesar = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeInAndStatusOrderByCreateDateTimeDesc(
                period, branch, Set.of(TrxType.KHT.getCode(), TrxType.CC.getCode(), TrxType.SAW.getCode(), TrxType.SAK.getCode(), TrxType.CASHOPNAME.getCode(), TrxType.RVKHT.getCode(), TrxType.RVMHT.getCode(),TrxType.RVSAW.getCode(), TrxType.RVSAK.getCode(), TrxType.RVCC.getCode()), TrxStatus.PENDING.getValue());
        if (kasBesar != null) {
            pendingHeadTellerOut.setIsExists(true).setBranchId(branch).setPeriod(period).setLastTransactionId(kasBesar.getTransactionId());
        } else {
            pendingHeadTellerOut.setIsExists(false).setBranchId(branch).setPeriod(period);
        }
        response.setData(pendingHeadTellerOut);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }

    public void getHeadTellerAmountDetail(HeadTellerOutDetail headTellerOutDetail,TrxAmountDetail beginBalance,TrxAmountDetail trxAmountDetail){
        Gson gson = new Gson();
        AmountConfig amountConfig = new AmountConfig();
        List<HeadTellerAmountDetail> headTellerAmountDetails = gson.fromJson(amountConfig.amount(), new TypeToken<List<HeadTellerAmountDetail>>() {
        }.getType());
        for (HeadTellerAmountDetail amountDetail : headTellerAmountDetails) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    amountDetail.setAmountCount(trxAmountDetail.getC50Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC50Amount());
                    amountDetail.setBeginBalance(beginBalance.getC50Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_100:
                    amountDetail.setAmountCount(trxAmountDetail.getC100Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC100Amount());
                    amountDetail.setBeginBalance(beginBalance.getC100Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_200:
                    amountDetail.setAmountCount(trxAmountDetail.getC200Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC200Amount());
                    amountDetail.setBeginBalance(beginBalance.getC200Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_500:
                    amountDetail.setAmountCount(trxAmountDetail.getC500Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC500Amount());
                    amountDetail.setBeginBalance(beginBalance.getC500Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_1K:
                    amountDetail.setAmountCount(trxAmountDetail.getC1KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getC1KAmount());
                    amountDetail.setBeginBalance(beginBalance.getC1KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_1K:
                    amountDetail.setAmountCount(trxAmountDetail.getP1KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP1KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP1KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_2K:
                    amountDetail.setAmountCount(trxAmountDetail.getP2KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP2KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP2KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_5K:
                    amountDetail.setAmountCount(trxAmountDetail.getP5KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP5KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP5KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_10K:
                    amountDetail.setAmountCount(trxAmountDetail.getP10KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP10KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP10KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_20K:
                    amountDetail.setAmountCount(trxAmountDetail.getP20KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP20KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP20KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_50K:
                    amountDetail.setAmountCount(trxAmountDetail.getP50KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP50KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP50KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_75K:
                    amountDetail.setAmountCount(trxAmountDetail.getP75KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP75KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP75KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_100K:
                    amountDetail.setAmountCount(trxAmountDetail.getP100KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP100KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP100KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
            }
        }
        headTellerOutDetail.setAmountDetails(headTellerAmountDetails);
    }

    public void getHeadTellerAmountDetail(HeadTellerInDetail headTellerInDetail,TrxAmountDetail beginBalance,TrxAmountDetail trxAmountDetail){
        Gson gson = new Gson();
        AmountConfig amountConfig = new AmountConfig();
        List<HeadTellerAmountDetail> headTellerAmountDetails = gson.fromJson(amountConfig.amount(), new TypeToken<List<HeadTellerAmountDetail>>() {
        }.getType());
        for (HeadTellerAmountDetail amountDetail : headTellerAmountDetails) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    amountDetail.setAmountCount(trxAmountDetail.getC50Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC50Amount());
                    amountDetail.setBeginBalance(beginBalance.getC50Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_100:
                    amountDetail.setAmountCount(trxAmountDetail.getC100Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC100Amount());
                    amountDetail.setBeginBalance(beginBalance.getC100Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_200:
                    amountDetail.setAmountCount(trxAmountDetail.getC200Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC200Amount());
                    amountDetail.setBeginBalance(beginBalance.getC200Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_500:
                    amountDetail.setAmountCount(trxAmountDetail.getC500Count());
                    amountDetail.setAmountTotal(trxAmountDetail.getC500Amount());
                    amountDetail.setBeginBalance(beginBalance.getC500Amount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case COIN_1K:
                    amountDetail.setAmountCount(trxAmountDetail.getC1KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getC1KAmount());
                    amountDetail.setBeginBalance(beginBalance.getC1KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_1K:
                    amountDetail.setAmountCount(trxAmountDetail.getP1KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP1KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP1KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_2K:
                    amountDetail.setAmountCount(trxAmountDetail.getP2KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP2KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP2KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_5K:
                    amountDetail.setAmountCount(trxAmountDetail.getP5KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP5KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP5KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_10K:
                    amountDetail.setAmountCount(trxAmountDetail.getP10KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP10KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP10KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_20K:
                    amountDetail.setAmountCount(trxAmountDetail.getP20KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP20KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP20KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_50K:
                    amountDetail.setAmountCount(trxAmountDetail.getP50KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP50KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP50KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_75K:
                    amountDetail.setAmountCount(trxAmountDetail.getP75KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP75KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP75KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
                case PAPER_100K:
                    amountDetail.setAmountCount(trxAmountDetail.getP100KCount());
                    amountDetail.setAmountTotal(trxAmountDetail.getP100KAmount());
                    amountDetail.setBeginBalance(beginBalance.getP100KAmount());
                    amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                    break;
            }
        }
        headTellerInDetail.setAmountDetails(headTellerAmountDetails);
    }

    @Transactional
    public String insertTodayBeginBalance(String branchId,LocalDate date,Profile profile, String checkerNik){
        BranchBalance branchBalance = branchBalanceRepository.findByBranchId(branchId);
        Double totalAmount = branchBalance.getTotal();
        TrxKasBesar trxKasBesar = new TrxKasBesar();
        String transactionId = getTransactionId(TrxType.SAW.getCode(), date, branchId,KAS_BESAR);
        TrxAmountDetail trxAmountDetail = new TrxAmountDetail();
        LocalDateTime now = LocalDateTime.now();
        PICModel pic = enrichPICName(profile.getPreferred_username(), checkerNik, officerImpl, officerNonProsperaRepository);
        trxKasBesar.setKasBesar(transactionId,date,branchId,TrxStatus.SUCCESS.getValue(), TrxType.SAW.getCode(), profile.getPreferred_username(),
                checkerNik, totalAmount, totalAmount,now,now,null, pic.getInputerName(), pic.getTellerName());
        trxAmountDetail.setAmountDetail(transactionId,branchBalance);
        trxKasBesarRepository.save(trxKasBesar);
        trxAmountDetailRepository.save(trxAmountDetail);
        return trxKasBesar.getTransactionId();
    }

    @Transactional
    public CommonResponse<InputTransactionResponse> headTellerApproval(HeadTellerApprovalReq req, Profile profile, boolean isAdmin){
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(CommonConstant.SUBMIT_APPROVAL);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        TransactionKasBesar transaction = trxKasBesarRepository.kasBesar(req.getApprovalTransactionId());
        String type = transaction.getTrxKasBesar().getTrxType();
        LocalDate period = LocalDate.parse(req.getPeriod());

        try{
            if (!validateVerification(transaction.getTrxKasBesar().getChecker(), req.getNikVerification())) {
                inputTransactionResponse.setRequestId(req.getRequestId());
                inputTransactionResponse.setTransactionId(req.getApprovalTransactionId());
                inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
                inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
                response.setStatus(ResponseStatus.FAILED.getCode());
                response.setStatusDesc(ResponseStatus.FAILED.getValue());
                return response;
            }
            if(!transaction.getTrxKasBesar().getStatus().equals(TrxStatus.APPROVED.getValue()) && !transaction.getTrxKasBesar().getStatus().equals(TrxStatus.REJECTED.getValue())) {
                if (req.getStatus().equals(TrxStatus.APPROVED.getValue())){
                    transaction.getTrxKasBesar().setStatus(req.getStatus());
                    if (!transaction.getTrxKasBesar().getPeriod().equals(period)){
                        transaction.getTrxKasBesar().setPeriod(period);
                        transaction.getTrxKasBesar().setAdditionalInfo("Tanggal Pengajuan " + transaction.getTrxKasBesar().getPeriod());
                    }
                    BranchBalance branchBalance = branchBalanceRepository.findByBranchId(req.getBranchId());

                    branchBalance.setC50Count(getCount(branchBalance.getC50Count(), transaction.getTrxAmountDetail().getC50Count(), type));
                    branchBalance.setC50Amount(getAmount(branchBalance.getC50Amount(), transaction.getTrxAmountDetail().getC50Amount(), type));
                    branchBalance.setC100Count(getCount(branchBalance.getC100Count(), transaction.getTrxAmountDetail().getC100Count(), type));
                    branchBalance.setC100Amount(getAmount(branchBalance.getC100Amount(), transaction.getTrxAmountDetail().getC100Amount(), type));
                    branchBalance.setC200Count(getCount(branchBalance.getC200Count(), transaction.getTrxAmountDetail().getC200Count(), type));
                    branchBalance.setC200Amount(getAmount(branchBalance.getC200Amount(), transaction.getTrxAmountDetail().getC200Amount(), type));
                    branchBalance.setC500Count(getCount(branchBalance.getC500Count(), transaction.getTrxAmountDetail().getC500Count(), type));
                    branchBalance.setC500Amount(getAmount(branchBalance.getC500Amount(), transaction.getTrxAmountDetail().getC500Amount(), type));
                    branchBalance.setC1KCount(getCount(branchBalance.getC1KCount(), transaction.getTrxAmountDetail().getC1KCount(), type));
                    branchBalance.setC1KAmount(getAmount(branchBalance.getC1KAmount(), transaction.getTrxAmountDetail().getC1KAmount(), type));
                    branchBalance.setP1KCount(getCount(branchBalance.getP1KCount(), transaction.getTrxAmountDetail().getP1KCount(), type));
                    branchBalance.setP1KAmount(getAmount(branchBalance.getP1KAmount(), transaction.getTrxAmountDetail().getP1KAmount(), type));
                    branchBalance.setP2KCount(getCount(branchBalance.getP2KCount(), transaction.getTrxAmountDetail().getP2KCount(), type));
                    branchBalance.setP2KAmount(getAmount(branchBalance.getP2KAmount(), transaction.getTrxAmountDetail().getP2KAmount(), type));
                    branchBalance.setP5KCount(getCount(branchBalance.getP5KCount(), transaction.getTrxAmountDetail().getP5KCount(), type));
                    branchBalance.setP5KAmount(getAmount(branchBalance.getP5KAmount(), transaction.getTrxAmountDetail().getP5KAmount(), type));
                    branchBalance.setP10KCount(getCount(branchBalance.getP10KCount(), transaction.getTrxAmountDetail().getP10KCount(), type));
                    branchBalance.setP10KAmount(getAmount(branchBalance.getP10KAmount(), transaction.getTrxAmountDetail().getP10KAmount(), type));
                    branchBalance.setP20KCount(getCount(branchBalance.getP20KCount(), transaction.getTrxAmountDetail().getP20KCount(), type));
                    branchBalance.setP20KAmount(getAmount(branchBalance.getP20KAmount(), transaction.getTrxAmountDetail().getP20KAmount(), type));
                    branchBalance.setP50KCount(getCount(branchBalance.getP50KCount(), transaction.getTrxAmountDetail().getP50KCount(), type));
                    branchBalance.setP50KAmount(getAmount(branchBalance.getP50KAmount(), transaction.getTrxAmountDetail().getP50KAmount(), type));
                    branchBalance.setP75KCount(getCount(branchBalance.getP75KCount(), transaction.getTrxAmountDetail().getP75KCount(), type));
                    branchBalance.setP75KAmount(getAmount(branchBalance.getP75KAmount(), transaction.getTrxAmountDetail().getP75KAmount(), type));
                    branchBalance.setP100KCount(getCount(branchBalance.getP100KCount(), transaction.getTrxAmountDetail().getP100KCount(), type));
                    branchBalance.setP100KAmount(getAmount(branchBalance.getP100KAmount(), transaction.getTrxAmountDetail().getP100KAmount(), type));

                    branchBalanceRepository.save(branchBalance);
                    Double totalAmount = branchBalance.getTotal();
                    TrxKasBesar trxKasBesarApp = new TrxKasBesar();
                    String trxKasBesarAppTransactionId = getTransactionId(TrxType.APR.getCode(), LocalDate.parse(req.getPeriod()),req.getBranchId(),KAS_BESAR);
                    LocalDateTime now = LocalDateTime.now();
                    PICModel picApprove = enrichPICName(transaction.getTrxKasBesar().getInputer(), profile.getPreferred_username(), officerImpl, officerNonProsperaRepository);
                    trxKasBesarApp.setKasBesar(trxKasBesarAppTransactionId, LocalDate.parse(req.getPeriod()), req.getBranchId(), req.getStatus(), TrxType.APR.getCode(), profile.getPreferred_username(), req.getNikVerification(), totalAmount, transaction.getTrxAmountDetail().getTotal(), now, now , null, picApprove.getTellerName(), picApprove.getTellerName());
                    transaction.getTrxKasBesar().setRequestId(req.getRequestId());
                    TrxAmountDetail trxAmountDetail = new TrxAmountDetail();
                    trxAmountDetail.setAmountDetail(trxKasBesarApp.getTransactionId(),branchBalance);
                    trxKasBesarRepository.save(trxKasBesarApp);
                    trxAmountDetailRepository.save(trxAmountDetail);
                    checkExistingSaldoAkhir(LocalDate.parse(req.getPeriod()),req.getBranchId());
                    TrxKasBesar trxKasBesarSaldo = new TrxKasBesar();
                    String transactionIdTrxKasBesarSaldo =getTransactionId(getSaldoType(TrxType.SA.getCode(),TrxType.SAK.getCode(),type), LocalDate.parse(req.getPeriod()),req.getBranchId(),KAS_BESAR);
                    trxKasBesarSaldo.setKasBesar(transactionIdTrxKasBesarSaldo, LocalDate.parse(req.getPeriod()), req.getBranchId(), TrxStatus.SUCCESS.getValue(), getSaldoType(TrxType.SA.getCode(),TrxType.SAK.getCode(),type), transaction.getTrxKasBesar().getInputer(), req.getNikVerification(), totalAmount, transaction.getTrxAmountDetail().getTotal(), now, now,null , picApprove.getInputerName(), picApprove.getTellerName());
                    TrxAmountDetail detailSaldo = new TrxAmountDetail();
                    detailSaldo.setAmountDetail(trxKasBesarSaldo.getTransactionId(),branchBalance);
                    trxKasBesarRepository.save(trxKasBesarSaldo);
                    trxAmountDetailRepository.save(detailSaldo);

                    processVaultAndHt(transaction.getTrxKasBesar().getInputer(), transaction.getTrxKasBesar().getBranchId(),transaction.getTrxAmountDetail(),transaction.getTrxKasBesar().getTotalAmount(),req.getApprovalTransactionId(), type);
                    recalculateHeadTeller(req.getBranchId(), type.equals(TrxType.KHT.getCode()) ? TrxType.HT2T.getCode() : TrxType.T2HT.getCode(), profile.getPreferred_username());
                    insertAuditTrail(profile.getPreferred_username(),getTrxType(Action.APPROVE_KHT.getValue(), Action.APPROVE_MHT.getValue(), type),req.getApprovalTransactionId(), req.getBranchId());
                }else if (req.getStatus().equals(TrxStatus.REJECTED.getValue())){
                    transaction.getTrxKasBesar().setStatus(req.getStatus());
                    transaction.getTrxKasBesar().setReason(encodeIfnotNull(req.getReason()));
                    trxKasBesarRepository.save(transaction.getTrxKasBesar());
                    if (isAdmin){
                        insertAuditTrail(profile.getPreferred_username(), getTrxType(Action.REJECT_KHT.getValue(), Action.REJECT_MHT.getValue().concat(ACTION_ADMIN), type),req.getApprovalTransactionId(), req.getBranchId());
                    }else {
                        insertAuditTrail(profile.getPreferred_username(), getTrxType(Action.REJECT_KHT.getValue(), Action.REJECT_MHT.getValue(), type),req.getApprovalTransactionId(), req.getBranchId());
                    }
                }
                statusMHTApprove(transaction.getTrxKasBesar().getTransactionId(),type,req.getStatus());
                approveKHTTellerExchange(req,transaction.getTrxKasBesar());
            }
            inputTransactionResponse.setRequestId(req.getRequestId());
            inputTransactionResponse.setTransactionId(transaction.getTrxKasBesar().getTransactionId());
            inputTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
            response.setData(inputTransactionResponse);
            return response;

        }catch (Exception e){
            logger.error("Failed to Approval headteller " + e.getMessage());
            inputTransactionResponse.setTransactionId(req.getApprovalTransactionId());
            inputTransactionResponse.setRequestId(req.getRequestId());
            inputTransactionResponse.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
            inputTransactionResponse.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
            response.setData(inputTransactionResponse);
            response.setStatus(ResponseStatus.FAILED.getCode());
            response.setStatusDesc(ResponseStatus.FAILED.getValue());
            return response;
        }
    }
    public void insertAuditTrail(String nik, String action, String transactionId, String branchId){
        TrxAuditTrail trxAuditTrail = new TrxAuditTrail();
        trxAuditTrail.setNik(nik);
        trxAuditTrail.setAction(action);
        trxAuditTrail.setCreateDateTime(LocalDateTime.now());
        trxAuditTrail.setTransactionId(transactionId);
        trxAuditTrail.setBranchId(branchId);
        trxAuditTrailRepository.save(trxAuditTrail);
    }

    public void processVaultAndHt(String nik,String branchId,TrxAmountDetail balance,Double totalAmount,String trxKhtId, String type){

        HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
        LocalDate period = LocalDate.now();
        PICModel pic = enrichPICName(nik, null, officerImpl, officerNonProsperaRepository);
        boolean overlimitHT = false;

        if (headTellerBalance==null && type.equals(TrxType.KHT.getCode())){
            TrxHeadTeller saldoAwal = new TrxHeadTeller();
            LocalDateTime now = LocalDateTime.now();
            String saldoAwalTransactionId =getTransactionId(TrxType.SWHT.getCode(),period,branchId,HEAD_TELLER);
            saldoAwal.setHeadTeller(saldoAwalTransactionId, branchId, period, TrxType.SWHT.getCode(), TrxStatus.SUCCESS.getValue(), nik, null, 0.0, 0.0, now, now, null );
            saldoAwal.setInputerName(pic.getInputerName());

            TrxHTAmountDetail saldoAwalDetail = new TrxHTAmountDetail();
            saldoAwalDetail.setTransactionId(saldoAwal.getTransactionId());
            saldoAwalDetail.setC50Amount(0.0);
            saldoAwalDetail.setC50Count(0);
            saldoAwalDetail.setC100Amount(0.0);
            saldoAwalDetail.setC100Count(0);
            saldoAwalDetail.setC200Amount(0.0);
            saldoAwalDetail.setC200Count(0);
            saldoAwalDetail.setC500Amount(0.0);
            saldoAwalDetail.setC500Count(0);
            saldoAwalDetail.setC1KAmount(0.0);
            saldoAwalDetail.setC1KCount(0);
            saldoAwalDetail.setP1KAmount(0.0);
            saldoAwalDetail.setP1KCount(0);
            saldoAwalDetail.setP2KAmount(0.0);
            saldoAwalDetail.setP2KCount(0);
            saldoAwalDetail.setP5KAmount(0.0);
            saldoAwalDetail.setP5KCount(0);
            saldoAwalDetail.setP10KAmount(0.0);
            saldoAwalDetail.setP10KCount(0);
            saldoAwalDetail.setP20KAmount(0.0);
            saldoAwalDetail.setP20KCount(0);
            saldoAwalDetail.setP50KAmount(0.0);
            saldoAwalDetail.setP50KCount(0);
            saldoAwalDetail.setP75KAmount(0.0);
            saldoAwalDetail.setP75KCount(0);
            saldoAwalDetail.setP100KAmount(0.0);
            saldoAwalDetail.setP100KCount(0);
            trxHeadTellerRepository.save(saldoAwal);
            trxHTAmountDetailRepository.save(saldoAwalDetail);
            TrxHeadTeller transaction = new TrxHeadTeller();
            String transactionId = getTransactionId(TrxType.V2HT.getCode(),period,branchId,HEAD_TELLER);
            transaction.setHeadTeller(transactionId, branchId, period, TrxType.V2HT.getCode(), TrxStatus.SUCCESS.getValue(), nik, null, totalAmount, totalAmount, now, now, trxKhtId );
            transaction.setInputerName(pic.getInputerName());

            trxHeadTellerRepository.save(transaction);
            TrxHTAmountDetail saldoTransaction = new TrxHTAmountDetail();
            saldoTransaction.setTransactionId(transaction.getTransactionId());
            saldoTransaction.setAmount(balance);
            trxHTAmountDetailRepository.save(saldoTransaction);
            checkExistingSaldoAkhirHeadTeller(LocalDate.now(),branchId,trxHeadTellerRepository, trxHTAmountDetailRepository);
            TrxHeadTeller trxSaldo = new TrxHeadTeller();
            trxSaldo.setHeadTeller(getTransactionId(TrxType.SKHT.getCode(),period,branchId,HEAD_TELLER), branchId, period, TrxType.SKHT.getCode(),
                    TrxStatus.SUCCESS.getValue(), nik, null, totalAmount, totalAmount, now, now, null );
            trxSaldo.setInputerName(pic.getInputerName());
            trxHeadTellerRepository.save(trxSaldo);
            TrxHTAmountDetail saldo = new TrxHTAmountDetail();
            saldo.setTransactionId(trxSaldo.getTransactionId());
            saldo.setAmount(balance);
            trxHTAmountDetailRepository.save(saldo);
            headTellerBalance = new HeadTellerBalance();
            headTellerBalance.setBranchId(branchId);
            headTellerBalance.setNikHT(nik);
            headTellerBalance.setC50Amount(balance.getC50Amount());
            headTellerBalance.setC50Count(balance.getC50Count());
            headTellerBalance.setC100Amount(balance.getC100Amount());
            headTellerBalance.setC100Count(balance.getC100Count());
            headTellerBalance.setC200Amount(balance.getC200Amount());
            headTellerBalance.setC200Count(balance.getC200Count());
            headTellerBalance.setC500Amount(balance.getC500Amount());
            headTellerBalance.setC500Count(balance.getC500Count());
            headTellerBalance.setC1KAmount(balance.getC1KAmount());
            headTellerBalance.setC1KCount(balance.getC1KCount());
            headTellerBalance.setP1KAmount(balance.getP1KAmount());
            headTellerBalance.setP1KCount(balance.getP1KCount());
            headTellerBalance.setP2KAmount(balance.getP2KAmount());
            headTellerBalance.setP2KCount(balance.getP2KCount());
            headTellerBalance.setP5KAmount(balance.getP5KAmount());
            headTellerBalance.setP5KCount(balance.getP5KCount());
            headTellerBalance.setP10KAmount(balance.getP10KAmount());
            headTellerBalance.setP10KCount(balance.getP10KCount());
            headTellerBalance.setP20KAmount(balance.getP20KAmount());
            headTellerBalance.setP20KCount(balance.getP20KCount());
            headTellerBalance.setP50KAmount(balance.getP50KAmount());
            headTellerBalance.setP50KCount(balance.getP50KCount());
            headTellerBalance.setP75KAmount(balance.getP75KAmount());
            headTellerBalance.setP75KCount(balance.getP75KCount());
            headTellerBalance.setP100KAmount(balance.getP100KAmount());
            headTellerBalance.setP100KCount(balance.getP100KCount());
            headTellerBalance.setTotalAmount(totalAmount);
        }else {
            TrxHeadTeller trxHeadTeller = trxHeadTellerRepository.findByPeriodAndBranchIdAndType(period,branchId,TrxType.SWHT.getCode());
            if (trxHeadTeller==null && type.equals(TrxType.KHT.getCode())){
                TrxHeadTeller saldoAwal = new TrxHeadTeller();
                saldoAwal.setHeadTeller(getTransactionId(TrxType.SWHT.getCode(),period,branchId,HEAD_TELLER), branchId,period,TrxType.SWHT.getCode(),TrxStatus.SUCCESS.getValue(),nik,null,headTellerBalance.getTotalAmount(),headTellerBalance.getTotalAmount(),LocalDateTime.now(), LocalDateTime.now(),null);
                saldoAwal.setInputerName(pic.getInputerName());
                TrxHTAmountDetail saldoAwalDetail = new TrxHTAmountDetail();
                saldoAwalDetail.setTransactionId(saldoAwal.getTransactionId());
                saldoAwalDetail.setAmount(headTellerBalance);
                trxHeadTellerRepository.save(saldoAwal);
                trxHTAmountDetailRepository.save(saldoAwalDetail);
            }
            if (type.equals(TrxType.KHT.getCode())){
                TrxHeadTeller transaction = new TrxHeadTeller();
                transaction.setTransactionId(getTransactionId(TrxType.V2HT.getCode(),period,branchId,HEAD_TELLER));
                transaction.setBranchId(branchId);
                transaction.setPeriod(period);
                transaction.setType(TrxType.V2HT.getCode());
                transaction.setStatus(TrxStatus.SUCCESS.getValue());
                transaction.setInputer(nik);
                transaction.setInputerName(pic.getInputerName());
                transaction.setTotalAmount(totalAmount);
                transaction.setBalance(headTellerBalance.getTotalAmount()+totalAmount);
                transaction.setCreateDateTime(LocalDateTime.now());
                transaction.setUpdateDateTime(LocalDateTime.now());
                transaction.setRefId(trxKhtId);
                trxHeadTellerRepository.save(transaction);
                TrxHTAmountDetail saldoTransaction = new TrxHTAmountDetail();
                saldoTransaction.setTransactionId(transaction.getTransactionId());
                saldoTransaction.setAmount(balance);
                trxHTAmountDetailRepository.save(saldoTransaction);
                checkExistingSaldoAkhirHeadTeller(LocalDate.now(),branchId,trxHeadTellerRepository, trxHTAmountDetailRepository);
                TrxHeadTeller trxSaldo = new TrxHeadTeller();
                trxSaldo.setTransactionId(getTransactionId(TrxType.SKHT.getCode(),period,branchId,HEAD_TELLER));
                trxSaldo.setBranchId(branchId);
                trxSaldo.setPeriod(period);
                trxSaldo.setType(TrxType.SKHT.getCode());
                trxSaldo.setStatus(TrxStatus.SUCCESS.getValue());
                trxSaldo.setInputer(nik);
                trxSaldo.setInputerName(pic.getInputerName());
                trxSaldo.setTotalAmount(headTellerBalance.getTotalAmount()+totalAmount);
                trxSaldo.setBalance(headTellerBalance.getTotalAmount()+totalAmount);
                trxSaldo.setCreateDateTime(LocalDateTime.now());
                trxSaldo.setUpdateDateTime(LocalDateTime.now());
                trxHeadTellerRepository.save(trxSaldo);
                TrxHTAmountDetail saldo = new TrxHTAmountDetail();
                saldo.setTransactionId(trxSaldo.getTransactionId());
                saldo.setC50Amount(headTellerBalance.getC50Amount()+balance.getC50Amount());
                saldo.setC50Count(headTellerBalance.getC50Count()+balance.getC50Count());
                saldo.setC100Amount(headTellerBalance.getC100Amount()+balance.getC100Amount());
                saldo.setC100Count(headTellerBalance.getC100Count()+balance.getC100Count());
                saldo.setC200Amount(headTellerBalance.getC200Amount()+balance.getC200Amount());
                saldo.setC200Count(headTellerBalance.getC200Count()+balance.getC200Count());
                saldo.setC500Amount(headTellerBalance.getC500Amount()+balance.getC500Amount());
                saldo.setC500Count(headTellerBalance.getC500Count()+balance.getC500Count());
                saldo.setC1KAmount(headTellerBalance.getC1KAmount()+balance.getC1KAmount());
                saldo.setC1KCount(headTellerBalance.getC1KCount()+balance.getC1KCount());
                saldo.setP1KAmount(headTellerBalance.getP1KAmount()+balance.getP1KAmount());
                saldo.setP1KCount(headTellerBalance.getP1KCount()+balance.getP1KCount());
                saldo.setP2KAmount(headTellerBalance.getP2KAmount()+balance.getP2KAmount());
                saldo.setP2KCount(headTellerBalance.getP2KCount()+balance.getP2KCount());
                saldo.setP5KAmount(headTellerBalance.getP5KAmount()+balance.getP5KAmount());
                saldo.setP5KCount(headTellerBalance.getP5KCount()+balance.getP5KCount());
                saldo.setP10KAmount(headTellerBalance.getP10KAmount()+balance.getP10KAmount());
                saldo.setP10KCount(headTellerBalance.getP10KCount()+balance.getP10KCount());
                saldo.setP20KAmount(headTellerBalance.getP20KAmount()+balance.getP20KAmount());
                saldo.setP20KCount(headTellerBalance.getP20KCount()+balance.getP20KCount());
                saldo.setP50KAmount(headTellerBalance.getP50KAmount()+balance.getP50KAmount());
                saldo.setP50KCount(headTellerBalance.getP50KCount()+balance.getP50KCount());
                saldo.setP75KAmount(headTellerBalance.getP75KAmount()+balance.getP75KAmount());
                saldo.setP75KCount(headTellerBalance.getP75KCount()+balance.getP75KCount());
                saldo.setP100KAmount(headTellerBalance.getP100KAmount()+balance.getP100KAmount());
                saldo.setP100KCount(headTellerBalance.getP100KCount()+balance.getP100KCount());
                trxHTAmountDetailRepository.save(saldo);
            }
            headTellerBalance.setC50Amount(getAmountHT(headTellerBalance.getC50Amount(),balance.getC50Amount(),type));
            headTellerBalance.setC50Count(getCountHT(headTellerBalance.getC50Count(),balance.getC50Count(),type));
            headTellerBalance.setC100Amount(getAmountHT(headTellerBalance.getC100Amount(),balance.getC100Amount(),type));
            headTellerBalance.setC100Count(getCountHT(headTellerBalance.getC100Count(),balance.getC100Count(),type));
            headTellerBalance.setC200Amount(getAmountHT(headTellerBalance.getC200Amount(),balance.getC200Amount(),type));
            headTellerBalance.setC200Count(getCountHT(headTellerBalance.getC200Count(),balance.getC200Count(),type));
            headTellerBalance.setC500Amount(getAmountHT(headTellerBalance.getC500Amount(),balance.getC500Amount(),type));
            headTellerBalance.setC500Count(getCountHT(headTellerBalance.getC500Count(),balance.getC500Count(),type));
            headTellerBalance.setC1KAmount(getAmountHT(headTellerBalance.getC1KAmount(),balance.getC1KAmount(),type));
            headTellerBalance.setC1KCount(getCountHT(headTellerBalance.getC1KCount(),balance.getC1KCount(),type));
            headTellerBalance.setP1KAmount(getAmountHT(headTellerBalance.getP1KAmount(),balance.getP1KAmount(),type));
            headTellerBalance.setP1KCount(getCountHT(headTellerBalance.getP1KCount(),balance.getP1KCount(),type));
            headTellerBalance.setP2KAmount(getAmountHT(headTellerBalance.getP2KAmount(),balance.getP2KAmount(),type));
            headTellerBalance.setP2KCount(getCountHT(headTellerBalance.getP2KCount(),balance.getP2KCount(),type));
            headTellerBalance.setP5KAmount(getAmountHT(headTellerBalance.getP5KAmount(),balance.getP5KAmount(),type));
            headTellerBalance.setP5KCount(getCountHT(headTellerBalance.getP5KCount(),balance.getP5KCount(),type));
            headTellerBalance.setP10KAmount(getAmountHT(headTellerBalance.getP10KAmount(),balance.getP10KAmount(),type));
            headTellerBalance.setP10KCount(getCountHT(headTellerBalance.getP10KCount(),balance.getP10KCount(),type));
            headTellerBalance.setP20KAmount(getAmountHT(headTellerBalance.getP20KAmount(),balance.getP20KAmount(),type));
            headTellerBalance.setP20KCount(getCountHT(headTellerBalance.getP20KCount(),balance.getP20KCount(),type));
            headTellerBalance.setP50KAmount(getAmountHT(headTellerBalance.getP50KAmount(),balance.getP50KAmount(),type));
            headTellerBalance.setP50KCount(getCountHT(headTellerBalance.getP50KCount(),balance.getP50KCount(),type));
            headTellerBalance.setP75KAmount(getAmountHT(headTellerBalance.getP75KAmount(),balance.getP75KAmount(),type));
            headTellerBalance.setP75KCount(getCountHT(headTellerBalance.getP75KCount(),balance.getP75KCount(),type));
            headTellerBalance.setP100KAmount(getAmountHT(headTellerBalance.getP100KAmount(),balance.getP100KAmount(),type));
            headTellerBalance.setP100KCount(getCountHT(headTellerBalance.getP100KCount(),balance.getP100KCount(),type));
            headTellerBalance.setTotalAmount(getAmountHT(headTellerBalance.getTotalAmount(),totalAmount,type));
        }
        headTellerBalanceRepository.save(headTellerBalance);
    }

    public CommonResponse<HashMap<String,List<ListBranchModel>>> getListBranch(){
        CommonResponse<HashMap<String,List<ListBranchModel>>> response = new CommonResponse<>();
        List<ListBranchModel> listBranchModel =cabangRepository.getListBranch();
        HashMap<String,List<ListBranchModel>> map = new HashMap<String,List<ListBranchModel>>();
        map.put("branch",listBranchModel);
        response.setData(map);
        response.setType(GET_LIST_BRANCH);
        return response;
    }

    public CommonResponse<PendingMHTModel> getPendingMHT(Profile profile, String branchId, String period){
        CommonResponse<PendingMHTModel> response = new CommonResponse<>();
        response.setType(GET_PENDING_MHT);
        PendingMHTModel mhtModel = new PendingMHTModel();
        LocalDate today = LocalDate.now();
        TrxKasBesar trxKasBesar;
        if (period != null && period.equals(MHT_PREVIOUS)){
            trxKasBesar = trxKasBesarRepository.findTopByPeriodGreaterThanEqualAndPeriodLessThanEqualAndBranchIdAndTrxTypeAndStatusOrderByCreateDateTimeDesc(today.minusDays(14),today,branchId,TrxType.MHT.getCode(),TrxStatus.PENDING.getValue());
        }else {
            trxKasBesar = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeInAndStatusOrderByCreateDateTimeDesc(today,branchId,Set.of(TrxType.MHT.getCode(), TrxType.CC.getCode(), TrxType.SAW.getCode(), TrxType.SAK.getCode(), TrxType.CASHOPNAME.getCode(), TrxType.RVKHT.getCode(), TrxType.RVMHT.getCode(), TrxType.RVSAW.getCode(), TrxType.RVSAK.getCode(), TrxType.RVCC.getCode()),TrxStatus.PENDING.getValue());
        }
        if (trxKasBesar != null){
            mhtModel.setExists(true);
            mhtModel.setLastTransactionId(trxKasBesar.getTransactionId());
        }else {
            mhtModel.setExists(false);
        }
        mhtModel.setPeriod(today);
        mhtModel.setBranchId(branchId);
        response.setData(mhtModel);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }

    public CommonResponse<InputTransactionResponse> inputMHT(Profile profile, InputMasukHeadTellerRequest request){
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(CommonConstant.SUBMIT_HEADTELLER_IN);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();

        if ((existInInterval(profile.getPreferred_username(), 180, request.getBranchId(), TrxType.MHT.getCode()))) {
            logger.info("Response >>> inputMHT : {}", HttpStatus.TOO_MANY_REQUESTS.getReasonPhrase());
            inputTransactionResponse.setStatusDesc(HttpStatus.TOO_MANY_REQUESTS.getReasonPhrase());
            inputTransactionResponse.setStatus(String.valueOf(HttpStatus.TOO_MANY_REQUESTS.value()));
            response.setData(inputTransactionResponse);
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
            return response;
        }
        if (!validationDenomAndVerification(request.getAmountDetails(), request.getNikVerification())){
            inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
            inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
            inputTransactionResponse.setRequestId(request.getRequestId());
            response.setData(inputTransactionResponse);
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
            logger.error("Error Submit MHT, Request not complete ");
            return response;
        }
        LocalDate date = LocalDate.now();
        String transactionId = getTransactionId(TrxType.MHT.getCode(),date,request.getBranchId(),KAS_BESAR);
        inputTransactionResponse.setRequestId(request.getRequestId());
        inputTransactionResponse.setTransactionId(transactionId);
        try {
            TrxKasBesar trxKasBesar = new TrxKasBesar();
            PICModel pic = enrichPICName(profile.getPreferred_username(), request.getNikVerification(), officerImpl, officerNonProsperaRepository);
            trxKasBesar.setKasBesar(transactionId,LocalDate.parse(request.getPeriod()),request.getBranchId(),TrxStatus.PENDING.getValue(),TrxType.MHT.getCode(), profile.getPreferred_username(),request.getNikVerification(),request.getTotalAmount(), request.getTotalAmount(),LocalDateTime.now(), LocalDateTime.now(),request.getRequestId(), pic.getInputerName(), pic.getTellerName());
            trxKasBesarRepository.save(trxKasBesar);
            saveHeadTellerTrxDetail(request.getAmountDetails(), request.getBranchId(), transactionId);
            TrxHeadTeller headTeller = trxHeadTellerRepository.findByTransactionId(request.getTransactionId());
            headTeller.setRefId(trxKasBesar.getTransactionId());
            inputTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
            insertAuditTrail(profile.getPreferred_username(),Action.INPUT_MHT.getValue(),transactionId, request.getBranchId());
        } catch (Exception e) {
            logger.error("Fail to Process MHT ", e);
            inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
        }
        response.setData(inputTransactionResponse);
        return response;
    }

    public Double getAmount(Double firstValue, Double secondValue, String type) {
        if (type.equals(TrxType.KHT.getCode()) || type.equals(TrxType.HT2T.getCode())){
            return firstValue - secondValue;
        }else if(type.equals(TrxType.MHT.getCode())) {
            return firstValue + secondValue;
        }
        return 0.0;
    }

    public Integer getCount(Integer firstValue, Integer secondValue, String type) {
        if (type.equals(TrxType.KHT.getCode())){
            return firstValue - secondValue;
        }else if(type.equals(TrxType.MHT.getCode())) {
            return firstValue + secondValue;
        }
        return 0;
    }

    public String getTrxType(String firstValue, String secondValue, String type) {
        if (type.equals(TrxType.KHT.getCode())) {
            return firstValue;
        } else if (type.equals(TrxType.MHT.getCode())) {
            return secondValue;
        }
        return null;
    }

    public void statusMHTApprove(String transactionId,String type,String status){
        if (type.equals(TrxType.MHT.getCode())){
            TrxHeadTeller trxHeadTeller = trxHeadTellerRepository.findByRefId(transactionId);
            if (status.equals(TrxStatus.REJECTED.getValue())){
                trxHeadTeller.setRefId(null);
            }else if (status.equals(TrxStatus.APPROVED.getValue())){
                trxHeadTeller.setStatus(TrxStatus.SUCCESS.getValue());
            }
            trxHeadTellerRepository.save(trxHeadTeller);
        }
    }
    public Double getAmountHT(Double firstValue, Double secondValue, String type) {
        if (type.equals(TrxType.KHT.getCode())){
            return firstValue + secondValue;
        }else if(type.equals(TrxType.MHT.getCode())) {
            return firstValue - secondValue;
        }
        return 0.0;
    }

    public Integer getCountHT(Integer firstValue, Integer secondValue, String type) {
        if (type.equals(TrxType.KHT.getCode())){
            return firstValue + secondValue;
        }else if(type.equals(TrxType.MHT.getCode())) {
            return firstValue - secondValue;
        }
        return 0;
    }

    public void checkExistingSaldoAkhir(LocalDate date, String branchId) {
        try {
            TrxKasBesar checkSaldoAkhir = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeAndStatusOrderByCreateDateTimeDesc(date, branchId, TrxType.SAK.getCode(), TrxStatus.SUCCESS.getValue());
            if (checkSaldoAkhir != null) {
                TrxKasBesar checkSaldoAwal = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeAndStatusInAndCreateDateTimeLessThanEqual(date, branchId, TrxType.SAW.getCode(), Set.of(TrxStatus.SUCCESS.getValue(),TrxStatus.APPROVED.getValue()),checkSaldoAkhir.getCreateDateTime());
                if (checkSaldoAwal != null) {
                    String newTransactionId = getTransactionId(TrxType.SA.getCode(), date, branchId, KAS_BESAR);
                    trxKasBesarRepository.updateTransactionId(newTransactionId, checkSaldoAkhir.getTransactionId(), TrxType.SA.getCode());
                    trxAmountDetailRepository.updateTransactionId(newTransactionId, checkSaldoAkhir.getTransactionId());
                }
            }
        } catch (Exception e) {
            logger.error("Error check Existing Saldo Akhir" + e);
        }
    }

    public String getSaldoType(String firstValue, String secondValue, String type){
        if (type.equals(TrxType.KHT.getCode())){
            return firstValue;
        }else if (type.equals(TrxType.MHT.getCode())){
            return secondValue;
        }
        return null;
    }

    public CommonResponse<HeadTellerBalanceValidation> headTellerBalanceValidation(Profile nik, String branchId){
        CommonResponse<HeadTellerBalanceValidation> response = new CommonResponse<>();
        response.setType(GET_HEAD_TELLER_BALANCE_VALIDATION);
        HeadTellerBalanceValidation headTellerBalanceValidation = new HeadTellerBalanceValidation();
        try {
            HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
            Integer kb = trxKasBesarRepository.checkTransactionToday(LocalDate.now(),branchId,Set.of(TrxType.MHT.getCode(),TrxType.KHT.getCode()),Set.of(TrxType.HT2T.getCode(), TrxType.T2HT.getCode(), TrxType.HT2V.getCode()));
            if (headTellerBalance !=null && headTellerBalance.getTotalAmount()!=0 && kb==0){
                headTellerBalanceValidation.setStatusValidation(ResponseStatus.VALIDATE_BALANCE_HT_NOT_ZERO.getCode());
                headTellerBalanceValidation.setReason(ResponseStatus.VALIDATE_BALANCE_HT_NOT_ZERO.getValue());
            }else {
                headTellerBalanceValidation.setStatusValidation(ResponseStatus.VALIDATE_BALANCE_HT_ALLOW.getCode());
                headTellerBalanceValidation.setReason(ResponseStatus.VALIDATE_BALANCE_HT_ALLOW.getValue());
            }
            response.setData(headTellerBalanceValidation);
        }catch (Exception e){
            logger.error("error");
        }
        return response;
    }

    public void approveKHTTellerExchange(HeadTellerApprovalReq request, TrxKasBesar kasBesar) {
        if (kasBesar.getTrxType().equals(TrxType.KHT.getCode())) {
            TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findByRefId(kasBesar.getTransactionId());
            if (vault != null) {
                if (request.getStatus().equals(TrxStatus.APPROVED.getValue())) {
                    vault.setStatus(TrxStatus.SUCCESS.getValue());
                } else if (request.getStatus().equals(TrxStatus.REJECTED.getValue())) {
                    vault.setStatus(TrxStatus.CANCEL.getValue());
                }
            }
        }
    }
    private static String encodeIfnotNull(String data) {
        if (data != null) {
            return StringEscapeUtils.escapeHtml4(data);
        }
        return null;
    }
    public boolean existInInterval(String nik, int interval, String branch, String type) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusSeconds(interval);
        return trxKasBesarRepository.checkRequestInterval(startDate, endDate, branch, nik, type) > 0;
    }
    public void recalculateHeadTeller(String branchId, String type, String nik){
        Gson gson = new Gson();
        LocalDate today = LocalDate.now();
        HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
        Double currentBalance = headTellerBalance.getTotalAmount();
        String saldoType = getSaldoHeadTellerType(type);
        List<String> deleteType = new ArrayList<>();
        deleteType.add(type);
        deleteType.add(saldoType);
        List<TrxPendingHeadTeller> trxPendingHeadTeller = trxPendingHeadTellerRepository.findAllByPeriodAndBranchIdAndType(today,branchId,type);
        trxPendingHeadTellerRepository.deleteTrxPendingHeadTellersByPeriodAndBranchIdAndTypeIn(today, branchId,deleteType);
        int seq = 1;
        TrxHTAmountDetail lastSaldo = new TrxHTAmountDetail();
        for (TrxPendingHeadTeller trxPendingHeadTellers : trxPendingHeadTeller) {

            TrxPendingHeadTeller trxPendingSaldo = new TrxPendingHeadTeller();
            trxPendingSaldo.setTransactionId(getTransactionId(saldoType,LocalDate.now(),branchId,PENDING_HEAD_TELLER));
            trxPendingSaldo.setRequestId(trxPendingHeadTellers.getRequestId());
            trxPendingSaldo.setBranchId(branchId);
            trxPendingSaldo.setInputer(nik);
            trxPendingSaldo.setType(saldoType);
            trxPendingSaldo.setPeriod(trxPendingHeadTellers.getPeriod());
            trxPendingSaldo.setStatus(TrxStatus.SUCCESS.getValue());
            trxPendingSaldo.setInputer(trxPendingHeadTellers.getInputer());
            TrxHTAmountDetail trxAmountDetail = gson.fromJson(trxPendingHeadTellers.getAmountDetail(), TrxHTAmountDetail.class);
            trxAmountDetail.setTransactionId(trxPendingSaldo.getTransactionId());
            trxPendingSaldo.setTotalAmount(getAmount(currentBalance, trxPendingHeadTellers.getTotalAmount(),type));
            trxPendingSaldo.setBalance(getAmount(currentBalance , trxPendingHeadTellers.getTotalAmount(),type));
            if (seq==1){
                trxAmountDetail.setC50Amount(getAmount(headTellerBalance.getC50Amount() , trxAmountDetail.getC50Amount(),type));
                trxAmountDetail.setC50Count(getCount(headTellerBalance.getC50Count(), trxAmountDetail.getC50Count(),type));
                trxAmountDetail.setC100Amount(getAmount(headTellerBalance.getC100Amount(), trxAmountDetail.getC100Amount(),type));
                trxAmountDetail.setC100Count(getCount(headTellerBalance.getC100Count(), trxAmountDetail.getC100Count(),type));
                trxAmountDetail.setC200Amount(getAmount(headTellerBalance.getC200Amount(), trxAmountDetail.getC200Amount(),type));
                trxAmountDetail.setC200Count(getCount(headTellerBalance.getC200Count(), trxAmountDetail.getC200Count(),type));
                trxAmountDetail.setC500Amount(getAmount(headTellerBalance.getC500Amount(), trxAmountDetail.getC500Amount(),type));
                trxAmountDetail.setC500Count(getCount(headTellerBalance.getC500Count(), trxAmountDetail.getC500Count(),type));
                trxAmountDetail.setC1KAmount(getAmount(headTellerBalance.getC1KAmount(), trxAmountDetail.getC1KAmount(),type));
                trxAmountDetail.setC1KCount(getCount(headTellerBalance.getC1KCount(), trxAmountDetail.getC1KCount(),type));
                trxAmountDetail.setP1KAmount(getAmount(headTellerBalance.getP1KAmount(), trxAmountDetail.getP1KAmount(),type));
                trxAmountDetail.setP1KCount(getCount(headTellerBalance.getP1KCount(), trxAmountDetail.getP1KCount(),type));
                trxAmountDetail.setP2KAmount(getAmount(headTellerBalance.getP2KAmount(), trxAmountDetail.getP2KAmount(),type));
                trxAmountDetail.setP2KCount(getCount(headTellerBalance.getP2KCount(), trxAmountDetail.getP2KCount(),type));
                trxAmountDetail.setP5KAmount(getAmount(headTellerBalance.getP5KAmount(), trxAmountDetail.getP5KAmount(),type));
                trxAmountDetail.setP5KCount(getCount(headTellerBalance.getP5KCount(), trxAmountDetail.getP5KCount(),type));
                trxAmountDetail.setP10KAmount(getAmount(headTellerBalance.getP10KAmount(), trxAmountDetail.getP10KAmount(),type));
                trxAmountDetail.setP10KCount(getCount(headTellerBalance.getP10KCount(), trxAmountDetail.getP10KCount(),type));
                trxAmountDetail.setP20KAmount(getAmount(headTellerBalance.getP20KAmount(), trxAmountDetail.getP20KAmount(),type));
                trxAmountDetail.setP20KCount(getCount(headTellerBalance.getP20KCount(), trxAmountDetail.getP20KCount(),type));
                trxAmountDetail.setP50KAmount(getAmount(headTellerBalance.getP50KAmount(), trxAmountDetail.getP50KAmount(),type));
                trxAmountDetail.setP50KCount(getCount(headTellerBalance.getP50KCount(), trxAmountDetail.getP50KCount(),type));
                trxAmountDetail.setP75KAmount(getAmount(headTellerBalance.getP75KAmount(), trxAmountDetail.getP75KAmount(),type));
                trxAmountDetail.setP75KCount(getCount(headTellerBalance.getP75KCount(), trxAmountDetail.getP75KCount(),type));
                trxAmountDetail.setP100KAmount(getAmount(headTellerBalance.getP100KAmount(), trxAmountDetail.getP100KAmount(),type));
                trxAmountDetail.setP100KCount(getCount(headTellerBalance.getP100KCount(), trxAmountDetail.getP100KCount(),type));
                lastSaldo = trxAmountDetail;
            }else {
                trxAmountDetail.setC50Amount(getAmount(lastSaldo.getC50Amount() , trxAmountDetail.getC50Amount(),type));
                trxAmountDetail.setC50Count(getCount(lastSaldo.getC50Count(), trxAmountDetail.getC50Count(),type));
                trxAmountDetail.setC100Amount(getAmount(lastSaldo.getC100Amount(), trxAmountDetail.getC100Amount(),type));
                trxAmountDetail.setC100Count(getCount(lastSaldo.getC100Count(), trxAmountDetail.getC100Count(),type));
                trxAmountDetail.setC200Amount(getAmount(lastSaldo.getC200Amount(), trxAmountDetail.getC200Amount(),type));
                trxAmountDetail.setC200Count(getCount(lastSaldo.getC200Count(), trxAmountDetail.getC200Count(),type));
                trxAmountDetail.setC500Amount(getAmount(lastSaldo.getC500Amount(), trxAmountDetail.getC500Amount(),type));
                trxAmountDetail.setC500Count(getCount(lastSaldo.getC500Count(), trxAmountDetail.getC500Count(),type));
                trxAmountDetail.setC1KAmount(getAmount(lastSaldo.getC1KAmount(), trxAmountDetail.getC1KAmount(),type));
                trxAmountDetail.setC1KCount(getCount(lastSaldo.getC1KCount(), trxAmountDetail.getC1KCount(),type));
                trxAmountDetail.setP1KAmount(getAmount(lastSaldo.getP1KAmount(), trxAmountDetail.getP1KAmount(),type));
                trxAmountDetail.setP1KCount(getCount(lastSaldo.getP1KCount(), trxAmountDetail.getP1KCount(),type));
                trxAmountDetail.setP2KAmount(getAmount(lastSaldo.getP2KAmount(), trxAmountDetail.getP2KAmount(),type));
                trxAmountDetail.setP2KCount(getCount(lastSaldo.getP2KCount(), trxAmountDetail.getP2KCount(),type));
                trxAmountDetail.setP5KAmount(getAmount(lastSaldo.getP5KAmount(), trxAmountDetail.getP5KAmount(),type));
                trxAmountDetail.setP5KCount(getCount(lastSaldo.getP5KCount(), trxAmountDetail.getP5KCount(),type));
                trxAmountDetail.setP10KAmount(getAmount(lastSaldo.getP10KAmount(), trxAmountDetail.getP10KAmount(),type));
                trxAmountDetail.setP10KCount(getCount(lastSaldo.getP10KCount(), trxAmountDetail.getP10KCount(),type));
                trxAmountDetail.setP20KAmount(getAmount(lastSaldo.getP20KAmount(), trxAmountDetail.getP20KAmount(),type));
                trxAmountDetail.setP20KCount(getCount(lastSaldo.getP20KCount(), trxAmountDetail.getP20KCount(),type));
                trxAmountDetail.setP50KAmount(getAmount(lastSaldo.getP50KAmount(), trxAmountDetail.getP50KAmount(),type));
                trxAmountDetail.setP50KCount(getCount(lastSaldo.getP50KCount(), trxAmountDetail.getP50KCount(),type));
                trxAmountDetail.setP75KAmount(getAmount(lastSaldo.getP75KAmount(), trxAmountDetail.getP75KAmount(),type));
                trxAmountDetail.setP75KCount(getCount(lastSaldo.getP75KCount(), trxAmountDetail.getP75KCount(),type));
                trxAmountDetail.setP100KAmount(getAmount(lastSaldo.getP100KAmount(), trxAmountDetail.getP100KAmount(),type));
                trxAmountDetail.setP100KCount(getCount(lastSaldo.getP100KCount(), trxAmountDetail.getP100KCount(),type));
                lastSaldo = trxAmountDetail;
            }
            trxPendingHeadTellers.setBalance(trxPendingSaldo.getBalance());
            trxPendingSaldo.setAmountDetail(gson.toJson(trxAmountDetail));
            trxPendingSaldo.setCreateDateTime(trxPendingHeadTellers.getCreateDateTime().plusSeconds(1));
            trxPendingSaldo.setUpdateDateTime(trxPendingHeadTellers.getUpdateDateTime().plusSeconds(1));
            currentBalance = trxPendingSaldo.getBalance();
            trxPendingHeadTellerRepository.save(trxPendingHeadTellers);
            trxPendingHeadTellerRepository.save(trxPendingSaldo);
            seq++;
        }
    }
    public String getSaldoHeadTellerType(String type){
        if (type.equals(TrxType.T2HT.getCode())){
            return TrxType.SAT2HT.getCode();
        }else {
            return TrxType.SAHT2T.getCode();
        }
    }
    public CommonResponse<BalanceStatusResponse> overlimitBranchBalance(Profile nik, String branchId){
        CommonResponse<BalanceStatusResponse> response = new CommonResponse<>();
        response.setType(GET_BRANCH_BALANCE_STATUS);
        BalanceStatusResponse overlimit = new BalanceStatusResponse();
        BalanceStatusDetailModel kasBesar = new BalanceStatusDetailModel();
        BalanceStatusDetailModel headteller = new BalanceStatusDetailModel();
        try{
            Cabang cabang = cabangRepository.findByCabangId(branchId);
            BranchBalance balance = branchBalanceRepository.findByBranchId(branchId);
            HeadTellerBalance htBalance = headTellerBalanceRepository.findByBranchId(branchId);
            if (cabang!=null || balance != null || htBalance != null){
                overlimit.setBranchId(branchId);
                overlimit.setBranchName(StringUtils.isEmpty(cabang.getCabangDesc()) ? "" : cabang.getCabangDesc());
                kasBesar.setBalance(balance.getTotal());
                headteller.setBalance(htBalance.getTotalAmount());

                if (balance.getTotal() > cabang.getTotalBalance()){
                    kasBesar.setStatus(OVERLIMIT_STATUS_BALANCE_OVERLIMIT);
                    kasBesar.setStatusDesc(OVERLIMIT_STATUS_BALANCE_OVERLIMIT_DESC);
                }else {
                    kasBesar.setStatus(OVERLIMIT_STATUS_BALANCE_UNDERLIMIT);
                    kasBesar.setStatusDesc(OVERLIMIT_STATUS_BALANCE_UNDERLIMIT_DESC);
                }

                LocalDateTime todayAt17 = LocalDate.now().atTime(17, 0);
                LocalDateTime now = LocalDateTime.now();

                headteller.setStatus(STATUS_BALANCE_OK);
                headteller.setStatusDesc(STATUS_BALANCE_OK);

                if (now.isAfter(todayAt17) && htBalance.getTotalAmount() != 0){
                    headteller.setStatus(STATUS_BALANCE_OD);
                    headteller.setStatusDesc(STATUS_BALANCE_OD_DESC);
                }else if (htBalance.getTotalAmount() > cabang.getTotalBalanceHT()){
                    headteller.setStatus(OVERLIMIT_STATUS_BALANCE_OVERLIMIT);
                    headteller.setStatusDesc(OVERLIMIT_STATUS_BALANCE_OVERLIMIT_DESC);
                }
            }
            overlimit.setKasBesar(kasBesar);
            overlimit.setHeadTeller(headteller);
            response.setData(overlimit);
        }catch (Exception e){
            logger.error("Failed to get balance status ", e);
        }

        return response;
    }

    public boolean checkBalanceOverlimit(String branchId, String transactionId){
        if (!branchId.equals("HO")) {
            Cabang cabang = cabangRepository.findByCabangId(branchId);
            BranchBalance balance = branchBalanceRepository.findByBranchId(branchId);
            if (cabang != null) {
                if (balance.getTotal() > cabang.getTotalBalance()) {
                    TrxOverlimitBranchBalance overlimit = new TrxOverlimitBranchBalance();
                    overlimit.setBranchId(branchId);
                    overlimit.setTransactionId(transactionId);
                    overlimit.setTotalBalance(balance.getTotal());
                    overlimit.setPeriod(LocalDate.now());
                    overlimit.setCreateDateTime(LocalDateTime.now());
                    trxOverlimitBranchBalanceRepository.save(overlimit);
                    return true;
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    public TrxKasBesar checkBeginBalance(LocalDate date, String branchId){
        TrxKasBesar checkBeginBalance = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeAndStatusInOrderByCreateDateTimeDesc(date,branchId,TrxType.SAW.getCode(),Set.of(TrxStatus.APPROVED.getValue(), TrxStatus.SUCCESS.getValue()));
        return checkBeginBalance;
    }

    public boolean checkPendingTransactionKasBesar(String branchId){
        LocalDate period = LocalDate.now();
        Set<String> transactionType = Set.of(TrxType.CC.getCode(), TrxType.KHT.getCode(), TrxType.MHT.getCode(), TrxType.SAW.getCode(), TrxType.SAK.getCode(), TrxType.CASHOPNAME.getCode());
        TrxKasBesar kasBesar = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeInAndStatusOrderByCreateDateTimeDesc(period, branchId, transactionType, TrxStatus.PENDING.getValue());
        if (kasBesar != null){
            return false;
        }else {
            return true;
        }
    }

    public CommonResponse<BranchOfficersModel> getBranchOfficers(String roleId, String branchId) {
        CommonResponse<BranchOfficersModel> response = new CommonResponse<>();
        response.setType(GET_BRANCH_OFFICERS);
        BranchOfficersModel data = new BranchOfficersModel();
        List<ListOfficersModel> listOfficers = new ArrayList<>();
        List<String> officersList = Arrays.asList(RoleConstant.NOM.getRoleId(), RoleConstant.QA.getRoleId(), RoleConstant.QAM.getRoleId(),RoleConstant.QAD.getRoleId(), TELLER_ROLE.toString(), BOM_ROLE.toString(), BO_ROLE.toString(), ODH_ROLE, SKAI_ROLE);
        List<String> listEmployeeHO;
        List<String> listBranchOfficers;
        List<OfficersDetailModel> allBranchOfficersAndEmployee = new ArrayList<>();
        try {
            Cabang cabang = cabangRepository.findByCabangId(branchId);
            if (roleId.contains(QA_ROLE)){
                roleId = roleId.concat(",").concat(QAD_ROLE).concat(",").concat(QAM_ROLE);
            }
            String[] array = roleId.split(",", -1);
            listEmployeeHO = Arrays.stream(array).filter(roles -> roles.equals(QA_ROLE)
                            || roles.equals(NOM_ROLE)
                            || roles.equals(ODH_ROLE)
                            || roles.equals(QAD_ROLE)
                            || roles.equals(QAM_ROLE)
                            || roles.equals(SKAI_ROLE)
                    )
                    .map(e -> {
                        e = RoleConstant.getValueByRoleId(e);
                        return e;
                    }).collect(Collectors.toList());

            listBranchOfficers = Arrays.stream(array).filter(roles -> roles.equals(BOM_ROLE.toString())
                    || roles.equals(TELLER_ROLE.toString()) || roles.equals(BO_ROLE.toString() )).collect(Collectors.toList());

            if (!listEmployeeHO.isEmpty()) {
                allBranchOfficersAndEmployee.addAll(employeeRepository.getListRoleHO(listEmployeeHO));
            }
            if (!listBranchOfficers.isEmpty()) {
                allBranchOfficersAndEmployee.addAll(officerImpl.getRoleOfficers(branchId, listBranchOfficers));
                allBranchOfficersAndEmployee.addAll(officerNonProsperaRepository.getAllRoleOfficers(branchId, listBranchOfficers));
            }
            for (String roles : officersList) {
                ListOfficersModel officersData = new ListOfficersModel();
                if(roles.equals(RoleConstant.QAD.getRoleId()) ||roles.equals(RoleConstant.QAM.getRoleId())){
                    officersData.setRoleId(QA_ROLE);
                }else {
                    officersData.setRoleId(RoleConstant.getRoleIdByValue(roles) != null ? RoleConstant.getRoleIdByValue(roles) : roles);
                }
                List<ListOfficerModel> listMembers = new ArrayList<>();
                allBranchOfficersAndEmployee.forEach(e -> {
                    if (e.getRole().equals(QAD_ROLE) || e.getRole().equals(QAM_ROLE)){
                        e.setRole(QA_ROLE);
                    }
                    if (e.getRole().equals(roles)) {
                        ListOfficerModel member = new ListOfficerModel(e.getNik(), e.getName());
                        listMembers.add(member);
                        officersData.setMembers(listMembers);
                    }
                });
                if (officersData.getMembers() != null && officersData.getMembers().size() > 0) {
                    listOfficers.add(officersData);
                }
            }
            if (Arrays.asList(array).contains(BM_ROLE)) {
                ListOfficersModel officerBM = new ListOfficersModel();
                officerBM.setRoleId(RoleConstant.BM.getRoleId());
                officerBM.setMembers(employeeRepository.findAllByRoleIDBM(RoleConstant.BM.getValue(), RoleConstant.AREAFUND.getValue(), branchId));
                listOfficers.add(officerBM);
            }
            if (Arrays.asList(array).contains(BOS_ROLE)) {
                ListOfficersModel officerBOS = new ListOfficersModel();
                officerBOS.setRoleId(RoleConstant.BOS.getRoleId());
                List<ListOfficerModel> officer = new ArrayList<>();
                officer.addAll(employeeRepository.findAllByRoleIDBOS(RoleConstant.BOS.getValue(), branchId));
                officer.addAll(officerImpl.getByBranchIdAndRoleIDIn(branchId, Set.of(TELLER_ROLE, BO_ROLE)));
                officer.addAll(officerNonProsperaRepository.findAllByBranchIdAndRoleIDIn(branchId, List.of(TELLER_ROLE.toString(), BOM_ROLE.toString(), BO_ROLE.toString())));
                officerBOS.setMembers(officer);
                listOfficers.add(officerBOS);
            }
            if (Arrays.asList(array).contains(ALT_TELLER_ROLE)) {
                ListOfficersModel officerAltTeller = new ListOfficersModel();
                officerAltTeller.setRoleId(ALT_TELLER_ROLE);
                List<ListOfficerModel> officer = officerImpl.getByBranchIdAndRoleIDIn(branchId, Set.of(TELLER_ROLE, BO_ROLE));
                officer.addAll(officerNonProsperaRepository.findAllByBranchIdAndRoleIDIn(branchId, List.of(TELLER_ROLE.toString(), BO_ROLE.toString())));
                officerAltTeller.setMembers(officer);
                listOfficers.add(officerAltTeller);
            }
            Comparator<ListOfficersModel> comparator = Comparator.comparingInt(s -> Arrays.asList(array).indexOf(s.getRoleId()));
            listOfficers.sort(comparator);
            data.setOfficers(listOfficers);
            data.setBranchName(cabang.getCabangDesc());
            data.setBranchId(branchId);
            data.setRoleId(roleId);
            response.setData(data);

        } catch (Exception e) {
            logger.error("Failed to get Branch Officers ", e);
            response.setStatusDesc(ResponseStatus.FAILED.getValue());
            response.setStatus(ResponseStatus.FAILED.getCode());
        }
        return response;
    }
    public CommonResponse<ProgressStatusResponse> getIndicatorProgress(String branchId){
        CommonResponse<ProgressStatusResponse> response = new CommonResponse<>();
        ProgressStatusResponse progressStatus = new ProgressStatusResponse();
        Map<String, StatusProgressModel> statusKasBesar = progressStatus.defaultStatusProgressKasBesar();
        Map<String, StatusProgressModel> statusHeadTeller = progressStatus.defaultStatusProgressKasHeadTeller();
        LocalDate period = LocalDate.now();
        Cabang cabang = cabangRepository.findByCabangId(branchId);
        HeadTellerBalance htBalance = headTellerBalanceRepository.findByBranchId(branchId);
        if (cabang != null){
            progressStatus.setBranchName(cabang.getCabangDesc());
        }
        List<TrxKasBesar> kasBesar = trxKasBesarRepository.findAllByPeriodAndBranchIdAndTrxTypeInAndStatusIn(period, branchId, Set.of(TrxType.KHT.getCode(), TrxType.MHT.getCode(), TrxType.SAK.getCode()), Set.of(TrxStatus.APPROVED.getValue(), TrxStatus.SUCCESS.getValue()));
        kasBesar.forEach(kb -> {
            if (kb != null) {
                if (kb.getTrxType().equals(TrxType.KHT.getCode())) {
                    statusKasBesar.get(INDICATOR_PROGRESS_KHT).setStatus(INDICATOR_PROGRESS_DONE);
                } else if (kb.getTrxType().equals(TrxType.MHT.getCode())) {
                    statusKasBesar.get(INDICATOR_PROGRESS_MHT).setStatus(INDICATOR_PROGRESS_DONE);
                } else if (kb.getTrxType().equals(TrxType.SAK.getCode()) && htBalance.getTotalAmount() == 0){
                    statusKasBesar.get(INDICATOR_PROGRESS_END).setStatus(INDICATOR_PROGRESS_DONE);
                    statusHeadTeller.get(INDICATOR_PROGRESS_END).setStatus(INDICATOR_PROGRESS_DONE);
                }
            }
        });

        List<TrxHeadTeller> ht = trxHeadTellerRepository.findAllByPeriodAndBranchIdAndTypeInAndStatusIn(period, branchId, Set.of(TrxType.V2HT.getCode(), TrxType.T2HT.getCode(), TrxType.HT2T.getCode(), TrxType.HT2V.getCode()), Set.of(TrxStatus.SUCCESS.getValue(), TrxStatus.PENDING.getValue()));
        ht.forEach(headTeller -> {
            if (headTeller != null) {
                if (headTeller.getType().equals(TrxType.V2HT.getCode())) {
                    statusHeadTeller.get(INDICATOR_PROGRESS_V2HT).setStatus(INDICATOR_PROGRESS_DONE);
                } else if (headTeller.getType().equals(TrxType.HT2T.getCode())) {
                    statusHeadTeller.get(INDICATOR_PROGRESS_HT2T).setStatus(INDICATOR_PROGRESS_DONE);
                } else if (headTeller.getType().equals(TrxType.T2HT.getCode())) {
                    statusHeadTeller.get(INDICATOR_PROGRESS_T2HT).setStatus(INDICATOR_PROGRESS_DONE);
                } else if (headTeller.getType().equals(TrxType.HT2V.getCode())) {
                    statusHeadTeller.get(INDICATOR_PROGRESS_HT2V).setStatus(INDICATOR_PROGRESS_DONE);
                    if (headTeller.getBalance() == 0) {
                        statusHeadTeller.get(INDICATOR_PROGRESS_END).setStatus(INDICATOR_PROGRESS_DONE);
                    }
                }
            }
        });


        Comparator<StatusProgressModel> comparatorKasBesar = Comparator.<StatusProgressModel, Boolean>comparing(s -> s.getId().equals(INDICATOR_PROGRESS_START)).
                thenComparing(s -> s.getId().equals(INDICATOR_PROGRESS_KHT)).
                thenComparing(s -> s.getId().equals(INDICATOR_PROGRESS_MHT)).
                thenComparing(s -> s.getId().equals(INDICATOR_PROGRESS_END)).reversed();
        List<StatusProgressModel> kasBesarStatus = new ArrayList(statusKasBesar.values());
        kasBesarStatus.sort(comparatorKasBesar);

        Comparator<StatusProgressModel> comparatorKasHeadTeller = Comparator.<StatusProgressModel, Boolean>comparing(s -> s.getId().equals(INDICATOR_PROGRESS_START)).
                thenComparing(s -> s.getId().equals(INDICATOR_PROGRESS_V2HT)).
                thenComparing(s -> s.getId().equals(INDICATOR_PROGRESS_HT2T)).
                thenComparing(s -> s.getId().equals(INDICATOR_PROGRESS_T2HT)).
                thenComparing(s -> s.getId().equals(INDICATOR_PROGRESS_HT2V)).
                thenComparing(s -> s.getId().equals(INDICATOR_PROGRESS_END)).reversed();
        List<StatusProgressModel> kasHTStatus = new ArrayList(statusHeadTeller.values());
        kasHTStatus.sort(comparatorKasHeadTeller);

        progressStatus.setKasBesar(kasBesarStatus);
        progressStatus.setKasHeadTeller(kasHTStatus);
        progressStatus.setPeriod(period.toString());
        progressStatus.setBranchId(branchId);
        response.setData(progressStatus);
        response.setType(GET_INDICATOR_PROGRESS);

        return response;
    }
}
