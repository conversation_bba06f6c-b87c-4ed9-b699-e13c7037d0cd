package com.btpns.fin.service;

import com.btpns.fin.configuration.AmountConfig;
import com.btpns.fin.constant.*;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.CashOpnameSubmitRequest;
import com.btpns.fin.model.request.HeadTellerApprovalReq;
import com.btpns.fin.model.response.CashOpnameDetailResponse;
import com.btpns.fin.model.response.CheckMonthlyCOPResponse;
import com.btpns.fin.model.response.InputTransactionResponse;
import com.btpns.fin.repository.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import liquibase.pro.packaged.C;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.btpns.fin.constant.CommonConstant.*;
import static com.btpns.fin.helper.CommonHelper.*;


@Service
public class CashOpnameService {
    private static final Logger logger = LoggerFactory.getLogger(CashOpnameService.class);

    private static final Gson gson = GsonUtil.getInstance().getGson();

    @Autowired
    CashOpnameRepository cashOpnameRepository;

    @Autowired
    TrxKasBesarRepository trxKasBesarRepository;

    @Autowired
    TrxAmountDetailRepository trxAmountDetailRepository;

    @Autowired
    TrxAuditTrailRepository trxAuditTrailRepository;

    @Autowired
    BranchBalanceRepository branchBalanceRepository;

    @Autowired
    CabangRepository cabangRepository;

    @Autowired
    CashOpnameDetailModelRepository cashOpnameDetailModelRepository;
    @Autowired
    HeadTellerBalanceRepository headTellerBalanceRepository;
    @Autowired
    OfficerRepository officerRepository;
    @Autowired
    EmployeeRepository employeeRepository;
    @Autowired
    OfficerNonProsperaRepository officerNonProsperaRepository;
    @Autowired
    OfficerImpl officerImpl;

    @Transactional
    public CommonResponse<InputTransactionResponse> submitCashOpname(String nikInputer, CashOpnameSubmitRequest request) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(SUBMIT_CASH_OPNAME);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        inputTransactionResponse.setRequestId(request.getRequestId());
        LocalDate period = LocalDate.parse(request.getPeriod());
        String transactionId = null;
        CashOpname cashOpname = new CashOpname();
        TrxKasBesar kasBesar = new TrxKasBesar();
        TrxAmountDetail detail = new TrxAmountDetail();
        LocalDateTime time = LocalDateTime.now();
        try {
            if (!validationDenomCashOpname(request)) {
                inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
                inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
                inputTransactionResponse.setRequestId(request.getRequestId());
                response.setStatus(ResponseStatus.FAILED.getCode());
                response.setStatusDesc(ResponseStatus.FAILED.getValue());
                logger.error("Error Submit Cash Opname, Request not complete ");
                return response;
            }
            checkBranchBalance(request.getBranchId(),branchBalanceRepository);
            checkHTBranchBalance(request.getBranchId(),headTellerBalanceRepository);
            if (request.getMode().equals(REQUEST_TYPE_UPDATE) && !StringUtils.isEmpty(request.getTransactionId())) {
                transactionId = request.getTransactionId();
                cashOpname = cashOpnameRepository.findByTransactionId(transactionId);
                kasBesar = trxKasBesarRepository.findByTransactionId(transactionId);
                detail = trxAmountDetailRepository.findByTransactionId(transactionId);
                cashOpname.setUpdateDateTime(time);
            } else if (request.getMode().equals(REQUEST_TYPE_NEW)) {
                transactionId = getTransactionId(LocalDate.parse(request.getPeriod()), request.getBranchId());
                cashOpname.setCreateDateTime(time);
            } else {
                inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
                inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
                inputTransactionResponse.setRequestId(request.getRequestId());
                response.setData(inputTransactionResponse);
                response.setStatus(ResponseStatus.FAILED.getCode());
                response.setStatusDesc(ResponseStatus.FAILED.getValue());
                logger.error("Error Submit Cash Opname, Request not complete ");
            }
            cashOpname.setRequestId(request.getRequestId());
            cashOpname.setPeriod(period);
            cashOpname.setStatus(TrxStatus.PENDING.getValue());
            cashOpname.setTransactionId(transactionId);
            cashOpname.setBranchId(request.getBranchId());
            cashOpname.setTotalBalance(request.getTotalBalance());
            cashOpname.setOldTotalBalance(request.getOldTotalBalance());
            cashOpname.setCarryBalance(request.getCarryBalance());
            cashOpname.setTotalPaperBalance(request.getTotalPaperBalance());
            cashOpname.setTotalCoinBalance(request.getTotalCoinBalance());
            cashOpname.setNikTeller(request.getNikTeller());
            cashOpname.setStatusVerificationTeller(TrxStatus.PENDING.getValue());
            cashOpname.setDateVerificationTeller(null);
            cashOpname.setNikBM(StringUtils.isEmpty(request.getNikBM()) ? null : request.getNikBM());
            cashOpname.setDateVerificationBM(null);
            cashOpname.setStatusVerificationBM(StringUtils.isEmpty(request.getNikBM()) ? null : TrxStatus.PENDING.getValue());
            cashOpname.setNikQA(StringUtils.isEmpty(request.getNikQA()) ? null : request.getNikQA());
            cashOpname.setDateVerificationQA(null);
            cashOpname.setStatusVerificationQA((StringUtils.isEmpty(request.getNikQA()) ? null : TrxStatus.PENDING.getValue()));
            cashOpname.setNikBOS(StringUtils.isEmpty(request.getNikBOS()) ? null : request.getNikBOS());
            cashOpname.setDateVerificationBOS(null);
            cashOpname.setStatusVerificationBOS((StringUtils.isEmpty(request.getNikBOS()) ? null : TrxStatus.PENDING.getValue()));
            cashOpname.setNikODH(StringUtils.isEmpty(request.getNikODH()) ? null : request.getNikODH());
            cashOpname.setStatusVerificationODH((StringUtils.isEmpty(request.getNikODH()) ? null : TrxStatus.PENDING.getValue()));
            cashOpname.setDateVerificationODH(null);
            cashOpname.setNikSKAI(StringUtils.isEmpty(request.getNikSKAI()) ? null : request.getNikSKAI());
            cashOpname.setStatusVerificationSKAI((StringUtils.isEmpty(request.getNikSKAI()) ? null : TrxStatus.PENDING.getValue()));
            cashOpname.setDateVerificationSKAI(null);
            cashOpname.setNikAltTeller(StringUtils.isEmpty(request.getNikAltTeller()) ? null : request.getNikAltTeller());
            cashOpname.setDateVerificationAltTeller(null);
            cashOpname.setStatusVerificationAltTeller((StringUtils.isEmpty(request.getNikAltTeller()) ? null : TrxStatus.PENDING.getValue()));
            cashOpname.setNikBOM(StringUtils.isEmpty(request.getNikBOM()) ? null : request.getNikBOM());
            cashOpname.setStatusVerificationBOM(TrxStatus.VERIFIED.getValue());
            cashOpname.setDateVerificationBOM(StringUtils.isEmpty(request.getNikBOM()) ? null : time);
            cashOpname.setNikNOM(StringUtils.isEmpty(request.getNikNOM()) ? null : request.getNikNOM());
            cashOpname.setStatusVerificationNOM((StringUtils.isEmpty(request.getNikNOM()) ? null : TrxStatus.PENDING.getValue()));
            cashOpname.setDateVerificationNOM(null);
            cashOpname.setNikQA2(StringUtils.isEmpty(request.getNikQA2()) ? null : request.getNikQA2());
            cashOpname.setStatusVerificationQA2((StringUtils.isEmpty(request.getNikQA2()) ? null : TrxStatus.PENDING.getValue()));
            cashOpname.setDateVerificationQA2(null);
            cashOpname.setReason(StringUtils.isEmpty(request.getReason()) ? null :encodeIfnotNull(request.getReason()));
            cashOpname.setAmountDetail(gson.toJson(request.getBalanceDetails()));
            enrichCashOpnameOfficer(cashOpname, officerImpl, employeeRepository, officerNonProsperaRepository);
            cashOpname.setNameBOM(StringUtils.isEmpty(request.getNikBOM()) ? null : cashOpname.getNameBOM());
            cashOpname.setNameTeller(StringUtils.isEmpty(request.getNikTeller()) ? null : cashOpname.getNameTeller());
            cashOpname.setNameBOS(StringUtils.isEmpty(request.getNikBOS()) ? null : cashOpname.getNameBOS());
            cashOpname.setNameBM(StringUtils.isEmpty(request.getNikBM()) ? null : cashOpname.getNameBM());
            cashOpname.setNameQA(StringUtils.isEmpty(request.getNikQA()) ? null : cashOpname.getNameQA());
            cashOpname.setNameQA2(StringUtils.isEmpty(request.getNikQA2()) ? null : cashOpname.getNameQA2());
            cashOpname.setNameAltTeller(StringUtils.isEmpty(request.getNikAltTeller()) ? null : cashOpname.getNameAltTeller());
            cashOpname.setNameNOM(StringUtils.isEmpty(request.getNikNOM()) ? null : cashOpname.getNameNOM());
            cashOpname.setNameODH(StringUtils.isEmpty(request.getNikODH()) ? null : cashOpname.getNameODH());
            cashOpname.setNameSKAI(StringUtils.isEmpty(request.getNikSKAI()) ? null : cashOpname.getNameSKAI());

            cashOpnameRepository.save(cashOpname);

            PICModel pic = enrichPICName(cashOpname.getNikBOM(), cashOpname.getNikTeller(), officerImpl, officerNonProsperaRepository);
            kasBesar.setKasBesar(transactionId, period, request.getBranchId(), TrxStatus.PENDING.getValue(), TrxType.CASHOPNAME.getCode(), request.getNikBOM(), request.getNikTeller(), request.getTotalBalance(), request.getTotalBalance(), LocalDateTime.now(), LocalDateTime.now(), request.getRequestId(), pic.getInputerName(), pic.getTellerName());
            trxKasBesarRepository.save(kasBesar);
            detail.setTransactionId(transactionId);
            detail.setAmountDetail(request.getBalanceDetails(), transactionId);
            trxAmountDetailRepository.save(detail);
            inputTransactionResponse.setTransactionId(transactionId);
            inputTransactionResponse.setStatus(ResponseStatus.SUCCESS.getCode());
            inputTransactionResponse.setStatusDesc(ResponseStatus.SUCCESS.getValue());
            response.setStatus(ResponseStatus.SUCCESS.getCode());
            response.setStatusDesc(ResponseStatus.SUCCESS.getValue());
            response.setData(inputTransactionResponse);
            insertAuditTrail(nikInputer, Action.SUBMIT_CASH_OPNAME.getValue(), transactionId, request.getBranchId());

        } catch (Exception e) {
            inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
            inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
            inputTransactionResponse.setRequestId(request.getRequestId());
            response.setStatusDesc(ResponseStatus.FAILED.getValue());
            response.setStatus(ResponseStatus.FAILED.getCode());
            logger.error("Error Submit Cash Opname " + e);
        }
        return response;
    }

    public String getTransactionId(LocalDate date, String branchId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        Integer lastTransactionId = cashOpnameRepository.countTransaction(date, branchId);
        String transactionNo = String.format("%02d",1);
        if (lastTransactionId!=null){
            lastTransactionId += 1;
            transactionNo =String.format("%02d",lastTransactionId);
        }
        return TrxType.CASHOPNAME.getCode() + formatter.format(date) + branchId + transactionNo;
    }

    public void insertAuditTrail(String nik, String action, String transactionId, String branchId){
        TrxAuditTrail trxAuditTrail = new TrxAuditTrail();
        trxAuditTrail.setNik(nik);
        trxAuditTrail.setAction(action);
        trxAuditTrail.setCreateDateTime(LocalDateTime.now());
        trxAuditTrail.setTransactionId(transactionId);
        trxAuditTrail.setBranchId(branchId);
        trxAuditTrailRepository.save(trxAuditTrail);
    }

    private static String encodeIfnotNull(String data) {
        if (data != null) {
            return StringEscapeUtils.escapeHtml4(data);
        }
        return null;
    }

    public boolean existInInterval(String nik, int interval, String branch) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusSeconds(interval);
        return cashOpnameRepository.checkRequestInterval(startDate, endDate, branch, nik) > 0;
    }
    public CommonResponse<CashOpnameDetailResponse> cashOpnameDetail(Profile nik, String transactionId){
        CommonResponse<CashOpnameDetailResponse> response = new CommonResponse<>();
        CashOpname cashOpname = cashOpnameRepository.findByTransactionId(transactionId);
        if (cashOpname !=null){
            CashOpnameDetail detail = cashOpnameDetailModelRepository.getCashOpnameDetailByTransactionId(transactionId, cashOpname.getBranchId());
            response.setType(GET_CASH_OPNAME);
            response.setStatus(ResponseStatus.SUCCESS.getCode());
            response.setStatusDesc(ResponseStatus.SUCCESS.getValue());
            response.setData(new CashOpnameDetailResponse(enrichOfficerBeritaAcara(detail)));
        }else {
            response.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
            response.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
        }
        return response;
    }
    private HashMap<String, UserOfficerModel> getOfficerMap(List<String> listNIK){
        HashMap<String, UserOfficerModel> map = new HashMap<>();
        if (!listNIK.isEmpty()){
            List<UserOfficerModel> officers = officerImpl.getOfficerName(listNIK);
            officers.forEach(officer -> {
                if (officer.getNik() == null){
                    if (officer.getLoginName() != null) {
                        map.putIfAbsent(officer.getLoginName().toUpperCase(), officer);
                    }
                }else {
                    map.putIfAbsent(officer.getNik().toUpperCase(), officer);
                }
            });
        }

        return map;
    }
    private List<CashOpnameListBADetailModel> enrichOfficerCashOpname(List<CashOpnameListBADetailModel> transactionModel) {
        
        Map<String, String> mapNIK = new HashMap<>();
        transactionModel.forEach(model -> {
            if (model.getPic1NIK() != null && StringUtils.isEmpty(model.getPic1Name())){
                mapNIK.putIfAbsent(model.getPic1NIK() ,model.getPic1NIK());
            }
            if (model.getPic2NIK() != null && StringUtils.isEmpty(model.getPic2Name())){
                mapNIK.putIfAbsent(model.getPic2NIK(), model.getPic2NIK());
            }
        });
        if (!transactionModel.isEmpty()){
            HashMap<String, UserOfficerModel> map = getOfficerMap(new ArrayList(mapNIK.keySet()));
            transactionModel.forEach(model -> {
                if (model.getPic1NIK() != null && StringUtils.isEmpty(model.getPic1Name())){
                    UserOfficerModel officer = map.get(model.getPic1NIK().toUpperCase());
                    if (officer != null) {
                        model.setPic1Name(officer.getOfficerName());
                    }
                }
                if (model.getPic2NIK() != null && StringUtils.isEmpty(model.getPic2Name())){
                    UserOfficerModel officer = map.get(model.getPic2NIK().toUpperCase());
                    if (officer != null) {
                        model.setPic2Name(officer.getOfficerName());
                    }
                }
            });
        }

        return transactionModel;
    }

    private CashOpnameDetail enrichOfficerBeritaAcara(CashOpnameDetail transactionModel) {
        Map<String, String> mapNIK = new HashMap<>();
        if (transactionModel.getNikTeller() != null && StringUtils.isEmpty(transactionModel.getNameTeller())) {
            mapNIK.putIfAbsent(transactionModel.getNikTeller(), transactionModel.getNikTeller());
        }
        if (transactionModel.getNikBOM() != null && StringUtils.isEmpty(transactionModel.getNameBOM())) {
            mapNIK.putIfAbsent(transactionModel.getNikBOM(), transactionModel.getNikBOM());
        }
        if (transactionModel.getNikBOS() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            mapNIK.putIfAbsent(transactionModel.getNikBOS(), transactionModel.getNikBOS());
        }
        if (transactionModel.getNikAltTeller() != null && StringUtils.isEmpty(transactionModel.getNameAltTeller())) {
            mapNIK.putIfAbsent(transactionModel.getNikAltTeller(), transactionModel.getNikAltTeller());
        }
        if (transactionModel.getNikQA2() != null && StringUtils.isEmpty(transactionModel.getNameQA2())) {
            mapNIK.putIfAbsent(transactionModel.getNikQA2(), transactionModel.getNikQA2());
        }
        HashMap<String, UserOfficerModel> map = getOfficerMap(new ArrayList(mapNIK.keySet()));

        if (transactionModel.getNikTeller() != null && StringUtils.isEmpty(transactionModel.getNameTeller())) {
            UserOfficerModel officer = map.get(transactionModel.getNikTeller().toUpperCase());
            if (officer != null) {
                transactionModel.setNameTeller(officer.getOfficerName());
            }
        }
        if (transactionModel.getNikBOM() != null && StringUtils.isEmpty(transactionModel.getNameBOM())) {
            UserOfficerModel officer = map.get(transactionModel.getNikBOM().toUpperCase());
            if (officer != null) {
                transactionModel.setNameBOM(officer.getOfficerName());
            }
        }
        if (transactionModel.getNikBOS() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            UserOfficerModel officer = map.get(transactionModel.getNikBOS().toUpperCase());
            if (officer != null) {
                transactionModel.setNameBOS(officer.getOfficerName());
            }
        }
        if (transactionModel.getNikAltTeller() != null && StringUtils.isEmpty(transactionModel.getNameAltTeller())) {
            UserOfficerModel officer = map.get(transactionModel.getNikAltTeller().toUpperCase());
            if (officer != null) {
                transactionModel.setNameAltTeller(officer.getOfficerName());
            }
        }
        if (transactionModel.getNikQA2() != null && StringUtils.isEmpty(transactionModel.getNameQA2())) {
            UserOfficerModel officer = map.get(transactionModel.getNikQA2().toUpperCase());
            if (officer != null) {
                transactionModel.setNameQA2(officer.getOfficerName());
            }
        }

        Map<String, String> mapNIKEmployee = new HashMap<>();
        if (transactionModel.getNikBM() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            mapNIKEmployee.putIfAbsent(transactionModel.getNikBM(), transactionModel.getNikBM());
        }
        if (transactionModel.getNikQA() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            mapNIKEmployee.putIfAbsent(transactionModel.getNikQA(), transactionModel.getNikQA());
        }
        if (transactionModel.getNikNOM() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            mapNIKEmployee.putIfAbsent(transactionModel.getNikNOM(), transactionModel.getNikNOM());
        }
        if (transactionModel.getNikODH() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            mapNIKEmployee.putIfAbsent(transactionModel.getNikODH(), transactionModel.getNikODH());
        }
        if (transactionModel.getNikSKAI() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            mapNIKEmployee.putIfAbsent(transactionModel.getNikSKAI(), transactionModel.getNikSKAI());
        }
        HashMap<String, Employee> mapEmployee = getEmployeeMap(new ArrayList(mapNIKEmployee.keySet()), employeeRepository);
        if (transactionModel.getNikBM() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            Employee employee = mapEmployee.get(transactionModel.getNikBM());
            if (employee != null) {
                transactionModel.setNameBM(employee.getFullName());
            }
        }
        if (transactionModel.getNikQA() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            Employee employee = mapEmployee.get(transactionModel.getNikQA());
            if (employee != null) {
                transactionModel.setNameQA(employee.getFullName());
            }
        }
        if (transactionModel.getNikNOM() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            Employee employee = mapEmployee.get(transactionModel.getNikNOM());
            if (employee != null) {
                transactionModel.setNameNOM(employee.getFullName());
            }
        }
        if (transactionModel.getNikODH() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            Employee employee = mapEmployee.get(transactionModel.getNikODH());
            if (employee != null) {
                transactionModel.setNameODH(employee.getFullName());
            }
        }
        if (transactionModel.getNikSKAI() != null && StringUtils.isEmpty(transactionModel.getNameBOS())) {
            Employee employee = mapEmployee.get(transactionModel.getNikSKAI());
            if (employee != null) {
                transactionModel.setNameSKAI(employee.getFullName());
            }
        }
        return transactionModel;
    }

    @Transactional
    public CommonResponse<InputTransactionResponse> cashOpnameVerification(HeadTellerApprovalReq request, Profile profile, boolean isAdmin) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(CommonConstant.SUBMIT_APPROVAL);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        inputTransactionResponse.setTransactionId(request.getApprovalTransactionId());
        inputTransactionResponse.setRequestId(request.getRequestId());
        try {
            TrxKasBesar kasBesar = trxKasBesarRepository.findByTransactionId(request.getApprovalTransactionId());
            CashOpname cashOpname = cashOpnameRepository.findByTransactionId(request.getApprovalTransactionId());
            if (validateVerification(cashOpname.getNikBM(), request.getNikVerification()) ||
                    validateVerification(cashOpname.getNikTeller(), request.getNikVerification()) ||
                    validateVerification(cashOpname.getNikNOM(), request.getNikVerification()) ||
                    validateVerification(cashOpname.getNikBOM(), request.getNikVerification()) ||
                    validateVerification(cashOpname.getNikBOS(), request.getNikVerification()) ||
                    validateVerification(cashOpname.getNikQA(), request.getNikVerification()) ||
                    validateVerification(cashOpname.getNikODH(), request.getNikVerification()) ||
                    validateVerification(cashOpname.getNikSKAI(), request.getNikVerification()) ||
                    validateVerification(cashOpname.getNikAltTeller(), request.getNikVerification()) ||
                    validateVerification(cashOpname.getNikQA2(), request.getNikVerification())) {
                TrxAmountDetail detail = trxAmountDetailRepository.findByTransactionId(kasBesar.getTransactionId());
                if (kasBesar.getStatus().equals(TrxStatus.APPROVED.getValue()) || kasBesar.getStatus().equals(TrxStatus.REJECTED.getValue())) {
                    return response.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
                }
                BranchBalance branchBalance = branchBalanceRepository.findByBranchId(cashOpname.getBranchId());

                if (request.getStatus().equals(TrxStatus.APPROVED.getValue())) {

                    if (cashOpname.getNikBM()!= null && cashOpname.getNikBM().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationBM(TrxStatus.VERIFIED.getValue());
                        cashOpname.setDateVerificationBM(LocalDateTime.now());

                    }else if (cashOpname.getNikQA()!= null && cashOpname.getNikQA().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationQA(TrxStatus.VERIFIED.getValue());
                        cashOpname.setDateVerificationQA(LocalDateTime.now());

                    }else if (cashOpname.getNikTeller()!= null && cashOpname.getNikTeller().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationTeller(TrxStatus.VERIFIED.getValue());
                        cashOpname.setDateVerificationTeller(LocalDateTime.now());
                    }else if (cashOpname.getNikBOS()!= null && cashOpname.getNikBOS().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationBOS(TrxStatus.VERIFIED.getValue());
                        cashOpname.setDateVerificationBOS(LocalDateTime.now());
                    }else if (cashOpname.getNikNOM()!= null && cashOpname.getNikNOM().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationNOM(TrxStatus.VERIFIED.getValue());
                        cashOpname.setDateVerificationNOM(LocalDateTime.now());
                    }else if (cashOpname.getNikODH()!= null && cashOpname.getNikODH().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationODH(TrxStatus.VERIFIED.getValue());
                        cashOpname.setDateVerificationODH(LocalDateTime.now());
                    }else if (cashOpname.getNikSKAI()!= null && cashOpname.getNikSKAI().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationSKAI(TrxStatus.VERIFIED.getValue());
                        cashOpname.setDateVerificationSKAI(LocalDateTime.now());
                    }else if (cashOpname.getNikAltTeller()!= null && cashOpname.getNikAltTeller().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationAltTeller(TrxStatus.VERIFIED.getValue());
                        cashOpname.setDateVerificationAltTeller(LocalDateTime.now());
                    }else if (cashOpname.getNikQA2()!= null && cashOpname.getNikQA2().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationQA2(TrxStatus.VERIFIED.getValue());
                        cashOpname.setDateVerificationQA2(LocalDateTime.now());
                    }
                    if(checkApprovedCashOpnameRole(cashOpname)){
                        cashOpname.setStatus(TrxStatus.APPROVED.getValue());
                        kasBesar.setStatus(TrxStatus.APPROVED.getValue());
                        if (!compareCashOpname(detail, branchBalance)) {
                            branchBalance.setAmount(detail);
                            branchBalance.setUpdateDateTime(LocalDateTime.now());
                            branchBalanceRepository.save(branchBalance);
                            kasBesar.setAdditionalData(BALANCE_OVERRIDE);
                            kasBesar.setReason(cashOpname.getReason());
                            kasBesar.setAdditionalInfo("Terdapat penyesuaian saldo vault");
                        }
                    }
                    insertAuditTrail(profile.getPreferred_username(), Action.APPROVE_CASH_OPNAME.getValue(), kasBesar.getTransactionId(), kasBesar.getBranchId());
                } else if (request.getStatus().equalsIgnoreCase(TrxStatus.REJECTED.getValue())) {
                    if (cashOpname.getNikBM()!= null && cashOpname.getNikBM().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationBM(TrxStatus.REJECTED.getValue());
                        cashOpname.setDateVerificationBM(LocalDateTime.now());
                    }else if (cashOpname.getNikQA()!= null &&  cashOpname.getNikQA().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationQA(TrxStatus.REJECTED.getValue());
                        cashOpname.setDateVerificationQA(LocalDateTime.now());
                    }else if (cashOpname.getNikTeller()!= null &&cashOpname.getNikTeller().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationTeller(TrxStatus.REJECTED.getValue());
                        cashOpname.setDateVerificationTeller(LocalDateTime.now());
                    }else if (cashOpname.getNikBOS()!= null && cashOpname.getNikBOS().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationBOS(TrxStatus.REJECTED.getValue());
                        cashOpname.setDateVerificationBOS(LocalDateTime.now());
                    }else if (cashOpname.getNikNOM()!= null && cashOpname.getNikNOM().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationNOM(TrxStatus.REJECTED.getValue());
                        cashOpname.setDateVerificationNOM(LocalDateTime.now());
                    }else if (cashOpname.getNikODH()!= null && cashOpname.getNikODH().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationODH(TrxStatus.REJECTED.getValue());
                        cashOpname.setDateVerificationODH(LocalDateTime.now());
                    }else if (cashOpname.getNikSKAI()!= null && cashOpname.getNikSKAI().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationSKAI(TrxStatus.REJECTED.getValue());
                        cashOpname.setDateVerificationSKAI(LocalDateTime.now());
                    }else if (cashOpname.getNikAltTeller()!= null && cashOpname.getNikAltTeller().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationAltTeller(TrxStatus.REJECTED.getValue());
                        cashOpname.setDateVerificationAltTeller(LocalDateTime.now());
                    }else if (cashOpname.getNikQA2()!= null && cashOpname.getNikQA2().equalsIgnoreCase(request.getNikVerification())){
                        cashOpname.setStatusVerificationQA2(TrxStatus.REJECTED.getValue());
                        cashOpname.setDateVerificationQA2(LocalDateTime.now());
                    }
                    cashOpname.setStatus(TrxStatus.REJECTED.getValue());
                    cashOpname.setReason(encodeIfnotNull(request.getReason()));
                    kasBesar.setStatus(TrxStatus.REJECTED.getValue());
                    kasBesar.setReason(encodeIfnotNull(request.getReason()));
                    if (isAdmin) {
                        insertAuditTrail(profile.getPreferred_username(), Action.REJECT_CASH_OPNAME.getValue().concat(ACTION_ADMIN), kasBesar.getTransactionId(), kasBesar.getBranchId());

                    } else {
                        insertAuditTrail(profile.getPreferred_username(), Action.REJECT_CASH_OPNAME.getValue(), kasBesar.getTransactionId(), kasBesar.getBranchId());
                    }
                }
                inputTransactionResponse.setStatusDesc(ResponseStatus.SUCCESS.getValue());
                inputTransactionResponse.setStatus(ResponseStatus.SUCCESS.getCode());
                
            }else {
                inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
                inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
                response.setData(inputTransactionResponse);
                response.setStatus(ResponseStatus.FAILED.getCode());
                response.setStatusDesc(ResponseStatus.FAILED.getValue());
                return response;
            }
        } catch (Exception e) {
            inputTransactionResponse.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
            inputTransactionResponse.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
            response.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
            response.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
            logger.error("Error verification Cash Opname " + e);
        }
        response.setData(inputTransactionResponse);
        return response;
    }
    public boolean compareCashOpname(TrxAmountDetail detail, BranchBalance balance){
        return detail.getC50Count().equals(balance.getC50Count()) && detail.getC50Amount().equals(balance.getC50Amount())
                && detail.getC100Count().equals(balance.getC100Count()) && detail.getC100Amount().equals(balance.getC100Amount())
                && detail.getC200Count().equals(balance.getC200Count()) && detail.getC200Amount().equals(balance.getC200Amount())
                && detail.getC500Count().equals(balance.getC500Count()) && detail.getC500Amount().equals(balance.getC500Amount())
                && detail.getC1KCount().equals(balance.getC1KCount()) && detail.getC1KAmount().equals(balance.getC1KAmount())
                && detail.getP1KCount().equals(balance.getP1KCount()) && detail.getP1KAmount().equals(balance.getP1KAmount())
                && detail.getP2KCount().equals(balance.getP2KCount()) && detail.getP2KAmount().equals(balance.getP2KAmount())
                && detail.getP5KCount().equals(balance.getP5KCount()) && detail.getP5KAmount().equals(balance.getP5KAmount())
                && detail.getP10KCount().equals(balance.getP10KCount()) && detail.getP10KAmount().equals(balance.getP10KAmount())
                && detail.getP20KCount().equals(balance.getP20KCount()) && detail.getP20KAmount().equals(balance.getP20KAmount())
                && detail.getP50KCount().equals(balance.getP50KCount()) && detail.getP50KAmount().equals(balance.getP50KAmount())
                && detail.getP75KCount().equals(balance.getP75KCount()) && detail.getP75KAmount().equals(balance.getP75KAmount())
                && detail.getP100KCount().equals(balance.getP100KCount()) && detail.getP100KAmount().equals(balance.getP100KAmount())
                ;
    }

    public CommonResponse<PendingCashOpname> getPendingCashOpname(String nik, String branchId) {
        CommonResponse<PendingCashOpname> response = new CommonResponse<>();
        PendingCashOpname cashOpname = new PendingCashOpname();
        LocalDate period = LocalDate.now();
        TrxKasBesar trxKasBesar = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeAndStatusInOrderByCreateDateTimeDesc(period, branchId, TrxType.CASHOPNAME.getCode(), Set.of(TrxStatus.PENDING.getValue(), TrxStatus.REJECTED.getValue()));
        if (trxKasBesar != null) {
            cashOpname.setExists(true);
            cashOpname.setLastTransactionId(trxKasBesar.getTransactionId());
            cashOpname.setStatus(trxKasBesar.getStatus());
        } else {
            cashOpname.setExists(false);
        }
        BranchBalance branchBalance = branchBalanceRepository.findByBranchId(branchId);
        BalanceModel balanceModel = new BalanceModel();
        if (branchBalance != null) {
            cashOpname.setBranchBalance(branchBalance.getTotal());
            balanceModel.setBalanceModel(branchBalance);
        }else {
            cashOpname.setBranchBalance(0.0);
        }
        cashOpname.setBalanceDetails(convertCashOpnameAmountDetail(balanceModel));
        cashOpname.setPeriod(period);
        cashOpname.setBranchId(branchId);
        response.setType(GET_PENDING_CASH_OPNAME);
        response.setStatus(ResponseStatus.SUCCESS.getCode());
        response.setStatusDesc(ResponseStatus.SUCCESS.getValue());
        response.setData(cashOpname);
        return response;
    }
    public CommonResponse<CashOpnameListBAModel> cashOpnameListBA(Profile nik, String branchId, String page, String limit, String startPeriod, String endPeriod){
        CommonResponse<CashOpnameListBAModel> response = new CommonResponse<>();
        CashOpnameListBAModel cashOpnameListBAModel = new CashOpnameListBAModel();
        LocalDate start = LocalDate.parse(startPeriod);
        LocalDate end = LocalDate.parse(endPeriod);
        Cabang cabang = cabangRepository.findByCabangId(branchId);
        String branch = cabang.getCabangId();
        int pageableLimit = cashOpnameListBAModel.getLimit();
        int pageablePage =   cashOpnameListBAModel.getPage();
        if (limit != null && limit.equals(LIMIT_ALL)){
            Integer count = cashOpnameRepository.getAllListTransaction(start,end, branch, BOM_ROLE, TELLER_ROLE);
            pageableLimit = count == 0 ? pageableLimit : count;
        }else if (limit != null && page != null){
            pageablePage = Integer.parseInt(page);
            pageableLimit = Integer.parseInt(limit);
        }
        Pageable pageable = PageRequest.of(pageablePage, pageableLimit).previous();
        Page<CashOpnameListBADetailModel> details = cashOpnameRepository.getDetailListBACashOpname(start,end, branch, BOM_ROLE,TELLER_ROLE, pageable);
        cashOpnameListBAModel.setPage(pageablePage);
        cashOpnameListBAModel.setLimit(pageableLimit);
        cashOpnameListBAModel.setTotalItems(details.getTotalElements());
        cashOpnameListBAModel.setTotalPages(details.getTotalPages());
        cashOpnameListBAModel.setStartPeriod(startPeriod);
        cashOpnameListBAModel.setEndPeriod(endPeriod);
        cashOpnameListBAModel.setBranchId(branch);
        cashOpnameListBAModel.setBranchName(cabang.getCabangDesc());
        cashOpnameListBAModel.setDetails(enrichOfficerCashOpname(details.getContent()));
        response.setType(GET_CASH_OPNAME_LIST_BA);
        response.setData(cashOpnameListBAModel);
        return response;
    }

    public boolean checkApprovedCashOpnameRole(CashOpname cashOpname) {
        if ( cashOpname.getStatusVerificationTeller() != null && cashOpname.getStatusVerificationTeller().equals(TrxStatus.PENDING.getValue())) {
            return false;
        }
        if (cashOpname.getStatusVerificationBM() != null && cashOpname.getStatusVerificationBM().equals(TrxStatus.PENDING.getValue()) ) {
            return false;
        }
        if (cashOpname.getStatusVerificationQA() != null && cashOpname.getStatusVerificationQA().equals(TrxStatus.PENDING.getValue()) ) {
            return false;
        }
        if (cashOpname.getStatusVerificationBOS() != null && cashOpname.getStatusVerificationBOS().equals(TrxStatus.PENDING.getValue())) {
            return false;
        }
        if (cashOpname.getStatusVerificationNOM() != null && cashOpname.getStatusVerificationNOM().equals(TrxStatus.PENDING.getValue())) {
            return false;
        }
        if (cashOpname.getStatusVerificationODH() != null && cashOpname.getStatusVerificationODH().equals(TrxStatus.PENDING.getValue())) {
            return false;
        }
        if (cashOpname.getStatusVerificationSKAI() != null && cashOpname.getStatusVerificationSKAI().equals(TrxStatus.PENDING.getValue())) {
            return false;
        }
        if (cashOpname.getStatusVerificationAltTeller() != null && cashOpname.getStatusVerificationAltTeller().equals(TrxStatus.PENDING.getValue())) {
            return false;
        }
        if (cashOpname.getStatusVerificationQA2() != null && cashOpname.getStatusVerificationQA2().equals(TrxStatus.PENDING.getValue())) {
            return false;
        }
        return true;
    }
    public CommonResponse<CheckMonthlyCOPResponse> checkMonthlyCashOpname(Profile nik, String branchId){
        CommonResponse<CheckMonthlyCOPResponse> response = new CommonResponse<>();
        response.setType(CHECK_MONTHLY_CASH_OPNAME);
        CheckMonthlyCOPResponse monthlyCOPResponse = new CheckMonthlyCOPResponse();
        Cabang branch = cabangRepository.findByCabangId(branchId);
        LocalDate now = LocalDate.now();
        LocalDate day1Month = now.withDayOfMonth(1);
        
        if (now.isBefore(now.withDayOfMonth(26))){
            day1Month = day1Month.minusMonths(1);
        }
        CashOpname checkMonthly =  cashOpnameRepository.findTopByBranchIdAndPeriodGreaterThanEqualAndPeriodLessThanEqualAndStatus(branchId,day1Month,now,TrxStatus.APPROVED.getValue());
        monthlyCOPResponse.setBranchId(branchId);
        monthlyCOPResponse.setBranchName(branch == null ? null : branch.getCabangDesc());
        if (checkMonthly !=null){
            monthlyCOPResponse.setStatus(FOUND_MONTHLY_TRANSACTION);
            monthlyCOPResponse.setLastTransactionId(checkMonthly.getTransactionId());
        }else {
            monthlyCOPResponse.setStatus(NOT_FOUND_MONTHLY_TRANSACTION);
        }
        response.setData(monthlyCOPResponse);
        return response;
    }
    public List<AmountDetail> convertCashOpnameAmountDetail(BalanceModel trxAmountDetail){
        Gson gson = new Gson();
        AmountConfig amountConfig = new AmountConfig();
        List<AmountDetail> amountDetails= gson.fromJson(amountConfig.amount(), new TypeToken<List<AmountDetail>>() {
        }.getType());
        for (AmountDetail amountDetail : amountDetails) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    amountDetail.setCount(trxAmountDetail.getC50Count());
                    amountDetail.setTotal(trxAmountDetail.getC50Amount());
                    break;
                case COIN_100:
                    amountDetail.setCount(trxAmountDetail.getC100Count());
                    amountDetail.setTotal(trxAmountDetail.getC100Amount());
                    break;
                case COIN_200:
                    amountDetail.setCount(trxAmountDetail.getC200Count());
                    amountDetail.setTotal(trxAmountDetail.getC200Amount());
                    break;
                case COIN_500:
                    amountDetail.setCount(trxAmountDetail.getC500Count());
                    amountDetail.setTotal(trxAmountDetail.getC500Amount());
                    break;
                case COIN_1K:
                    amountDetail.setCount(trxAmountDetail.getC1KCount());
                    amountDetail.setTotal(trxAmountDetail.getC1KAmount());
                    break;
                case PAPER_1K:
                    amountDetail.setCount(trxAmountDetail.getP1KCount());
                    amountDetail.setTotal(trxAmountDetail.getP1KAmount());
                    break;
                case PAPER_2K:
                    amountDetail.setCount(trxAmountDetail.getP2KCount());
                    amountDetail.setTotal(trxAmountDetail.getP2KAmount());
                    break;
                case PAPER_5K:
                    amountDetail.setCount(trxAmountDetail.getP5KCount());
                    amountDetail.setTotal(trxAmountDetail.getP5KAmount());
                    break;
                case PAPER_10K:
                    amountDetail.setCount(trxAmountDetail.getP10KCount());
                    amountDetail.setTotal(trxAmountDetail.getP10KAmount());
                    break;
                case PAPER_20K:
                    amountDetail.setCount(trxAmountDetail.getP20KCount());
                    amountDetail.setTotal(trxAmountDetail.getP20KAmount());
                    break;
                case PAPER_50K:
                    amountDetail.setCount(trxAmountDetail.getP50KCount());
                    amountDetail.setTotal(trxAmountDetail.getP50KAmount());
                    break;
                case PAPER_75K:
                    amountDetail.setCount(trxAmountDetail.getP75KCount());
                    amountDetail.setTotal(trxAmountDetail.getP75KAmount());
                    break;
                case PAPER_100K:
                    amountDetail.setCount(trxAmountDetail.getP100KCount());
                    amountDetail.setTotal(trxAmountDetail.getP100KAmount());
                    break;
            }
        }
        return amountDetails;
    }
}
