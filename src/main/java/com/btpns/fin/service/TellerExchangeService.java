package com.btpns.fin.service;

import com.btpns.fin.configuration.AmountConfig;
import com.btpns.fin.constant.Action;
import com.btpns.fin.constant.ResponseStatus;
import com.btpns.fin.constant.TrxStatus;
import com.btpns.fin.constant.TrxType;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.SubmitTellerExchangeRequest;
import com.btpns.fin.model.request.TellerExchangeApprovalRequest;
import com.btpns.fin.model.request.TellerExchangeCancelRequest;
import com.btpns.fin.model.response.ActiveTellerExchangeDetailModel;
import com.btpns.fin.model.response.InputTransactionResponse;
import com.btpns.fin.model.response.TellerExchangeCancelResponse;
import com.btpns.fin.repository.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.btpns.fin.constant.CommonConstant.*;
import static com.btpns.fin.constant.TrxType.TEV2HT;
import static com.btpns.fin.helper.CommonHelper.*;


@Service
public class TellerExchangeService {

    private static final Logger logger = LoggerFactory.getLogger(TrxKasBesarService.class);

    @Autowired
    TrxTellerExchangeRepository trxTellerExchangeRepository;

    @Autowired
    CabangRepository cabangRepository;

    @Autowired
    OfficerRepository officerRepository;

    @Autowired
    TrxAuditTrailRepository trxAuditTrailRepository;
    @Autowired
    TrxTellerExchangeVaultRepository trxTellerExchangeVaultRepository;
    @Autowired
    TrxPendingHeadTellerRepository trxPendingHeadTellerRepository;
    @Autowired
    HeadTellerBalanceRepository headTellerBalanceRepository;

    @Autowired
    TrxHeadTellerRepository trxHeadTellerRepository;

    @Autowired
    TrxHTAmountDetailRepository trxHTAmountDetailRepository;

    @Autowired
    BranchBalanceRepository branchBalanceRepository;

    @Autowired
    TrxKasBesarRepository trxKasBesarRepository;

    @Autowired
    TrxAmountDetailRepository trxAmountDetailRepository;
    @Autowired
    OfficerNonProsperaRepository officerNonProsperaRepository;
    @Autowired
    OfficerImpl officerImpl;

    public CommonResponse<ActiveTellerExchangeModel> activeTellerExchange(Profile nik, String type, String branchId) {
        CommonResponse<ActiveTellerExchangeModel> response = new CommonResponse<>();
        response.setType(GET_TELLER_EXCHANGE_ACTIVE_TRANSACTION);
        Gson gson = new Gson();
        ActiveTellerExchangeModel activeTellerExchangeModel = new ActiveTellerExchangeModel();
        ActiveTellerExchangeDetailModel detailModel = new ActiveTellerExchangeDetailModel();
        LocalDate period = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddMMyyyy");
        try {
            Cabang cabang = cabangRepository.findByCabangId(branchId);
            TrxTellerExchange trxTellerExchange = trxTellerExchangeRepository.findTopByPeriodAndBranchIdAndTypeAndStatusInAndInputerNikOrderByCreateDateTimeDesc(period, cabang.getCabangId(), type, Set.of(TrxStatus.REJECTED.getValue(), TrxStatus.PENDING.getValue()),nik.getPreferred_username());
            if (trxTellerExchange == null) {
                activeTellerExchangeModel.setStatusTE(ResponseStatus.ALLOW_TELLER_EXCHANGE.getCode());
            } else {
                String inputerName = getOfficerName(trxTellerExchange.getInputerNik(), branchId);
                String verificationName = getOfficerName(trxTellerExchange.getVerificationNik(), branchId);
                detailModel.setRequestId(trxTellerExchange.getRequestId());
                detailModel.setTransactionId(trxTellerExchange.getTransactionId());
                detailModel.setBranchId(trxTellerExchange.getBranchId());
                detailModel.setPeriod(formatter.format(trxTellerExchange.getPeriod()));
                detailModel.setBranchName(cabang.getCabangDesc());
                detailModel.setType(trxTellerExchange.getType());
                detailModel.setFromName(trxTellerExchange.getFromName());
                detailModel.setToName(trxTellerExchange.getToName());
                detailModel.setRequestFlag(trxTellerExchange.isRequestFlag());
                detailModel.setDepositFlag(trxTellerExchange.isDepositFlag());
                detailModel.setStatus(trxTellerExchange.getStatus());
                detailModel.setInputerNIK(trxTellerExchange.getInputerNik());
                detailModel.setInputerName(inputerName);
                detailModel.setVerificationNIK(trxTellerExchange.getVerificationNik());
                detailModel.setVerificationName(verificationName);
                detailModel.setTotalAmount(trxTellerExchange.getTotalAmount());
                detailModel.setTotalAmountSpelled(trxTellerExchange.getTotalAmountSpelled());
                List<AmountDetail> details = gson.fromJson(trxTellerExchange.getAmountDetail(), new TypeToken<List<AmountDetail>>() {
                }.getType());
                detailModel.setAmountDetail(details);
                activeTellerExchangeModel.setDetail(detailModel);
                if (trxTellerExchange.getStatus().equals(TrxStatus.REJECTED.getValue())) {
                    activeTellerExchangeModel.setStatusTE(ResponseStatus.FOUND_TELLER_EXCHANGE_REJECTED.getCode());
                } else if (trxTellerExchange.getStatus().equals(TrxStatus.PENDING.getValue())) {
                    activeTellerExchangeModel.setStatusTE(ResponseStatus.FOUND_TELLER_EXCHANGE_PENDING.getCode());
                }
            }
            response.setData(activeTellerExchangeModel);
        } catch (Exception e) {
            logger.error("Get Active Teller Exchange Error cause " + e.getMessage());
        }
        response.setData(activeTellerExchangeModel);
        return response;
    }

    @Transactional
    public CommonResponse<InputTransactionResponse> submitTellerExchange(SubmitTellerExchangeRequest request, Profile nik) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(SUBMIT_TELLER_EXCHANGE);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        Gson gson = new Gson();
        String transactionId = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddMMyyyy");
        LocalDate period = LocalDate.parse(request.getDetails().getPeriod(), formatter);
        LocalDateTime now = LocalDateTime.now();

        try {
            if (validationDenomAndVerificationTellerExchange(request.getDetails().getAmountDetail(), request.getDetails().getVerificationNIK(), request.getDetails().getInputerNIK())) {
                checkBranchBalance(request.getDetails().getBranchId(), branchBalanceRepository);
                checkHTBranchBalance(request.getDetails().getBranchId(), headTellerBalanceRepository);
                if (request.getType().equals(TrxType.TEHT2T.getCode()) || request.getType().equals(TrxType.TET2HT.getCode())) {
                    TrxTellerExchange trxTellerExchange = new TrxTellerExchange();
                    if (request.getRequestType().equals(REQUEST_TYPE_NEW)) {
                        trxTellerExchange.setRequestId(request.getDetails().getRequestId());
                        trxTellerExchange.setTransactionId(generateTransactionId(request.getDetails().getBranchId(), request.getType(), period, TELLER_EXCHANGE));
                        trxTellerExchange.setFromName(request.getDetails().getFromName());
                        trxTellerExchange.setBranchId(request.getDetails().getBranchId());
                        trxTellerExchange.setPeriod(period);
                        trxTellerExchange.setStatus(TrxStatus.PENDING.getValue());
                        trxTellerExchange.setType(request.getType());
                        trxTellerExchange.setToName(request.getDetails().getToName());
                        trxTellerExchange.setRequestFlag(request.getDetails().isRequestFlag());
                        trxTellerExchange.setDepositFlag(request.getDetails().isDepositFlag());
                        trxTellerExchange.setInputerNik(request.getDetails().getInputerNIK());
                        trxTellerExchange.setVerificationNik(request.getDetails().getVerificationNIK());
                        trxTellerExchange.setTotalAmount(request.getDetails().getTotalAmount());
                        trxTellerExchange.setTotalAmountSpelled(request.getDetails().getTotalAmountSpelled());
                        trxTellerExchange.setCreateDateTime(now);
                        trxTellerExchange.setUpdateDateTime(now);
                        transactionId = trxTellerExchange.getTransactionId();
                    } else if (request.getRequestType().equals(REQUEST_TYPE_UPDATE)) {
                        trxTellerExchange = trxTellerExchangeRepository.findByTransactionId(request.getDetails().getTransactionId());
                        if (trxTellerExchange != null) {
                            trxTellerExchange.setFromName(request.getDetails().getFromName());
                            trxTellerExchange.setToName(request.getDetails().getToName());
                            trxTellerExchange.setRequestFlag(request.getDetails().isRequestFlag());
                            trxTellerExchange.setDepositFlag(request.getDetails().isDepositFlag());
                            trxTellerExchange.setStatus(TrxStatus.PENDING.getValue());
                            trxTellerExchange.setInputerNik(request.getDetails().getInputerNIK());
                            trxTellerExchange.setVerificationNik(request.getDetails().getVerificationNIK());
                            trxTellerExchange.setTotalAmount(request.getDetails().getTotalAmount());
                            trxTellerExchange.setTotalAmountSpelled(request.getDetails().getTotalAmountSpelled());
                            trxTellerExchange.setUpdateDateTime(now);
                            transactionId = trxTellerExchange.getTransactionId();

                        }
                    }
                    trxTellerExchange.setAmountDetail(gson.toJson(request.getDetails().getAmountDetail()));
                    trxTellerExchangeRepository.save(trxTellerExchange);

                } else if (TEV2HT.getCode().equals(request.getType())) {
                    TrxTellerExchangeVault tellerExchangeVault = new TrxTellerExchangeVault();
                    if (request.getRequestType().equals(REQUEST_TYPE_NEW)){
                        checkSubmitExistingTellerExchangeVault(period, request.getDetails().getBranchId(), request.getType(), TrxStatus.SUBMIT.getValue());
                        tellerExchangeVault.setRequestId(request.getDetails().getRequestId());
                        tellerExchangeVault.setTransactionId(generateTransactionId(request.getDetails().getBranchId(), request.getType(), period, TELLER_EXCHANGE_VAULT));
                        tellerExchangeVault.setType(request.getType());
                        tellerExchangeVault.setPeriod(period);
                        tellerExchangeVault.setBranchId(request.getDetails().getBranchId());
                        tellerExchangeVault.setCreateDateTime(now);
                        transactionId = tellerExchangeVault.getTransactionId();
                    }else if (request.getRequestType().equals(REQUEST_TYPE_UPDATE)) {
                        tellerExchangeVault = trxTellerExchangeVaultRepository.findByTransactionId(request.getDetails().getTransactionId());
                        if (tellerExchangeVault != null) {
                            transactionId = tellerExchangeVault.getTransactionId();
                        }
                    }
                    tellerExchangeVault.setFromName(request.getDetails().getFromName());
                    tellerExchangeVault.setToName(request.getDetails().getToName());
                    tellerExchangeVault.setRequestFlag(request.getDetails().isRequestFlag());
                    tellerExchangeVault.setDepositFlag(request.getDetails().isDepositFlag());
                    tellerExchangeVault.setStatus(TrxStatus.SUBMIT.getValue());
                    tellerExchangeVault.setInputerNik(request.getDetails().getInputerNIK());
                    tellerExchangeVault.setVerificationNik(request.getDetails().getVerificationNIK());
                    tellerExchangeVault.setTotalAmount(request.getDetails().getTotalAmount());
                    tellerExchangeVault.setTotalAmountSpelled(request.getDetails().getTotalAmountSpelled());
                    tellerExchangeVault.setUpdateDateTime(now);
                    tellerExchangeVault.setAmountDetail(gson.toJson(request.getDetails().getAmountDetail()));
                    trxTellerExchangeVaultRepository.save(tellerExchangeVault);
                }
                inputTransactionResponse.setRequestId(request.getDetails().getRequestId());
                inputTransactionResponse.setTransactionId(transactionId);
                inputTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
                inputTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
                insertAuditTrail(nik.getPreferred_username(), Action.SUBMIT_TELLER_EXCHANGE.getValue(), transactionId, request.getDetails().getBranchId());
            } else {
                inputTransactionResponse.setRequestId(request.getDetails().getRequestId());
                inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
                inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
                response.setStatus(TrxStatus.FAILED.getCode());
                response.setStatusDesc(TrxStatus.FAILED.getValue());
                logger.error("Submit Teller Exchange Failed Request Not Complete");
            }
        } catch (Exception e) {
            inputTransactionResponse.setRequestId(request.getDetails().getRequestId());
            inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
            logger.error("Submit Teller Exchange Error " + e.getMessage());
        }
        response.setData(inputTransactionResponse);
        return response;
    }


    private void checkSubmitExistingTellerExchangeVault(LocalDate period, String branchId, String type, String status){
        TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findTopByPeriodAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(period, branchId, type, status);
        if (vault != null){
            vault.setStatus(TrxStatus.CANCEL.getValue());
            vault.setUpdateDateTime(LocalDateTime.now());
            trxTellerExchangeVaultRepository.save(vault);
        }
    }

    public String generateTransactionId(String branchId, String type, LocalDate period, String process) {
        Integer getLastTransactionId;
        if (process.equals(TELLER_EXCHANGE)) {
            getLastTransactionId = trxTellerExchangeRepository.getLastTrxTellerExchangeTransactionId(branchId, type, period);
        }else if (process.equals(TELLER_EXCHANGE_VAULT)){
            getLastTransactionId = trxTellerExchangeVaultRepository.getLastTrxTellerExchangeVaultTransactionId(branchId, type, period);
        }else{
            getLastTransactionId = trxPendingHeadTellerRepository.getLastTransactionId(branchId,type,period) ;
        }
        String transactionNo = String.format("%02d", 1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        if (getLastTransactionId != null) {
            getLastTransactionId += 1;
            transactionNo = String.format("%02d", getLastTransactionId);
        }
        return type + formatter.format(period) + branchId + transactionNo;

    }

    public void insertAuditTrail(String nik, String action, String transactionId, String branchId){
        TrxAuditTrail trxAuditTrail = new TrxAuditTrail();
        trxAuditTrail.setNik(nik);
        trxAuditTrail.setAction(action);
        trxAuditTrail.setCreateDateTime(LocalDateTime.now());
        trxAuditTrail.setTransactionId(transactionId);
        trxAuditTrail.setBranchId(branchId);
        trxAuditTrailRepository.save(trxAuditTrail);
    }

    public CommonResponse<TellerExchangeDetailModel> detailTellerExchange(Profile nik, String transactionId){
        CommonResponse<TellerExchangeDetailModel> response = new CommonResponse<>();
        response.setType(GET_TELLER_EXCHANGE_DETAIL_TRANSACTION);
        TellerExchangeDetailModel detail = trxTellerExchangeRepository.getDetailTellerExchangeByTransactionId(transactionId);
        if (detail!=null){
            detail.setInputerName(officerImpl.getOfficerNameByNik(detail.getInputerNIK(), detail.getBranchId()));
            detail.setVerificationName(officerImpl.getOfficerNameByNik(detail.getVerificationNIK(), detail.getBranchId()));
            response.setStatus(ResponseStatus.SUCCESS.getCode());
            response.setStatusDesc(ResponseStatus.SUCCESS.getValue());
            response.setData(detail);
        }else {
            response.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
            response.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
        }
        return response;
    }

    public CommonResponse<TellerExchangeListModel> tellerExchangeList(Profile nik, String branch, String status, String type, String page, String limit){
        CommonResponse<TellerExchangeListModel> response = new CommonResponse<>();
        response.setType(GET_TELLER_EXCHANGE_LIST_TRANSACTION);
        TellerExchangeListModel tellerExchangeListModel = new TellerExchangeListModel();
        LocalDate period = LocalDate.now();
        Cabang cabang = cabangRepository.findByCabangId(branch);
        int pageableLimit = tellerExchangeListModel.getLimit();
        int pageablePage =   tellerExchangeListModel.getPage();
        if (limit != null && limit.equals(LIMIT_ALL)){
            Integer count = trxTellerExchangeRepository.getAllListTransaction(period, branch, status, type, nik.getPreferred_username());
            pageableLimit = count == 0 ? pageableLimit : count;
        }else if (limit != null && page != null){
            pageablePage = Integer.parseInt(page);
            pageableLimit = Integer.parseInt(limit);
        }
        Pageable pageable = PageRequest.of(pageablePage, pageableLimit).previous();
        Page<TellerExchangeListDetailModel> details = trxTellerExchangeRepository.getDetailListTellerExchange(period, branch, status, type, nik.getPreferred_username(), pageable);
        if (!details.isEmpty()){
            details.forEach(te->{
                te.setInputerName(getOfficerName(te.getInputerNIK(), branch));
                te.setVerificationName(getOfficerName(te.getVerificationNIK(), branch));
            });
        }
        tellerExchangeListModel.setPage(pageablePage);
        tellerExchangeListModel.setLimit(pageableLimit);
        tellerExchangeListModel.setTotalItems(details.getTotalElements());
        tellerExchangeListModel.setTotalPages(details.getTotalPages());
        tellerExchangeListModel.setPeriod(period.toString());
        tellerExchangeListModel.setBranchId(branch);
        tellerExchangeListModel.setBranchName(cabang.getCabangDesc());
        tellerExchangeListModel.setDetails(details.getContent());
        response.setData(tellerExchangeListModel);
        return response;
    }

    @Transactional
    public CommonResponse<InputTransactionResponse> approvalTellerExchange(TellerExchangeApprovalRequest request, Profile nik) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(APPROVAL_TELLER_EXCHANGE);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        inputTransactionResponse.setTransactionId(request.getApprovalTransactionId());
        inputTransactionResponse.setRequestId(request.getRequestId());
        try {
            if (request.getApprovalTransactionId().startsWith(TrxType.TEHT2V.getCode())) {
                TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findByTransactionId(request.getApprovalTransactionId());
                if (request.getStatus().equals(TrxStatus.APPROVED.getValue())) {
                    vault.setStatus(TrxStatus.SUBMIT.getValue());
                    vault.setVerificationNik(request.getNikVerification());
                } else if (request.getStatus().equals(TrxStatus.REJECTED.getValue())) {
                    vault.setStatus(TrxStatus.REJECTED.getValue());
                }
                trxTellerExchangeVaultRepository.save(vault);
            } else {
                TrxTellerExchange trxTellerExchange = trxTellerExchangeRepository.findByTransactionId(request.getApprovalTransactionId());
                if (!validateVerification(trxTellerExchange.getVerificationNik(), request.getNikVerification())) {
                    inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
                    inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
                    response.setData(inputTransactionResponse);
                    response.setStatus(TrxStatus.FAILED.getCode());
                    response.setStatusDesc(TrxStatus.FAILED.getValue());
                    return response;
                }
                if (request.getStatus().equals(TrxStatus.APPROVED.getValue())) {
                    if (trxTellerExchange != null && !trxTellerExchange.getStatus().equals(TrxStatus.APPROVED.getValue())) {
                        if (!validateRequestOfFund(trxTellerExchange.getType(), request.getSourceOfFund())){
                            inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
                            inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
                            response.setStatus(TrxStatus.FAILED.getCode());
                            response.setStatusDesc(TrxStatus.FAILED.getValue());
                            response.setData(inputTransactionResponse);
                            return response;
                        }
                        if (trxTellerExchange.getType().equals(TrxType.TEHT2T.getCode()) && request.getSourceOfFund().equals(TE_SOURCE_OF_FUND_BRANCH_BALANCE)) {
                            if (!validateActiveTellerExchangeVaultToHT(request.getBranchId())){
                                inputTransactionResponse.setStatus(ResponseStatus.TEV2HT_ALREADY_SUBMITTED.getCode());
                                inputTransactionResponse.setStatusDesc(ResponseStatus.TEV2HT_ALREADY_SUBMITTED.getValue());
                                response.setStatus(ResponseStatus.TEV2HT_ALREADY_SUBMITTED.getCode());
                                response.setStatusDesc(ResponseStatus.TEV2HT_ALREADY_SUBMITTED.getValue());
                                response.setData(inputTransactionResponse);
                                logger.error("Approve Teller Exchange Error , TEV2HT Already Submitted " );
                                return response;
                            }
                            TrxTellerExchangeVault activeTEV2HT = trxTellerExchangeVaultRepository.findTopByPeriodAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(LocalDate.parse(request.getPeriod()), request.getBranchId(), TEV2HT.getCode(), TrxStatus.PENDING.getValue());
                            if (activeTEV2HT != null) {
                                activeTEV2HT.setAmountDetail(updateExistingTEV2HT(activeTEV2HT, activeTEV2HT.getAmountDetail(), trxTellerExchange.getAmountDetail()));
                                activeTEV2HT.setTotalAmount(activeTEV2HT.getTotalAmount() + trxTellerExchange.getTotalAmount());
                                trxTellerExchangeVaultRepository.save(activeTEV2HT);
                            } else {
                                TrxTellerExchangeVault tellerExchangeVault = new TrxTellerExchangeVault();
                                tellerExchangeVault.setRequestId(request.getRequestId());
                                tellerExchangeVault.setTransactionId(generateTransactionId(request.getBranchId(), TEV2HT.getCode(), LocalDate.parse(request.getPeriod()), TELLER_EXCHANGE_VAULT));
                                tellerExchangeVault.setFromName(FROM_NAME_HEAD_TELLER);
                                tellerExchangeVault.setBranchId(trxTellerExchange.getBranchId());
                                tellerExchangeVault.setPeriod(LocalDate.parse(request.getPeriod()));
                                tellerExchangeVault.setStatus(TrxStatus.PENDING.getValue());
                                tellerExchangeVault.setType(TEV2HT.getCode());
                                tellerExchangeVault.setToName(TO_NAME_KHASANAH);
                                tellerExchangeVault.setRequestFlag(trxTellerExchange.isRequestFlag());
                                tellerExchangeVault.setDepositFlag(trxTellerExchange.isDepositFlag());
                                tellerExchangeVault.setInputerNik(trxTellerExchange.getInputerNik());
                                tellerExchangeVault.setVerificationNik(trxTellerExchange.getVerificationNik());
                                tellerExchangeVault.setTotalAmount(trxTellerExchange.getTotalAmount());
                                tellerExchangeVault.setTotalAmountSpelled(trxTellerExchange.getTotalAmountSpelled());
                                tellerExchangeVault.setCreateDateTime(LocalDateTime.now());
                                tellerExchangeVault.setUpdateDateTime(LocalDateTime.now());
                                tellerExchangeVault.setAmountDetail(trxTellerExchange.getAmountDetail());
                                trxTellerExchangeVaultRepository.save(tellerExchangeVault);
                            }
                        }else if (trxTellerExchange.getType().equals(TrxType.TEHT2T.getCode()) && request.getSourceOfFund().equals(TE_SOURCE_OF_FUND_HT_BALANCE) && !checkBalanceHeadTeller(trxTellerExchange)){
                            inputTransactionResponse.setStatus(ResponseStatus.COMPARE_HEAD_TELLER_BALANCE_MINUS.getCode());
                            inputTransactionResponse.setStatusDesc(ResponseStatus.COMPARE_HEAD_TELLER_BALANCE_MINUS.getValue());
                            response.setStatus(ResponseStatus.COMPARE_HEAD_TELLER_BALANCE_MINUS.getCode());
                            response.setStatusDesc(ResponseStatus.COMPARE_HEAD_TELLER_BALANCE_MINUS.getValue());
                            response.setData(inputTransactionResponse);
                            logger.error("Approve Teller Exchange Error Denom " );
                            return response;
                        }
                        trxTellerExchange.setSourceOfFund(request.getSourceOfFund());
                        trxTellerExchange.setStatus(TrxStatus.APPROVED.getValue());
                        submitPendingTellerExchange(request, trxTellerExchange);
                    }
                } else if (request.getStatus().equals(TrxStatus.REJECTED.getValue())) {
                    trxTellerExchange.setStatus(TrxStatus.REJECTED.getValue());
                }
                trxTellerExchangeRepository.save(trxTellerExchange);
            }
            inputTransactionResponse.setStatus(TrxStatus.SUCCESS.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
            insertAuditTrail(nik.getPreferred_username(), Action.APPROVE_TELLER_EXCHANGE.getValue(), request.getApprovalTransactionId(), request.getBranchId());
        } catch (Exception e) {
            inputTransactionResponse.setStatus(TrxStatus.FAILED.getCode());
            inputTransactionResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
            logger.error("Approve Teller Exchange Error " + e.getMessage());
        }
        response.setData(inputTransactionResponse);
        return response;
    }

    public String updateExistingTEV2HT(TrxTellerExchangeVault vault,String existingDetail, String newDetail){
        Gson gson = new Gson();
        List<AmountDetail> existingDetails = gson.fromJson(existingDetail, new TypeToken<List<AmountDetail>>() {
        }.getType());
        List<AmountDetail> newDetails = gson.fromJson(newDetail, new TypeToken<List<AmountDetail>>() {
        }.getType());
        Map<String,AmountDetail> detail = newDetails.stream().collect(Collectors.toMap(AmountDetail :: getId, Function.identity()));
        existingDetails.forEach(e-> {
           AmountDetail amountDetail = detail.get(e.getId());
            e.setCount(e.getCount() + amountDetail.getCount());
            e.setTotal(e.getTotal() + amountDetail.getTotal());
        });
        return gson.toJson(existingDetails);
    }

    public void submitPendingTellerExchange(TellerExchangeApprovalRequest request, TrxTellerExchange tellerExchange){
        Gson gson = new Gson();
        TrxPendingHeadTeller lastBalance = trxPendingHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeOrderByCreateDateTimeDesc(LocalDate.now(), request.getBranchId(), getSaldoType(tellerExchange.getType()));
        HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(request.getBranchId());
        BalanceModel balanceModel = new BalanceModel();
        if (lastBalance!=null){
            TrxHTAmountDetail trxAmountDetail = gson.fromJson(lastBalance.getAmountDetail(), TrxHTAmountDetail.class);
            balanceModel.setBalanceModel(trxAmountDetail);
            balanceModel.setBalance(trxAmountDetail.getTotal());
        }else if (headTellerBalance != null){
            balanceModel.setBalanceModel(headTellerBalance);
            balanceModel.setBalance(headTellerBalance.getTotalAmount());
        }
        TrxPendingHeadTeller trxPendingHeadTeller = new TrxPendingHeadTeller();
        trxPendingHeadTeller.setRequestId(request.getRequestId());
        trxPendingHeadTeller.setBranchId(request.getBranchId());
        trxPendingHeadTeller.setPeriod(LocalDate.parse(request.getPeriod()));
        trxPendingHeadTeller.setTransactionId(generateTransactionId(tellerExchange.getBranchId(), getType(tellerExchange.getType()), tellerExchange.getPeriod(), ADD_HT));
        trxPendingHeadTeller.setType(getType(tellerExchange.getType()));
        trxPendingHeadTeller.setStatus(TrxStatus.PENDING.getValue());
        trxPendingHeadTeller.setInputer(tellerExchange.getVerificationNik());
        trxPendingHeadTeller.setTellerId(tellerExchange.getInputerNik());
        trxPendingHeadTeller.setTotalAmount(tellerExchange.getTotalAmount());
        trxPendingHeadTeller.setBalance(getAmount(balanceModel.getBalance(), trxPendingHeadTeller.getTotalAmount(), tellerExchange.getType()));
        trxPendingHeadTeller.setCreateDateTime(LocalDateTime.now());
        trxPendingHeadTeller.setUpdateDateTime(LocalDateTime.now());
        TrxPendingHeadTeller saldo = new TrxPendingHeadTeller();
        saldo.setRequestId(request.getRequestId());
        saldo.setBranchId(request.getBranchId());
        saldo.setPeriod(LocalDate.parse(request.getPeriod()));
        String saldoType = getSaldoType(tellerExchange.getType());
        String trxHTSaldotransactionId = generateTransactionId(request.getBranchId(),saldoType, LocalDate.parse(request.getPeriod()), ADD_HT);
        saldo.setTransactionId(trxHTSaldotransactionId);
        saldo.setType(saldoType);
        saldo.setStatus(TrxStatus.SUCCESS.getValue());
        saldo.setInputer(trxPendingHeadTeller.getInputer());
        saldo.setTellerId(trxPendingHeadTeller.getTellerId());
        saldo.setTotalAmount(getAmount(balanceModel.getBalance(), trxPendingHeadTeller.getTotalAmount(), tellerExchange.getType()));
        saldo.setBalance(getAmount(balanceModel.getBalance(), trxPendingHeadTeller.getTotalAmount(), tellerExchange.getType()));
        saldo.setCreateDateTime(LocalDateTime.now());
        saldo.setUpdateDateTime(LocalDateTime.now());
        TrxHTAmountDetail htAmountDetail = new TrxHTAmountDetail();
        TrxHTAmountDetail htAmountDetailSaldo = new TrxHTAmountDetail();
        htAmountDetail.setTransactionId(trxPendingHeadTeller.getTransactionId());
        htAmountDetailSaldo.setTransactionId(saldo.getTransactionId());
        getAmountDetails(htAmountDetail,htAmountDetailSaldo,tellerExchange.getAmountDetail(),balanceModel, tellerExchange.getType());
        trxPendingHeadTeller.setAmountDetail(gson.toJson(htAmountDetail));
        saldo.setAmountDetail(gson.toJson(htAmountDetailSaldo));
        trxPendingHeadTellerRepository.save(trxPendingHeadTeller);
        trxPendingHeadTellerRepository.save(saldo);
    }

    public String getType(String type){
        if (TEV2HT.getCode().equals(type)){
            return TrxType.V2HT.getCode();
        }else if (TrxType.TEHT2T.getCode().equals(type)){
            return TrxType.HT2T.getCode();
        }else if (TrxType.TET2HT.getCode().equals(type)){
            return TrxType.T2HT.getCode();
        }
        return null;
    }
    public void getAmountDetails(TrxHTAmountDetail trxAmountDetail, TrxHTAmountDetail trxAmountDetailSaldo,String amountDetails, BalanceModel balanceModel,String type) {
        Gson gson = new Gson();
        List<AmountDetail> detailAmount = gson.fromJson(amountDetails, new TypeToken<List<AmountDetail>>() {
        }.getType());
        for (AmountDetail amountDetail : detailAmount) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    trxAmountDetail.setC50Amount(amountDetail.getTotal());
                    trxAmountDetail.setC50Count(amountDetail.getCount());
                    trxAmountDetailSaldo.setC50Amount(getAmount(balanceModel.getC50Amount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setC50Count(getCount(balanceModel.getC50Count(),amountDetail.getCount(),type));
                    break;
                case COIN_100:
                    trxAmountDetail.setC100Amount(amountDetail.getTotal());
                    trxAmountDetail.setC100Count(amountDetail.getCount());
                    trxAmountDetailSaldo.setC100Amount(getAmount(balanceModel.getC100Amount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setC100Count(getCount(balanceModel.getC100Count(),amountDetail.getCount(),type));
                    break;
                case COIN_200:
                    trxAmountDetail.setC200Amount(amountDetail.getTotal());
                    trxAmountDetail.setC200Count(amountDetail.getCount());
                    trxAmountDetailSaldo.setC200Amount(getAmount(balanceModel.getC200Amount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setC200Count(getCount(balanceModel.getC200Count(),amountDetail.getCount(),type));
                    break;
                case COIN_500:
                    trxAmountDetail.setC500Amount(amountDetail.getTotal());
                    trxAmountDetail.setC500Count(amountDetail.getCount());
                    trxAmountDetailSaldo.setC500Amount(getAmount(balanceModel.getC500Amount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setC500Count(getCount(balanceModel.getC500Count(),amountDetail.getCount(),type));
                    break;
                case COIN_1K:
                    trxAmountDetail.setC1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setC1KCount(amountDetail.getCount());
                    trxAmountDetailSaldo.setC1KAmount(getAmount(balanceModel.getC1KAmount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setC1KCount(getCount(balanceModel.getC1KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_1K:
                    trxAmountDetail.setP1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP1KCount(amountDetail.getCount());
                    trxAmountDetailSaldo.setP1KAmount(getAmount(balanceModel.getP1KAmount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setP1KCount(getCount(balanceModel.getP1KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_2K:
                    trxAmountDetail.setP2KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP2KCount(amountDetail.getCount());
                    trxAmountDetailSaldo.setP2KAmount(getAmount(balanceModel.getP2KAmount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setP2KCount(getCount(balanceModel.getP2KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_5K:
                    trxAmountDetail.setP5KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP5KCount(amountDetail.getCount());
                    trxAmountDetailSaldo.setP5KAmount(getAmount(balanceModel.getP5KAmount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setP5KCount(getCount(balanceModel.getP5KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_10K:
                    trxAmountDetail.setP10KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP10KCount(amountDetail.getCount());
                    trxAmountDetailSaldo.setP10KAmount(getAmount(balanceModel.getP10KAmount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setP10KCount(getCount(balanceModel.getP10KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_20K:
                    trxAmountDetail.setP20KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP20KCount(amountDetail.getCount());
                    trxAmountDetailSaldo.setP20KAmount(getAmount(balanceModel.getP20KAmount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setP20KCount(getCount(balanceModel.getP20KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_50K:
                    trxAmountDetail.setP50KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP50KCount(amountDetail.getCount());
                    trxAmountDetailSaldo.setP50KAmount(getAmount(balanceModel.getP50KAmount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setP50KCount(getCount(balanceModel.getP50KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_75K:
                    trxAmountDetail.setP75KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP75KCount(amountDetail.getCount());
                    trxAmountDetailSaldo.setP75KAmount(getAmount(balanceModel.getP75KAmount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setP75KCount(getCount(balanceModel.getP75KCount(),amountDetail.getCount(),type));
                    break;
                case PAPER_100K:
                    trxAmountDetail.setP100KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP100KCount(amountDetail.getCount());
                    trxAmountDetailSaldo.setP100KAmount(getAmount(balanceModel.getP100KAmount(),amountDetail.getTotal(),type));
                    trxAmountDetailSaldo.setP100KCount(getCount(balanceModel.getP100KCount(),amountDetail.getCount(),type));
                    break;
            }
        }
    }
    public CommonResponse<TellerExchangePendingHT2VModel> pendingV2HT(Profile nik,String type, String branchId){
        CommonResponse<TellerExchangePendingHT2VModel> response = new CommonResponse<>();
        response.setType(GET_TELLER_EXCHANGE_PENDING_VAULT);
        TellerExchangePendingHT2VModel tellerExchangePendingHT2VModel = new TellerExchangePendingHT2VModel();
        Gson gson = new Gson();
        LocalDate period = LocalDate.now();
        BalanceModel balanceModel = new BalanceModel();
        Cabang cabang = cabangRepository.findByCabangId(branchId);
        String vaultType = getTypeVault(type);
        Set<String> statusTellerExchange = new HashSet<>(Set.of(TrxStatus.PENDING.getValue(), TrxStatus.SUBMIT.getValue()));
        if (TEV2HT.getCode().equals(vaultType)){
            statusTellerExchange.add(TrxStatus.SUCCESS.getValue());
        }
        TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findTopByPeriodAndBranchIdAndTypeAndStatusInOrderByCreateDateTimeDesc(period, cabang.getCabangId(), vaultType, statusTellerExchange);
        tellerExchangePendingHT2VModel.setBranchId(cabang.getCabangId());
        tellerExchangePendingHT2VModel.setBranchName(cabang.getCabangDesc());
        tellerExchangePendingHT2VModel.setPeriod(period.toString());
        if (vault != null){
            TrxHTAmountDetail vaultDetail = new TrxHTAmountDetail();
            if (TEV2HT.getCode().equals(vaultType)){
                getAmountDetailPendingTEV2HT(vaultDetail,vault.getAmountDetail());
                TrxKasBesar lastBalance = trxKasBesarRepository.findFirstByPeriodGreaterThanEqualAndBranchIdAndTrxTypeInAndCreateDateTimeLessThanAndStatusInOrderByCreateDateTimeDesc(period.minusDays(14), cabang.getCabangId(), Set.of(TrxType.SAW.getCode(),TrxType.SAK.getCode(), TrxType.CC.getCode(),TrxType.SA.getCode(), TrxType.CASHOPNAME.getCode()),vault.getCreateDateTime(),Set.of(TrxStatus.SUCCESS.getValue(),TrxStatus.APPROVED.getValue()));
                BranchBalance branchBalance = branchBalanceRepository.findByBranchId(cabang.getCabangId());
                if (lastBalance!=null){
                    TrxAmountDetail detail = trxAmountDetailRepository.findByTransactionId(lastBalance.getTransactionId());
                    balanceModel.setBalanceModel(detail);
                    tellerExchangePendingHT2VModel.setTotalBeginBalance(lastBalance.getBalanceAmount());
                }else if (branchBalance != null){
                    balanceModel.setBalanceModel(branchBalance);
                    tellerExchangePendingHT2VModel.setTotalBeginBalance(branchBalance.getTotal());
                }else {
                    tellerExchangePendingHT2VModel.setTotalBeginBalance(balanceModel.getBalance());
                }
            } else if (vaultType.equals(TrxType.TEHT2V.getCode())) {
                vaultDetail = gson.fromJson(vault.getAmountDetail(), new TypeToken<TrxHTAmountDetail>() {
                }.getType());
                TrxHeadTeller lastBalance = trxHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeInAndStatusOrderByCreateDateTimeDesc(LocalDate.now(), cabang.getCabangId(), Set.of(TrxType.SKHT.getCode(), TrxType.SA.getCode()), TrxStatus.SUCCESS.getValue());
                HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(cabang.getCabangId());
                if (lastBalance != null) {
                    TrxHTAmountDetail detail = trxHTAmountDetailRepository.findByTransactionId(lastBalance.getTransactionId());
                    balanceModel.setBalanceModel(detail);
                    tellerExchangePendingHT2VModel.setTotalBeginBalance(lastBalance.getBalance());
                } else if (headTellerBalance != null) {
                    balanceModel.setBalanceModel(headTellerBalance);
                    tellerExchangePendingHT2VModel.setTotalBeginBalance(headTellerBalance.getTotalAmount());
                } else {
                    tellerExchangePendingHT2VModel.setTotalBeginBalance(balanceModel.getBalance());
                }
            }
            tellerExchangePendingHT2VModel.setRequestId(vault.getRequestId());
            tellerExchangePendingHT2VModel.setTransactionId(vault.getTransactionId());
            tellerExchangePendingHT2VModel.setStatus(vault.getStatus());
            tellerExchangePendingHT2VModel.setFromName(vault.getFromName());
            tellerExchangePendingHT2VModel.setInputerNik(vault.getInputerNik());
            tellerExchangePendingHT2VModel.setInputerName(getOfficerName(vault.getInputerNik(), branchId));
            tellerExchangePendingHT2VModel.setVerificationNik(vault.getVerificationNik());
            tellerExchangePendingHT2VModel.setVerificationName(getOfficerName(vault.getVerificationNik(), branchId));
            tellerExchangePendingHT2VModel.setToName(vault.getToName());
            tellerExchangePendingHT2VModel.setRequestFlag(vault.isRequestFlag());
            tellerExchangePendingHT2VModel.setDepositFlag(vault.isDepositFlag());
            tellerExchangePendingHT2VModel.setTotalAmount(vault.getTotalAmount());
            tellerExchangePendingHT2VModel.setTotalRemainingBalance(tellerExchangePendingHT2VModel.getTotalBeginBalance() - tellerExchangePendingHT2VModel.getTotalAmount());
            tellerExchangePendingHT2VModel.setAmountDetails(getTEHT2VDetail(balanceModel,vaultDetail));
        }
        response.setData(tellerExchangePendingHT2VModel);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        return response;
    }
        public List<HeadTellerAmountDetail> getTEHT2VDetail(BalanceModel trxAmountDetail, TrxHTAmountDetail vault){
            Gson gson = new Gson();
            AmountConfig amountConfig = new AmountConfig();
            List<HeadTellerAmountDetail> headTellerAmountDetail= gson.fromJson(amountConfig.amount(), new TypeToken<List<HeadTellerAmountDetail>>() {
            }.getType());
            for (HeadTellerAmountDetail amountDetail : headTellerAmountDetail) {
                switch (amountDetail.getId()) {
                    case COIN_50:
                        amountDetail.setAmountCount(vault.getC50Count());
                        amountDetail.setAmountTotal(vault.getC50Amount());
                        amountDetail.setBeginBalance(trxAmountDetail.getC50Amount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case COIN_100:
                        amountDetail.setAmountCount(vault.getC100Count());
                        amountDetail.setAmountTotal(vault.getC100Amount());
                        amountDetail.setBeginBalance(trxAmountDetail.getC100Amount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case COIN_200:
                        amountDetail.setAmountCount(vault.getC200Count());
                        amountDetail.setAmountTotal(vault.getC200Amount());
                        amountDetail.setBeginBalance(trxAmountDetail.getC200Amount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case COIN_500:
                        amountDetail.setAmountCount(vault.getC500Count());
                        amountDetail.setAmountTotal(vault.getC500Amount());
                        amountDetail.setBeginBalance(trxAmountDetail.getC500Amount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case COIN_1K:
                        amountDetail.setAmountCount(vault.getC1KCount());
                        amountDetail.setAmountTotal(vault.getC1KAmount());
                        amountDetail.setBeginBalance(trxAmountDetail.getC1KAmount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case PAPER_1K:
                        amountDetail.setAmountCount(vault.getP1KCount());
                        amountDetail.setAmountTotal(vault.getP1KAmount());
                        amountDetail.setBeginBalance(trxAmountDetail.getP1KAmount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case PAPER_2K:
                        amountDetail.setAmountCount(vault.getP2KCount());
                        amountDetail.setAmountTotal(vault.getP2KAmount());
                        amountDetail.setBeginBalance(trxAmountDetail.getP2KAmount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case PAPER_5K:
                        amountDetail.setAmountCount(vault.getP5KCount());
                        amountDetail.setAmountTotal(vault.getP5KAmount());
                        amountDetail.setBeginBalance(trxAmountDetail.getP5KAmount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case PAPER_10K:
                        amountDetail.setAmountCount(vault.getP10KCount());
                        amountDetail.setAmountTotal(vault.getP10KAmount());
                        amountDetail.setBeginBalance(trxAmountDetail.getP10KAmount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case PAPER_20K:
                        amountDetail.setAmountCount(vault.getP20KCount());
                        amountDetail.setAmountTotal(vault.getP20KAmount());
                        amountDetail.setBeginBalance(trxAmountDetail.getP20KAmount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case PAPER_50K:
                        amountDetail.setAmountCount(vault.getP50KCount());
                        amountDetail.setAmountTotal(vault.getP50KAmount());
                        amountDetail.setBeginBalance(trxAmountDetail.getP50KAmount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case PAPER_75K:
                        amountDetail.setAmountCount(vault.getP75KCount());
                        amountDetail.setAmountTotal(vault.getP75KAmount());
                        amountDetail.setBeginBalance(trxAmountDetail.getP75KAmount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                    case PAPER_100K:
                        amountDetail.setAmountCount(vault.getP100KCount());
                        amountDetail.setAmountTotal(vault.getP100KAmount());
                        amountDetail.setBeginBalance(trxAmountDetail.getP100KAmount());
                        amountDetail.setRemainingBalance(amountDetail.getBeginBalance()-amountDetail.getAmountTotal());
                        break;
                }
            }
            return headTellerAmountDetail;
        }
    public void getAmountDetailPendingTEV2HT(TrxHTAmountDetail trxAmountDetail,String amountDetails) {
        Gson gson = new Gson();
        List<AmountDetail> detailAmount = gson.fromJson(amountDetails, new TypeToken<List<AmountDetail>>() {
        }.getType());
        for (AmountDetail amountDetail : detailAmount) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    trxAmountDetail.setC50Amount(amountDetail.getTotal());
                    trxAmountDetail.setC50Count(amountDetail.getCount());
                    break;
                case COIN_100:
                    trxAmountDetail.setC100Amount(amountDetail.getTotal());
                    trxAmountDetail.setC100Count(amountDetail.getCount());
                    break;
                case COIN_200:
                    trxAmountDetail.setC200Amount(amountDetail.getTotal());
                    trxAmountDetail.setC200Count(amountDetail.getCount());
                    break;
                case COIN_500:
                    trxAmountDetail.setC500Amount(amountDetail.getTotal());
                    trxAmountDetail.setC500Count(amountDetail.getCount());
                    break;
                case COIN_1K:
                    trxAmountDetail.setC1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setC1KCount(amountDetail.getCount());
                    break;
                case PAPER_1K:
                    trxAmountDetail.setP1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP1KCount(amountDetail.getCount());
                    break;
                case PAPER_2K:
                    trxAmountDetail.setP2KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP2KCount(amountDetail.getCount());
                    break;
                case PAPER_5K:
                    trxAmountDetail.setP5KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP5KCount(amountDetail.getCount());
                    break;
                case PAPER_10K:
                    trxAmountDetail.setP10KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP10KCount(amountDetail.getCount());
                    break;
                case PAPER_20K:
                    trxAmountDetail.setP20KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP20KCount(amountDetail.getCount());
                    break;
                case PAPER_50K:
                    trxAmountDetail.setP50KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP50KCount(amountDetail.getCount());
                    break;
                case PAPER_75K:
                    trxAmountDetail.setP75KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP75KCount(amountDetail.getCount());
                    break;
                case PAPER_100K:
                    trxAmountDetail.setP100KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP100KCount(amountDetail.getCount());
                    break;
            }
        }
    }
    public Double getAmount(Double firstValue,Double lastValue,String type){
        Double amount = 0.0;
        if (type.equals(TrxType.TEHT2T.getCode())){
            amount = firstValue - lastValue;
        } else if (type.equals(TrxType.TET2HT.getCode())){
            amount = firstValue + lastValue;
        }
        return amount;
    }

    public Integer getCount(Integer firstValue,Integer lastValue,String type){
        Integer amount = 0;
        if (type.equals(TrxType.TEHT2T.getCode())){
            amount = firstValue - lastValue;
        }
        return amount;
    }
    public String getSaldoType(String type){
        if (type.equals(TrxType.TEHT2T.getCode())){
            return TrxType.SAHT2T.getCode();
        }else if (type.equals(TrxType.TET2HT.getCode())){
            return TrxType.SAT2HT.getCode();
        }
        return null;
    }

    public CommonResponse<TellerExchangePendingKHTModel> getPendingKHT(Profile nik, String branchId){
        CommonResponse<TellerExchangePendingKHTModel> response = new CommonResponse<>();
        response.setType(GET_TELLER_EXCHANGE_PENDING_KHT);
        TellerExchangePendingKHTModel tellerExchangePendingKHTModel = new TellerExchangePendingKHTModel();
        LocalDate period = LocalDate.now();
        TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findTopByPeriodAndBranchIdAndTypeAndStatusInOrderByCreateDateTimeDesc(period, branchId, TEV2HT.getCode(), Set.of(TrxStatus.SUBMIT.getValue(),TrxStatus.PENDING.getValue()));
        tellerExchangePendingKHTModel.setBranchId(branchId);
        tellerExchangePendingKHTModel.setPeriod(period.toString());
        if (vault !=null) {
            BranchBalance balance = branchBalanceRepository.findByBranchId(branchId);
            tellerExchangePendingKHTModel.setTransactionId(vault.getTransactionId());
            tellerExchangePendingKHTModel.setStatus(vault.getStatus());
            tellerExchangePendingKHTModel.setTotalBeginBalance(balance.getTotal());
            tellerExchangePendingKHTModel.setTotalInBalance(vault.getTotalAmount());
            tellerExchangePendingKHTModel.setTotalRemainingBalance(tellerExchangePendingKHTModel.getTotalBeginBalance() + tellerExchangePendingKHTModel.getTotalInBalance());
            TrxHTAmountDetail vaultDetail = new TrxHTAmountDetail();
            BalanceModel balanceModel = new BalanceModel();
            balanceModel.setBalanceModel(balance);
            getAmountDetailPendingTEV2HT(vaultDetail, vault.getAmountDetail());
            tellerExchangePendingKHTModel.setAmountDetails(getTEHT2VDetail(balanceModel, vaultDetail));
        }
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        response.setData(tellerExchangePendingKHTModel);
        return response;
    }
    public String getTypeVault(String type){
        String vaultType = null;
        if (type.equals(TrxType.V2HT.getCode())){
            vaultType = TEV2HT.getCode();
        }else if (type.equals(TrxType.HT2V.getCode())){
            vaultType = TrxType.TEHT2V.getCode();
        }
        return vaultType;
    }

    public CommonResponse<TellerExchangeListPendingModel> getListPending(Profile nik, String branch ,String type){
        CommonResponse<TellerExchangeListPendingModel> response = new CommonResponse<>();
        response.setType(GET_LIST_TELLER_EXCHANGE_BALANCE_PENDING);
        TellerExchangeListPendingModel tellerExchangeListPendingModel = new TellerExchangeListPendingModel();
        Cabang cabang = cabangRepository.findByCabangId(branch);
        LocalDate period = LocalDate.now();
        tellerExchangeListPendingModel.setBranchId(branch);
        tellerExchangeListPendingModel.setBranchName(cabang.getCabangDesc());
        tellerExchangeListPendingModel.setPeriod(period.toString());
        List<TellerExchangeListPendingDetailModel> getListPending = new ArrayList<>();
        try {
            BranchBalance balance = branchBalanceRepository.findByBranchId(branch);
            checkSaldoVaultTellerExchangeVault(period, branch, type, balance);
            TellerExchangeListPendingDetailModel saldoAwal = new TellerExchangeListPendingDetailModel(null, null, TELLER_EXCHANGE_SALDO_VAULT, null, balance.getTotal(), null);
            saldoAwal.setSaldoAwal(balance);
            getListPending.add(saldoAwal);
            List<TellerExchangeListPendingDetailModel> listTransactionPending = trxTellerExchangeRepository.getDetailListPendingTellerExchange(period, cabang.getCabangId(), TrxStatus.PENDING.getValue(), type);
            if (listTransactionPending != null){
                BalanceModel balanceModel = new BalanceModel();
                balanceModel.setBalanceModel(balance);
                List<TellerExchangeListPendingDetailModel> list = new ArrayList<>();
                for (TellerExchangeListPendingDetailModel detailModel : listTransactionPending) {
                    detailModel.setTellerName(getOfficerName(detailModel.getTellerNIK(), branch));
                    balanceModel.setBalance(getAmount(balanceModel.getBalance(),detailModel.getTotalAmount(),type));
                    TellerExchangeListPendingDetailModel saldoVault = new TellerExchangeListPendingDetailModel(null, null, TELLER_EXCHANGE_SALDO_VAULT,  null, balanceModel.getBalance(), null);
                    saldoVault.setTellerName(getOfficerName(detailModel.getTellerNIK(), branch));
                    list.add(detailModel);
                    balanceModel.setP100KAmount(getAmount(balanceModel.getP100KAmount(),detailModel.getP100KAmount(),type));
                    balanceModel.setP75KAmount(getAmount(balanceModel.getP75KAmount(),detailModel.getP75KAmount(),type));
                    balanceModel.setP50KAmount(getAmount(balanceModel.getP50KAmount(),detailModel.getP50KAmount(),type));
                    balanceModel.setP20KAmount(getAmount(balanceModel.getP20KAmount(),detailModel.getP20KAmount(),type));
                    balanceModel.setP10KAmount(getAmount(balanceModel.getP10KAmount(),detailModel.getP10KAmount(),type));
                    balanceModel.setP5KAmount(getAmount(balanceModel.getP5KAmount(),detailModel.getP5KAmount(),type));
                    balanceModel.setP2KAmount(getAmount(balanceModel.getP2KAmount(),detailModel.getP2KAmount(),type));
                    balanceModel.setP1KAmount(getAmount(balanceModel.getP1KAmount(),detailModel.getP1KAmount(),type));
                    balanceModel.setC1KAmount(getAmount(balanceModel.getC1KAmount(),detailModel.getC1KAmount(),type));
                    balanceModel.setC500Amount(getAmount(balanceModel.getC500Amount(),detailModel.getC500Amount(),type));
                    balanceModel.setC200Amount(getAmount(balanceModel.getC200Amount(),detailModel.getC200Amount(),type));
                    balanceModel.setC100Amount(getAmount(balanceModel.getC100Amount(),detailModel.getC100Amount(),type));
                    balanceModel.setC50Amount(getAmount(balanceModel.getC50Amount(),detailModel.getC50Amount(),type));
                    saldoVault.setSaldoVault(balanceModel);
                    list.add(saldoVault);
                }
                getListPending.addAll(list);
            }
            tellerExchangeListPendingModel.setTransactions(getListPending);
            response.setStatus(TrxStatus.SUCCESS.getCode());
            response.setStatusDesc(TrxStatus.SUCCESS.getValue());
            response.setData(tellerExchangeListPendingModel);

        }catch (Exception e){
            logger.error("Failed to get list teller exchange pending balance " + e.getMessage());
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
            return response;
        }
        return response;
    }

    public void checkSaldoVaultTellerExchangeVault(LocalDate period, String branch, String type, BranchBalance balance){
        TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findTopByPeriodAndBranchIdAndTypeAndStatusInOrderByCreateDateTimeDesc(period, branch, TEV2HT.getCode(), Set.of(TrxStatus.PENDING.getValue(),TrxStatus.SUBMIT.getValue()));
        if (vault != null){
            TrxHTAmountDetail detail = new TrxHTAmountDetail();
            getAmountDetailPendingTEV2HT(detail,vault.getAmountDetail());
            balance.setP100KAmount(getAmount(balance.getP100KAmount(),detail.getP100KAmount(),type));
            balance.setP75KAmount(getAmount(balance.getP75KAmount(),detail.getP75KAmount(),type));
            balance.setP50KAmount(getAmount(balance.getP50KAmount(),detail.getP50KAmount(),type));
            balance.setP20KAmount(getAmount(balance.getP20KAmount(),detail.getP20KAmount(),type));
            balance.setP10KAmount(getAmount(balance.getP10KAmount(),detail.getP10KAmount(),type));
            balance.setP5KAmount(getAmount(balance.getP5KAmount(),detail.getP5KAmount(),type));
            balance.setP2KAmount(getAmount(balance.getP2KAmount(),detail.getP2KAmount(),type));
            balance.setP1KAmount(getAmount(balance.getP1KAmount(),detail.getP1KAmount(),type));
            balance.setC1KAmount(getAmount(balance.getC1KAmount(),detail.getC1KAmount(),type));
            balance.setC500Amount(getAmount(balance.getC500Amount(),detail.getC500Amount(),type));
            balance.setC200Amount(getAmount(balance.getC200Amount(),detail.getC200Amount(),type));
            balance.setC100Amount(getAmount(balance.getC100Amount(),detail.getC100Amount(),type));
            balance.setC50Amount(getAmount(balance.getC50Amount(),detail.getC50Amount(),type));
        }

    }

    public CommonResponse<TellerExchangeCancelResponse> cancelTellerExchange(String nik, TellerExchangeCancelRequest request){
        Gson gson = new Gson();
        CommonResponse<TellerExchangeCancelResponse> response = new CommonResponse<>();
        response.setType(CANCEL_TELLER_EXCHANGE);
        TellerExchangeCancelResponse tellerExchangeCancelResponse = new TellerExchangeCancelResponse();
        tellerExchangeCancelResponse.setRequestId(request.getRequestId());
        TrxTellerExchange tellerExchange = trxTellerExchangeRepository.findByTransactionId(request.getCancelTransactionId());
        try {
            if (tellerExchange != null && tellerExchange.getStatus().equals(TrxStatus.PENDING.getValue())) {
                tellerExchange.setStatus(TrxStatus.REJECTED.getValue());
                tellerExchange.setVerificationNik(null);
                trxTellerExchangeRepository.save(tellerExchange);
                LocalDateTime now = LocalDateTime.now();
                TrxAuditTrail auditTrail = new TrxAuditTrail();
                AuditTrailAdditionalInfoModel additionalInfo = new AuditTrailAdditionalInfoModel();
                additionalInfo.setReason(encodeIfnotNull(request.getReason()));
                additionalInfo.setTimestamp(now);
                auditTrail.setTransactionId(tellerExchange.getTransactionId());
                auditTrail.setNik(nik);
                auditTrail.setAction(Action.CANCEL_TELLER_EXCHANGE.getValue());
                auditTrail.setCreateDateTime(now);
                auditTrail.setAdditionalInfo(gson.toJson(additionalInfo));
                trxAuditTrailRepository.save(auditTrail);
            }
            tellerExchangeCancelResponse.setStatus(TrxStatus.SUCCESS.getCode());
            tellerExchangeCancelResponse.setStatusDesc(TrxStatus.SUCCESS.getValue());
            response.setData(tellerExchangeCancelResponse);
            return response;
        } catch (Exception e) {
            tellerExchangeCancelResponse.setStatus(TrxStatus.FAILED.getCode());
            tellerExchangeCancelResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setData(tellerExchangeCancelResponse);
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
            return response;
        }

    }
    private static String encodeIfnotNull(String data) {
        if (data != null) {
            return StringEscapeUtils.escapeHtml4(data);
        }
        return null;
    }
    public boolean existInInterval(String nik, int interval, String branch, String type) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusSeconds(interval);
        return TEV2HT.getCode().equals(type) ? trxTellerExchangeVaultRepository.checkRequestInterval(startDate, endDate, branch, nik, type) > 0 : trxTellerExchangeRepository.checkRequestInterval(startDate, endDate, branch, nik, type) > 0;
    }
    public boolean checkBalanceHeadTeller(TrxTellerExchange tellerExchange){
        HeadTellerBalance htBalance = headTellerBalanceRepository.findByBranchId(tellerExchange.getBranchId());
        Gson gson = new Gson();
        List<AmountDetail> details = gson.fromJson(tellerExchange.getAmountDetail(), new TypeToken<List<AmountDetail>>() {
        }.getType());
        for (AmountDetail amountDetail : details) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    if (htBalance.getC50Amount() - amountDetail.getTotal() < 0 || htBalance.getC50Count() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case COIN_100:
                    if (htBalance.getC100Amount() - amountDetail.getTotal() < 0 || htBalance.getC100Count() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case COIN_200:
                    if (htBalance.getC200Amount() - amountDetail.getTotal() < 0 || htBalance.getC200Count() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case COIN_500:
                    if (htBalance.getC500Amount() - amountDetail.getTotal() < 0 || htBalance.getC500Count() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case COIN_1K:
                    if (htBalance.getC1KAmount() - amountDetail.getTotal() < 0 || htBalance.getC1KCount() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case PAPER_1K:
                    if (htBalance.getP1KAmount() - amountDetail.getTotal() < 0 || htBalance.getP1KCount() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case PAPER_2K:
                    if (htBalance.getP2KAmount() - amountDetail.getTotal() < 0 || htBalance.getP2KCount() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case PAPER_5K:
                    if (htBalance.getP5KAmount() - amountDetail.getTotal() < 0 || htBalance.getP5KCount() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case PAPER_10K:
                    if (htBalance.getP10KAmount() - amountDetail.getTotal() < 0 || htBalance.getP10KCount() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case PAPER_20K:
                    if (htBalance.getP20KAmount() - amountDetail.getTotal() < 0 || htBalance.getP20KCount() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case PAPER_50K:
                    if (htBalance.getP50KAmount() - amountDetail.getTotal() < 0 || htBalance.getP50KCount() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case PAPER_75K:
                    if (htBalance.getP75KAmount() - amountDetail.getTotal() < 0 || htBalance.getP75KCount() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
                case PAPER_100K:
                    if (htBalance.getP100KAmount() - amountDetail.getTotal() < 0 || htBalance.getP100KCount() - amountDetail.getCount() < 0) {
                        return false;
                    }
                    break;
            }
        }
        return true;
    }

    public boolean validateActiveTellerExchangeVaultToHT(String branchId) {
        TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findTopByPeriodAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(LocalDate.now(), branchId, TEV2HT.getCode(), TrxStatus.SUBMIT.getValue());
        if (vault != null) {
            return false;
        }
        return true;
    }

    private boolean validateRequestOfFund(String type, String sourceOfFund) {
        if (TrxType.TEHT2T.getCode().equals(type)) {
            switch (sourceOfFund) {
                case TE_SOURCE_OF_FUND_HT_BALANCE:
                case TE_SOURCE_OF_FUND_BRANCH_BALANCE:
                    return true;
                default:
                    return false;
            }
        }
        return true;
    }

    private String getOfficerName(String nik, String branchId){
        String name = officerNonProsperaRepository.getOfficerNameByNIK(nik);
        if (StringUtils.isEmpty(name)){
            name = officerImpl.getOfficerNameByNik(nik, branchId);
        }
        return name;
    }

}


