package com.btpns.fin.service;

import com.btpns.fin.constant.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.HeadTellerApprovalReq;
import com.btpns.fin.model.request.KoreksiKasBesarRequest;
import com.btpns.fin.model.response.InputTransactionResponse;
import com.btpns.fin.model.response.KoreksiKasBesarResponse;
import com.btpns.fin.repository.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.btpns.fin.constant.Action.*;
import static com.btpns.fin.constant.CommonConstant.*;
import static com.btpns.fin.constant.ResponseStatus.*;
import static com.btpns.fin.constant.TrxStatus.*;
import static com.btpns.fin.constant.TrxStatus.PENDING;
import static com.btpns.fin.constant.TrxStatus.SUCCESS;
import static com.btpns.fin.constant.TrxType.*;
import static com.btpns.fin.helper.CommonHelper.enrichPICName;
import static com.btpns.fin.helper.CommonHelper.validateVerification;

@Service
public class KoreksiService {
    private static final Logger logger = LoggerFactory.getLogger(KoreksiService.class);

    @Autowired
    TrxKasBesarRepository trxKasBesarRepository;

    @Autowired
    TrxHeadTellerRepository trxHeadTellerRepository;

    @Autowired
    HeadTellerBalanceRepository headTellerBalanceRepository;

    @Autowired
    TrxHTAmountDetailRepository trxHTAmountDetailRepository;

    @Autowired
    CabangRepository cabangRepository;

    @Autowired
    TrxAuditTrailRepository trxAuditTrailRepository;

    @Autowired
    BranchBalanceRepository branchBalanceRepository;

    @Autowired
    TrxAmountDetailRepository trxAmountDetailRepository;

    @Autowired
    OfficerRepository officerRepository;
    @Autowired
    TrxTellerExchangeVaultRepository trxTellerExchangeVaultRepository;
    @Autowired
    OfficerNonProsperaRepository officerNonProsperaRepository;
    @Autowired
    OfficerImpl officerImpl;

    public KoreksiTransactionModel getKoreksiTransactionKasBesar(String branchId, String status, String inputerNik) {

        LocalDate period = LocalDate.now();
        List<KoreksiTransactionDetailModel> trxList = trxKasBesarRepository.getListKoreksiTransactionKasBesar(
                period, branchId, status, inputerNik);

        Cabang cabang = cabangRepository.findByCabangId(branchId);
        
        KoreksiTransactionModel result = new KoreksiTransactionModel(period.toString(), branchId, cabang.getCabangDesc(), KAS_BESAR, trxList);
                
        return result;
    }

    @Transactional
    public CommonResponse<KoreksiKasBesarResponse> koreksiKasBesar(KoreksiKasBesarRequest request, String inputerNik) {
        CommonResponse<KoreksiKasBesarResponse> response = new CommonResponse<>();
        response.setType(KOREKSI_KAS_BESAR);
        KoreksiKasBesarResponse result = (KoreksiKasBesarResponse) new KoreksiKasBesarResponse().setRequestId(request.getRequestId());

        TrxKasBesar pendingReversal = isPendingReversal(request.getBranchId());

        if (pendingReversal != null) {
            result.setTransactionId(pendingReversal.getTransactionId()).setStatus(ResponseStatus.PENDING.getCode()).setStatusDesc(ResponseStatus.PENDING.getValue());
        } else {

            TrxKasBesar trxKasBesar = trxKasBesarRepository.findByTransactionId(request.getTransactionId());
            if (!checkTransactionHeadTeller(trxKasBesar)){
                result.setRequestId(request.getRequestId());
                result.setTransactionId(request.getTransactionId());
                result.setStatus(ResponseStatus.KOREKSI_HEAD_TELLER_FIRST.getCode());
                result.setStatusDesc(ResponseStatus.KOREKSI_HEAD_TELLER_FIRST.getValue());
                response.setStatus(ResponseStatus.KOREKSI_HEAD_TELLER_FIRST.getCode());
                response.setStatusDesc(ResponseStatus.KOREKSI_HEAD_TELLER_FIRST.getValue());
                response.setData(result);
                return response;
            }
            String type = null;
            String action = null;
            if (trxKasBesar.getTransactionId().startsWith(KHT.getCode())){
                type = RVKHT.getCode();
                action = REVERSAL_KHT.getValue();
            } else if (trxKasBesar.getTransactionId().startsWith(MHT.getCode())) {
                type = RVMHT.getCode();
                action = REVERSAL_MHT.getValue();
            } else if (trxKasBesar.getTransactionId().startsWith(CC.getCode())) {
                type = RVCC.getCode();
                action = REVERSAL_CC.getValue();
            }else if (trxKasBesar.getTransactionId().startsWith(SAW.getCode())) {
                type = RVSAW.getCode();
                action = REVERSAL_SAW.getValue();
            }else if (trxKasBesar.getTransactionId().startsWith(SAK.getCode())) {
                type = RVSAK.getCode();
                action = REVERSAL_SAK.getValue();
            }

            String transactionId = RV_DASH.getCode().concat(trxKasBesar.getTransactionId());
            TrxKasBesar checkReversal = trxKasBesarRepository.findByTransactionId(transactionId);
            if (checkReversal!=null){
                Integer sequence = trxKasBesarRepository.getReversalSequence(transactionId);
                transactionId = transactionId.concat("-".concat(String.valueOf(sequence+1)));
            }

            TrxKasBesar trxReversal = new TrxKasBesar(trxKasBesar);
            
            trxReversal.setTransactionId(transactionId).setPeriod(LocalDate.now()).setTrxType(type)
                    .setStatus(PENDING.getValue()).setReason(encodeIfnotNull(request.getNotes())).setRequestId(request.getRequestId());

            trxKasBesarRepository.save(trxReversal);
            
            TrxAmountDetail trxAmountDetail = new TrxAmountDetail(trxAmountDetailRepository.findByTransactionId(trxKasBesar.getTransactionId()));
            trxAmountDetail.setTransactionId(transactionId);
            trxAmountDetailRepository.save(trxAmountDetail);
            
            insertAuditTrail(inputerNik, action, trxReversal.getTransactionId(), trxReversal.getBranchId());

            result.setTransactionId(transactionId).setStatus(SUCCESS.getCode()).setStatusDesc(SUCCESS.getValue());
        }
        response.setData(result);
        return response;
    }
    
    public TrxKasBesar isPendingReversal(String branch) {
        return trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeInAndStatus(
                LocalDate.now(), branch, Set.of(RVKHT.getCode(), RVMHT.getCode(), SAW.getCode(), SAK.getCode(), KHT.getCode(), MHT.getCode(), CASHOPNAME.getCode(), CC.getCode(), RVCC.getCode(), RVSAK.getCode(), RVSAW.getCode()), PENDING.getValue());
    }


    public KoreksiTransactionModel getKoreksiTransactionHeadTeller(String branchId, String status, String inputerNik) {
        LocalDate period = LocalDate.now();
        List<KoreksiTransactionDetailModel> details = trxHeadTellerRepository.getListTransactionKoreksi(period,branchId,status,inputerNik);

        Cabang cabang = cabangRepository.findByCabangId(branchId);

        KoreksiTransactionModel result = new KoreksiTransactionModel(period.toString(), branchId, cabang.getCabangDesc(), HEAD_TELLER, details);
        return result;
    }

    @Transactional
    public CommonResponse<KoreksiKasBesarResponse> processReversalHeadTeller(KoreksiKasBesarRequest request, String inputerNik){
        CommonResponse<KoreksiKasBesarResponse> response = new CommonResponse<>();
        response.setType(KOREKSI_HEAD_TELLER);
        KoreksiKasBesarResponse koreksiKasBesarResponse = new KoreksiKasBesarResponse();
        String transactionId = RV.getCode().concat("-").concat(request.getTransactionId());
        koreksiKasBesarResponse.setTransactionId(transactionId);
        koreksiKasBesarResponse.setRequestId(request.getRequestId());
        LocalDate period = LocalDate.now();
        try {
            TransactionHeadTeller transactionHeadTeller = trxHeadTellerRepository.transactionHeadTeller(request.getTransactionId());
            CheckTransactionReversalModel transaction = checkMHTReversal(transactionHeadTeller.getTrxHeadTeller());
            if (transaction !=null ){
                koreksiKasBesarResponse.setTransactionId(transaction.getTransactionId());
                if (transaction.getStatus().equals(APPROVED.getValue())){
                    koreksiKasBesarResponse.setStatus(ResponseStatus.KOREKSI_MUST_MHT_FIRST.getCode());
                    koreksiKasBesarResponse.setStatusDesc(ResponseStatus.KOREKSI_MUST_MHT_FIRST.getValue());
                    response.setStatus(ResponseStatus.KOREKSI_MUST_MHT_FIRST.getCode());
                    response.setStatusDesc(ResponseStatus.KOREKSI_MUST_MHT_FIRST.getValue());
                }else if (transaction.getStatus().equals(PENDING.getValue())){
                    koreksiKasBesarResponse.setStatus(ResponseStatus.KOREKSI_FOUND_MHT_PENDING.getCode());
                    koreksiKasBesarResponse.setStatusDesc(ResponseStatus.KOREKSI_FOUND_MHT_PENDING.getValue());
                    response.setStatus(ResponseStatus.KOREKSI_FOUND_MHT_PENDING.getCode());
                    response.setStatusDesc(ResponseStatus.KOREKSI_FOUND_MHT_PENDING.getValue());
                }
                response.setData(koreksiKasBesarResponse);
                return response;
            }
            TrxHeadTeller ht2vPending = trxHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(period, request.getBranchId(), TrxType.HT2V.getCode(), PENDING.getValue());
            if (ht2vPending != null){
                koreksiKasBesarResponse.setStatus(ResponseStatus.KOREKSI_HT2V_PENDING.getCode());
                koreksiKasBesarResponse.setStatusDesc(ResponseStatus.KOREKSI_HT2V_PENDING.getValue());
                response.setStatus(ResponseStatus.KOREKSI_HT2V_PENDING.getCode());
                response.setStatusDesc(ResponseStatus.KOREKSI_HT2V_PENDING.getValue());
                response.setData(koreksiKasBesarResponse);
                return response;
            }
            HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(request.getBranchId());
            if (headTellerBalance.getTotalAmount().equals(0.0) && (transactionHeadTeller.getTrxHeadTeller().getType().equals(TrxType.V2HT.getCode()) || transactionHeadTeller.getTrxHeadTeller().getType().equals(TrxType.T2HT.getCode())) || transactionHeadTeller.getTrxHeadTeller().getStatus().equals(TrxStatus.REVERSAL.getValue())) {
                koreksiKasBesarResponse.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
                koreksiKasBesarResponse.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
                response.setData(koreksiKasBesarResponse);
                response.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
                response.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
                return response;
            }
            Double totalAmount = transactionHeadTeller.getTrxHTAmountDetail().getTotal();
            headTellerBalance.setC50Amount(getHTAmount(headTellerBalance.getC50Amount(), transactionHeadTeller.getTrxHTAmountDetail().getC50Amount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setC50Count(getHTCount(headTellerBalance.getC50Count(), transactionHeadTeller.getTrxHTAmountDetail().getC50Count(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setC100Amount(getHTAmount(headTellerBalance.getC100Amount(), transactionHeadTeller.getTrxHTAmountDetail().getC100Amount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setC100Count(getHTCount(headTellerBalance.getC100Count(), transactionHeadTeller.getTrxHTAmountDetail().getC100Count(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setC200Amount(getHTAmount(headTellerBalance.getC200Amount(), transactionHeadTeller.getTrxHTAmountDetail().getC200Amount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setC200Count(getHTCount(headTellerBalance.getC200Count(), transactionHeadTeller.getTrxHTAmountDetail().getC200Count(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setC500Amount(getHTAmount(headTellerBalance.getC500Amount(), transactionHeadTeller.getTrxHTAmountDetail().getC500Amount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setC500Count(getHTCount(headTellerBalance.getC500Count(), transactionHeadTeller.getTrxHTAmountDetail().getC500Count(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setC1KAmount(getHTAmount(headTellerBalance.getC1KAmount(), transactionHeadTeller.getTrxHTAmountDetail().getC1KAmount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setC1KCount(getHTCount(headTellerBalance.getC1KCount(), transactionHeadTeller.getTrxHTAmountDetail().getC1KCount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP1KAmount(getHTAmount(headTellerBalance.getP1KAmount(), transactionHeadTeller.getTrxHTAmountDetail().getP1KAmount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP1KCount(getHTCount(headTellerBalance.getP1KCount(), transactionHeadTeller.getTrxHTAmountDetail().getP1KCount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP2KAmount(getHTAmount(headTellerBalance.getP2KAmount(), transactionHeadTeller.getTrxHTAmountDetail().getP2KAmount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP2KCount(getHTCount(headTellerBalance.getP2KCount(), transactionHeadTeller.getTrxHTAmountDetail().getP2KCount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP5KAmount(getHTAmount(headTellerBalance.getP5KAmount(), transactionHeadTeller.getTrxHTAmountDetail().getP5KAmount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP5KCount(getHTCount(headTellerBalance.getP5KCount(), transactionHeadTeller.getTrxHTAmountDetail().getP5KCount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP10KAmount(getHTAmount(headTellerBalance.getP10KAmount(), transactionHeadTeller.getTrxHTAmountDetail().getP10KAmount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP10KCount(getHTCount(headTellerBalance.getP10KCount(), transactionHeadTeller.getTrxHTAmountDetail().getP10KCount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP20KAmount(getHTAmount(headTellerBalance.getP20KAmount(), transactionHeadTeller.getTrxHTAmountDetail().getP20KAmount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP20KCount(getHTCount(headTellerBalance.getP20KCount(), transactionHeadTeller.getTrxHTAmountDetail().getP20KCount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP50KAmount(getHTAmount(headTellerBalance.getP50KAmount(), transactionHeadTeller.getTrxHTAmountDetail().getP50KAmount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP50KCount(getHTCount(headTellerBalance.getP50KCount(), transactionHeadTeller.getTrxHTAmountDetail().getP50KCount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP75KAmount(getHTAmount(headTellerBalance.getP75KAmount(), transactionHeadTeller.getTrxHTAmountDetail().getP75KAmount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP75KCount(getHTCount(headTellerBalance.getP75KCount(), transactionHeadTeller.getTrxHTAmountDetail().getP75KCount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP100KAmount(getHTAmount(headTellerBalance.getP100KAmount(), transactionHeadTeller.getTrxHTAmountDetail().getP100KAmount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setP100KCount(getHTCount(headTellerBalance.getP100KCount(), transactionHeadTeller.getTrxHTAmountDetail().getP100KCount(), transactionHeadTeller.getTrxHeadTeller().getType()));
            headTellerBalance.setTotalAmount(getHTAmount(headTellerBalance.getTotalAmount(), totalAmount, transactionHeadTeller.getTrxHeadTeller().getType()));

            transactionHeadTeller.getTrxHeadTeller().setStatus(TrxStatus.REVERSAL.getValue());
            TrxHeadTeller reversalTransaction = new TrxHeadTeller();
            reversalTransaction.setTransactionId(transactionId);
            reversalTransaction.setPeriod(period);
            reversalTransaction.setStatus(SUCCESS.getValue());
            reversalTransaction.setInputer(inputerNik);
            reversalTransaction.setType(TrxType.RV.getCode().concat("-").concat(transactionHeadTeller.getTrxHeadTeller().getType()));
            reversalTransaction.setTotalAmount(transactionHeadTeller.getTrxHeadTeller().getTotalAmount());
            reversalTransaction.setBalance(headTellerBalance.getTotalAmount());
            reversalTransaction.setTellerId(transactionHeadTeller.getTrxHeadTeller().getTellerId());
            reversalTransaction.setBranchId(request.getBranchId());
            reversalTransaction.setReason(encodeIfnotNull(request.getNotes()));
            reversalTransaction.setCreateDateTime(LocalDateTime.now());
            reversalTransaction.setUpdateDateTime(LocalDateTime.now());
            PICModel pic = enrichPICName(reversalTransaction.getInputer(), reversalTransaction.getTellerId(), officerImpl, officerNonProsperaRepository);
            reversalTransaction.setInputerName(pic.getInputerName());
            reversalTransaction.setTellerName(pic.getTellerName());

            TrxHTAmountDetail reversalDetail = new TrxHTAmountDetail();
            reversalDetail.setTransactionId(reversalTransaction.getTransactionId());
            reversalDetail.setC50Amount(transactionHeadTeller.getTrxHTAmountDetail().getC50Amount());
            reversalDetail.setC50Count(transactionHeadTeller.getTrxHTAmountDetail().getC50Count());
            reversalDetail.setC100Amount(transactionHeadTeller.getTrxHTAmountDetail().getC100Amount());
            reversalDetail.setC100Count(transactionHeadTeller.getTrxHTAmountDetail().getC100Count());
            reversalDetail.setC200Amount(transactionHeadTeller.getTrxHTAmountDetail().getC200Amount());
            reversalDetail.setC200Count(transactionHeadTeller.getTrxHTAmountDetail().getC200Count());
            reversalDetail.setC500Amount(transactionHeadTeller.getTrxHTAmountDetail().getC500Amount());
            reversalDetail.setC500Count(transactionHeadTeller.getTrxHTAmountDetail().getC500Count());
            reversalDetail.setC1KAmount(transactionHeadTeller.getTrxHTAmountDetail().getC1KAmount());
            reversalDetail.setC1KCount(transactionHeadTeller.getTrxHTAmountDetail().getC1KCount());
            reversalDetail.setP1KAmount(transactionHeadTeller.getTrxHTAmountDetail().getP1KAmount());
            reversalDetail.setP1KCount(transactionHeadTeller.getTrxHTAmountDetail().getP1KCount());
            reversalDetail.setP2KAmount(transactionHeadTeller.getTrxHTAmountDetail().getP2KAmount());
            reversalDetail.setP2KCount(transactionHeadTeller.getTrxHTAmountDetail().getP2KCount());
            reversalDetail.setP5KAmount(transactionHeadTeller.getTrxHTAmountDetail().getP5KAmount());
            reversalDetail.setP5KCount(transactionHeadTeller.getTrxHTAmountDetail().getP5KCount());
            reversalDetail.setP10KAmount(transactionHeadTeller.getTrxHTAmountDetail().getP10KAmount());
            reversalDetail.setP10KCount(transactionHeadTeller.getTrxHTAmountDetail().getP10KCount());
            reversalDetail.setP20KAmount(transactionHeadTeller.getTrxHTAmountDetail().getP20KAmount());
            reversalDetail.setP20KCount(transactionHeadTeller.getTrxHTAmountDetail().getP20KCount());
            reversalDetail.setP50KAmount(transactionHeadTeller.getTrxHTAmountDetail().getP50KAmount());
            reversalDetail.setP50KCount(transactionHeadTeller.getTrxHTAmountDetail().getP50KCount());
            reversalDetail.setP75KAmount(transactionHeadTeller.getTrxHTAmountDetail().getP75KAmount());
            reversalDetail.setP75KCount(transactionHeadTeller.getTrxHTAmountDetail().getP75KCount());
            reversalDetail.setP100KAmount(transactionHeadTeller.getTrxHTAmountDetail().getP100KAmount());
            reversalDetail.setP100KCount(transactionHeadTeller.getTrxHTAmountDetail().getP100KCount());

            TrxHeadTeller saldoReversal = new TrxHeadTeller();
            saldoReversal.setTransactionId(generateHTTransactionId(request.getBranchId(),TrxType.SA.getCode(),period));
            saldoReversal.setBranchId(request.getBranchId());
            saldoReversal.setPeriod(period);
            saldoReversal.setBalance(headTellerBalance.getTotalAmount());
            saldoReversal.setTotalAmount(headTellerBalance.getTotalAmount());
            saldoReversal.setInputer(inputerNik);
            saldoReversal.setInputerName(pic.getInputerName());
            saldoReversal.setType(TrxType.SA.getCode());
            saldoReversal.setStatus(SUCCESS.getValue());
            saldoReversal.setCreateDateTime(LocalDateTime.now());
            saldoReversal.setUpdateDateTime(LocalDateTime.now());

            TrxHTAmountDetail saldoReversalDetail = new TrxHTAmountDetail();
            saldoReversalDetail.setTransactionId(saldoReversal.getTransactionId());
            saldoReversalDetail.setC50Amount(headTellerBalance.getC50Amount());
            saldoReversalDetail.setC50Count(headTellerBalance.getC50Count());
            saldoReversalDetail.setC100Amount(headTellerBalance.getC100Amount());
            saldoReversalDetail.setC100Count(headTellerBalance.getC100Count());
            saldoReversalDetail.setC200Amount(headTellerBalance.getC200Amount());
            saldoReversalDetail.setC200Count(headTellerBalance.getC200Count());
            saldoReversalDetail.setC500Amount(headTellerBalance.getC500Amount());
            saldoReversalDetail.setC500Count(headTellerBalance.getC500Count());
            saldoReversalDetail.setC1KAmount(headTellerBalance.getC1KAmount());
            saldoReversalDetail.setC1KCount(headTellerBalance.getC1KCount());
            saldoReversalDetail.setP1KAmount(headTellerBalance.getP1KAmount());
            saldoReversalDetail.setP1KCount(headTellerBalance.getP1KCount());
            saldoReversalDetail.setP2KAmount(headTellerBalance.getP2KAmount());
            saldoReversalDetail.setP2KCount(headTellerBalance.getP2KCount());
            saldoReversalDetail.setP5KAmount(headTellerBalance.getP5KAmount());
            saldoReversalDetail.setP5KCount(headTellerBalance.getP5KCount());
            saldoReversalDetail.setP10KAmount(headTellerBalance.getP10KAmount());
            saldoReversalDetail.setP10KCount(headTellerBalance.getP10KCount());
            saldoReversalDetail.setP20KAmount(headTellerBalance.getP20KAmount());
            saldoReversalDetail.setP20KCount(headTellerBalance.getP20KCount());
            saldoReversalDetail.setP50KAmount(headTellerBalance.getP50KAmount());
            saldoReversalDetail.setP50KCount(headTellerBalance.getP50KCount());
            saldoReversalDetail.setP75KAmount(headTellerBalance.getP75KAmount());
            saldoReversalDetail.setP75KCount(headTellerBalance.getP75KCount());
            saldoReversalDetail.setP100KAmount(headTellerBalance.getP100KAmount());
            saldoReversalDetail.setP100KCount(headTellerBalance.getP100KCount());
            trxHeadTellerRepository.save(transactionHeadTeller.getTrxHeadTeller());
            trxHeadTellerRepository.save(reversalTransaction);
            trxHeadTellerRepository.save(saldoReversal);
            trxHTAmountDetailRepository.save(reversalDetail);
            trxHTAmountDetailRepository.save(saldoReversalDetail);
            headTellerBalanceRepository.save(headTellerBalance);
            insertAuditTrail(inputerNik, Action.REVERSAL_HT.getValue(),reversalTransaction.getTransactionId(), reversalTransaction.getBranchId());
            koreksiKasBesarResponse.setStatus(ResponseStatus.SUCCESS.getCode());
            koreksiKasBesarResponse.setStatusDesc(ResponseStatus.SUCCESS.getValue());
        }catch (Exception e){
            logger.error("Failes to Process reversal Head Teller" + e.getMessage());
            koreksiKasBesarResponse.setStatus(ResponseStatus.FAILED.getCode());
            koreksiKasBesarResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
            response.setStatus(ResponseStatus.FAILED.getCode());
            response.setStatusDesc(ResponseStatus.FAILED.getValue());
        }
        response.setData(koreksiKasBesarResponse);
        return response;
    }

    public Double getHTAmount(Double firstValue,Double lastValue,String type){
        Double amount = 0.0;
        if (type.equals(TrxType.HT2T.getCode())){
            amount = firstValue + lastValue;
        }else if(type.equals(TrxType.T2HT.getCode())) {
            amount = firstValue - lastValue;
        }
        return amount;
    }

    public Integer getHTCount(Integer firstValue,Integer lastValue,String type){
        Integer count = 0;
        if (type.equals(TrxType.HT2T.getCode())){
            count = firstValue + lastValue;
        }else if(type.equals(TrxType.T2HT.getCode())) {
            count = firstValue - lastValue;
        }
        return count;
    }
    public String generateHTTransactionId(String branchId, String type, LocalDate period){

        Integer getLastTransactionId =  trxHeadTellerRepository.getLastTrxHeadTellerTransactionId(branchId,type,period);
        String transactionNo = String.format("%02d",1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        if (getLastTransactionId!=null){
            getLastTransactionId += 1;
            transactionNo =String.format("%02d",getLastTransactionId);
        }
        return type+formatter.format(period)+branchId+transactionNo;

    }
    
    @Transactional
    public CommonResponse<InputTransactionResponse> koreksiApproval(HeadTellerApprovalReq request, Profile profile, boolean isAdmin) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(CommonConstant.SUBMIT_APPROVAL);
        KoreksiKasBesarResponse koreksiKasBesarResponse = new KoreksiKasBesarResponse();
        TransactionKasBesar transaction = trxKasBesarRepository.kasBesar(request.getApprovalTransactionId());

        try {
            if (!validateVerification(transaction.getTrxKasBesar().getChecker(), request.getNikVerification())) {
                koreksiKasBesarResponse.setRequestId(request.getRequestId());
                koreksiKasBesarResponse.setTransactionId(request.getApprovalTransactionId());
                koreksiKasBesarResponse.setStatus(ResponseStatus.FAILED.getCode());
                koreksiKasBesarResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
                response.setData(koreksiKasBesarResponse);
                response.setStatus(ResponseStatus.FAILED.getCode());
                response.setStatusDesc(ResponseStatus.FAILED.getValue());
                return response;
            }
            String type = transaction.getTrxKasBesar().getTrxType();
            if (transaction.getTrxKasBesar().getStatus().equals(PENDING.getValue())) {
                if (request.getStatus().equals(APPROVED.getValue())) {
                    transaction.getTrxKasBesar().setStatus(request.getStatus());
                    transaction.getTrxKasBesar().setRequestId(request.getRequestId());

                    String trxIdOriginal = transaction.getTrxKasBesar().getTrxType().equals(RVCC.getCode()) ? request.getApprovalTransactionId().substring(3,17) : request.getApprovalTransactionId().substring(3,18);

                    BranchBalance branchBalance = branchBalanceRepository.findByBranchId(request.getBranchId());
                    if (request.getApprovalTransactionId().startsWith(RVSAW.getCode()) || request.getApprovalTransactionId().startsWith(RVCC.getCode()) || request.getApprovalTransactionId().startsWith(RVSAK.getCode())){
                        TrxKasBesar originalTrx = trxKasBesarRepository.findByTransactionId(trxIdOriginal);
                        if (originalTrx != null){
                            TrxKasBesar previousSA = trxKasBesarRepository.findTopByBranchIdAndTrxTypeInAndStatusInAndCreateDateTimeLessThanOrderByCreateDateTimeDesc(originalTrx.getBranchId(), Set.of(TrxType.SAW.getCode(), TrxType.SAK.getCode(), CASHOPNAME.getCode(), TrxType.SA.getCode()), Set.of(APPROVED.getValue(), SUCCESS.getValue()), originalTrx.getCreateDateTime());
                            if (previousSA != null){
                                TrxAmountDetail trxAmountDetail = trxAmountDetailRepository.findByTransactionId(previousSA.getTransactionId());
                                branchBalance.setAmount(trxAmountDetail);
                                branchBalanceRepository.save(branchBalance);
                            }
                        }
                    }else {
                        branchBalance.setC50Count(getCount(branchBalance.getC50Count(), transaction.getTrxAmountDetail().getC50Count(), type));
                        branchBalance.setC50Amount(getAmount(branchBalance.getC50Amount(), transaction.getTrxAmountDetail().getC50Amount(), type));
                        branchBalance.setC100Count(getCount(branchBalance.getC100Count(), transaction.getTrxAmountDetail().getC100Count(), type));
                        branchBalance.setC100Amount(getAmount(branchBalance.getC100Amount(), transaction.getTrxAmountDetail().getC100Amount(), type));
                        branchBalance.setC200Count(getCount(branchBalance.getC200Count(), transaction.getTrxAmountDetail().getC200Count(), type));
                        branchBalance.setC200Amount(getAmount(branchBalance.getC200Amount(), transaction.getTrxAmountDetail().getC200Amount(), type));
                        branchBalance.setC500Count(getCount(branchBalance.getC500Count(), transaction.getTrxAmountDetail().getC500Count(), type));
                        branchBalance.setC500Amount(getAmount(branchBalance.getC500Amount(), transaction.getTrxAmountDetail().getC500Amount(), type));
                        branchBalance.setC1KCount(getCount(branchBalance.getC1KCount(), transaction.getTrxAmountDetail().getC1KCount(), type));
                        branchBalance.setC1KAmount(getAmount(branchBalance.getC1KAmount(), transaction.getTrxAmountDetail().getC1KAmount(), type));
                        branchBalance.setP1KCount(getCount(branchBalance.getP1KCount(), transaction.getTrxAmountDetail().getP1KCount(), type));
                        branchBalance.setP1KAmount(getAmount(branchBalance.getP1KAmount(), transaction.getTrxAmountDetail().getP1KAmount(), type));
                        branchBalance.setP2KCount(getCount(branchBalance.getP2KCount(), transaction.getTrxAmountDetail().getP2KCount(), type));
                        branchBalance.setP2KAmount(getAmount(branchBalance.getP2KAmount(), transaction.getTrxAmountDetail().getP2KAmount(), type));
                        branchBalance.setP5KCount(getCount(branchBalance.getP5KCount(), transaction.getTrxAmountDetail().getP5KCount(), type));
                        branchBalance.setP5KAmount(getAmount(branchBalance.getP5KAmount(), transaction.getTrxAmountDetail().getP5KAmount(), type));
                        branchBalance.setP10KCount(getCount(branchBalance.getP10KCount(), transaction.getTrxAmountDetail().getP10KCount(), type));
                        branchBalance.setP10KAmount(getAmount(branchBalance.getP10KAmount(), transaction.getTrxAmountDetail().getP10KAmount(), type));
                        branchBalance.setP20KCount(getCount(branchBalance.getP20KCount(), transaction.getTrxAmountDetail().getP20KCount(), type));
                        branchBalance.setP20KAmount(getAmount(branchBalance.getP20KAmount(), transaction.getTrxAmountDetail().getP20KAmount(), type));
                        branchBalance.setP50KCount(getCount(branchBalance.getP50KCount(), transaction.getTrxAmountDetail().getP50KCount(), type));
                        branchBalance.setP50KAmount(getAmount(branchBalance.getP50KAmount(), transaction.getTrxAmountDetail().getP50KAmount(), type));
                        branchBalance.setP75KCount(getCount(branchBalance.getP75KCount(), transaction.getTrxAmountDetail().getP75KCount(), type));
                        branchBalance.setP75KAmount(getAmount(branchBalance.getP75KAmount(), transaction.getTrxAmountDetail().getP75KAmount(), type));
                        branchBalance.setP100KCount(getCount(branchBalance.getP100KCount(), transaction.getTrxAmountDetail().getP100KCount(), type));
                        branchBalance.setP100KAmount(getAmount(branchBalance.getP100KAmount(), transaction.getTrxAmountDetail().getP100KAmount(), type));
                        branchBalanceRepository.save(branchBalance);  
                    }
                    


                    TrxKasBesar trxKasBesarApp = new TrxKasBesar();
                    PICModel pic = enrichPICName(transaction.getTrxKasBesar().getInputer(), request.getNikVerification(), officerImpl, officerNonProsperaRepository);
                    trxKasBesarApp.setTransactionId(getTransactionId(TrxType.APR.getCode(), LocalDate.parse(request.getPeriod()),request.getBranchId(),KAS_BESAR));
                    trxKasBesarApp.setStatus(request.getStatus());
                    trxKasBesarApp.setBranchId(request.getBranchId());
                    trxKasBesarApp.setPeriod(LocalDate.parse(request.getPeriod()));
                    trxKasBesarApp.setTrxType(TrxType.APR.getCode());
                    trxKasBesarApp.setChecker(request.getNikVerification());
                    trxKasBesarApp.setCheckerName(pic.getTellerName());
                    trxKasBesarApp.setBalanceAmount(branchBalance.getTotal());
                    trxKasBesarApp.setTotalAmount(transaction.getTrxAmountDetail().getTotal());
                    trxKasBesarApp.setCreateDateTime(LocalDateTime.now());
                    trxKasBesarApp.setUpdateDateTime(LocalDateTime.now());
                    trxKasBesarRepository.save(trxKasBesarApp);

                    TrxAmountDetail trxAmountDetail = new TrxAmountDetail();
                    trxAmountDetail.setTransactionId(trxKasBesarApp.getTransactionId());
                    trxAmountDetail.setC50Amount(branchBalance.getC50Amount());
                    trxAmountDetail.setC50Count(branchBalance.getC50Count());
                    trxAmountDetail.setC100Amount(branchBalance.getC100Amount());
                    trxAmountDetail.setC100Count(branchBalance.getC100Count());
                    trxAmountDetail.setC200Amount(branchBalance.getC200Amount());
                    trxAmountDetail.setC200Count(branchBalance.getC200Count());
                    trxAmountDetail.setC500Amount(branchBalance.getC500Amount());
                    trxAmountDetail.setC500Count(branchBalance.getC500Count());
                    trxAmountDetail.setC1KAmount(branchBalance.getC1KAmount());
                    trxAmountDetail.setC1KCount(branchBalance.getC1KCount());
                    trxAmountDetail.setP1KAmount(branchBalance.getP1KAmount());
                    trxAmountDetail.setP1KCount(branchBalance.getP1KCount());
                    trxAmountDetail.setP2KAmount(branchBalance.getP2KAmount());
                    trxAmountDetail.setP2KCount(branchBalance.getP2KCount());
                    trxAmountDetail.setP5KAmount(branchBalance.getP5KAmount());
                    trxAmountDetail.setP5KCount(branchBalance.getP5KCount());
                    trxAmountDetail.setP10KAmount(branchBalance.getP10KAmount());
                    trxAmountDetail.setP10KCount(branchBalance.getP10KCount());
                    trxAmountDetail.setP20KAmount(branchBalance.getP20KAmount());
                    trxAmountDetail.setP20KCount(branchBalance.getP20KCount());
                    trxAmountDetail.setP50KAmount(branchBalance.getP50KAmount());
                    trxAmountDetail.setP50KCount(branchBalance.getP50KCount());
                    trxAmountDetail.setP75KAmount(branchBalance.getP75KAmount());
                    trxAmountDetail.setP75KCount(branchBalance.getP75KCount());
                    trxAmountDetail.setP100KAmount(branchBalance.getP100KAmount());
                    trxAmountDetail.setP100KCount(branchBalance.getP100KCount());
                    trxAmountDetailRepository.save(trxAmountDetail);

                    TrxKasBesar trxKasBesarSaldo = new TrxKasBesar();
                    trxKasBesarSaldo.setTransactionId(getTransactionId(TrxType.SA.getCode(), LocalDate.parse(request.getPeriod()),request.getBranchId(),KAS_BESAR));
                    trxKasBesarSaldo.setStatus(SUCCESS.getValue());
                    trxKasBesarSaldo.setBranchId(request.getBranchId());
                    trxKasBesarSaldo.setPeriod(LocalDate.parse(request.getPeriod()));
                    trxKasBesarSaldo.setTrxType(TrxType.SA.getCode());
                    trxKasBesarSaldo.setChecker(request.getNikVerification());
                    trxKasBesarSaldo.setCheckerName(pic.getTellerName());
                    trxKasBesarSaldo.setInputer(transaction.getTrxKasBesar().getInputer());
                    trxKasBesarSaldo.setInputerName(pic.getInputerName());
                    trxKasBesarSaldo.setBalanceAmount(branchBalance.getTotal());
                    trxKasBesarSaldo.setTotalAmount(transaction.getTrxAmountDetail().getTotal());
                    trxKasBesarSaldo.setCreateDateTime(LocalDateTime.now());
                    trxKasBesarSaldo.setUpdateDateTime(LocalDateTime.now());
                    trxKasBesarRepository.save(trxKasBesarSaldo);

                    TrxAmountDetail detailSaldo = new TrxAmountDetail();
                    detailSaldo.setTransactionId(trxKasBesarSaldo.getTransactionId());
                    detailSaldo.setC50Amount(branchBalance.getC50Amount());
                    detailSaldo.setC50Count(branchBalance.getC50Count());
                    detailSaldo.setC100Amount(branchBalance.getC100Amount());
                    detailSaldo.setC100Count(branchBalance.getC100Count());
                    detailSaldo.setC200Amount(branchBalance.getC200Amount());
                    detailSaldo.setC200Count(branchBalance.getC200Count());
                    detailSaldo.setC500Amount(branchBalance.getC500Amount());
                    detailSaldo.setC500Count(branchBalance.getC500Count());
                    detailSaldo.setC1KAmount(branchBalance.getC1KAmount());
                    detailSaldo.setC1KCount(branchBalance.getC1KCount());
                    detailSaldo.setP1KAmount(branchBalance.getP1KAmount());
                    detailSaldo.setP1KCount(branchBalance.getP1KCount());
                    detailSaldo.setP2KAmount(branchBalance.getP2KAmount());
                    detailSaldo.setP2KCount(branchBalance.getP2KCount());
                    detailSaldo.setP5KAmount(branchBalance.getP5KAmount());
                    detailSaldo.setP5KCount(branchBalance.getP5KCount());
                    detailSaldo.setP10KAmount(branchBalance.getP10KAmount());
                    detailSaldo.setP10KCount(branchBalance.getP10KCount());
                    detailSaldo.setP20KAmount(branchBalance.getP20KAmount());
                    detailSaldo.setP20KCount(branchBalance.getP20KCount());
                    detailSaldo.setP50KAmount(branchBalance.getP50KAmount());
                    detailSaldo.setP50KCount(branchBalance.getP50KCount());
                    detailSaldo.setP75KAmount(branchBalance.getP75KAmount());
                    detailSaldo.setP75KCount(branchBalance.getP75KCount());
                    detailSaldo.setP100KAmount(branchBalance.getP100KAmount());
                    detailSaldo.setP100KCount(branchBalance.getP100KCount());
                    trxAmountDetailRepository.save(detailSaldo);

                    TrxKasBesar trxOriginal = trxKasBesarRepository.findByTransactionId(trxIdOriginal);
                    trxOriginal.setStatus(TrxStatus.REVERSAL.getValue());
                    trxKasBesarRepository.save(trxOriginal);
                    
                    if (trxOriginal.getTrxType().equals(KHT.getCode()) || trxOriginal.getTrxType().equals(MHT.getCode()) ){
                        processVaultAndHt(transaction.getTrxKasBesar(), request.getApprovalTransactionId(), trxIdOriginal, type, transaction.getTrxAmountDetail());
                    }
                    insertAuditTrail(profile.getPreferred_username(), getTrxType(Action.APPROVE_RVKHT.getValue(), Action.APPROVE_RVMHT.getValue(),Action.APPROVE_RVCC.getValue(),Action.APPROVE_RVSAW.getValue(),Action.APPROVE_RVSAK.getValue(), type), request.getApprovalTransactionId(), request.getBranchId());
                    approveKoreksiTellerExchange(request, trxOriginal);
                } else if (request.getStatus().equals(TrxStatus.REJECTED.getValue())) {
                    transaction.getTrxKasBesar().setStatus(request.getStatus());
                    transaction.getTrxKasBesar().setAdditionalInfo(encodeIfnotNull(request.getReason()));
                    trxKasBesarRepository.save(transaction.getTrxKasBesar());
                    if (isAdmin){
                        insertAuditTrail(profile.getPreferred_username(), getTrxType(Action.REJECT_RVKHT.getValue().concat(ACTION_ADMIN), Action.REJECT_RVMHT.getValue().concat(ACTION_ADMIN),Action.REJECT_RVCC.getValue().concat(ACTION_ADMIN),Action.REJECT_RVSAW.getValue().concat(ACTION_ADMIN),Action.REJECT_RVSAK.getValue().concat(ACTION_ADMIN), type), request.getApprovalTransactionId(), request.getBranchId());
                    }else {
                        insertAuditTrail(profile.getPreferred_username(), getTrxType(Action.REJECT_RVKHT.getValue(), Action.REJECT_RVMHT.getValue(),Action.REJECT_RVCC.getValue(),Action.REJECT_RVSAW.getValue(),Action.REJECT_RVSAK.getValue(), type), request.getApprovalTransactionId(), request.getBranchId());
                    }
                }
                koreksiKasBesarResponse.setRequestId(request.getRequestId());
                koreksiKasBesarResponse.setStatus(SUCCESS.getCode());
                koreksiKasBesarResponse.setStatusDesc(SUCCESS.getValue());
            } else {
                koreksiKasBesarResponse.setRequestId(transaction.getTrxKasBesar().getRequestId());
                koreksiKasBesarResponse.setStatus(ALREADY_SUCCESS.getCode());
                koreksiKasBesarResponse.setStatusDesc(ALREADY_SUCCESS.getValue());
                response.setStatus(ALREADY_SUCCESS.getCode());
                response.setStatusDesc(ALREADY_SUCCESS.getValue());
            }

            koreksiKasBesarResponse.setTransactionId(transaction.getTrxKasBesar().getTransactionId());
            response.setData(koreksiKasBesarResponse);
            return response;

        } catch (Exception e) {
            logger.error("Failed to Approval Koreksi " + e.getMessage());
            koreksiKasBesarResponse.setStatus(TrxStatus.FAILED.getCode());
            koreksiKasBesarResponse.setStatusDesc(TrxStatus.FAILED.getValue());
            response.setData(koreksiKasBesarResponse);
            response.setStatus(TrxStatus.FAILED.getCode());
            response.setStatusDesc(TrxStatus.FAILED.getValue());
            return response;
        }
    }

    public void processVaultAndHt(TrxKasBesar trxKasBesar, String trxIdKasBesar, String trxIdOriginal, String type,TrxAmountDetail balance){

        String branchId = trxKasBesar.getBranchId();
        Double totalAmount = trxKasBesar.getTotalAmount();
        HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branchId);
        LocalDate period = LocalDate.now();

        TrxHeadTeller trxHeadTeller = trxHeadTellerRepository.findByRefId(trxIdOriginal);

        String transactionId = RV_DASH.getCode().concat(trxHeadTeller.getTransactionId());
        String headTellerType = trxHeadTeller.getTransactionId().startsWith(V2HT.getCode()) ? RVV2HT.getCode() : RVHT2V.getCode();
        TrxHeadTeller transaction = new TrxHeadTeller(trxHeadTeller);
        Double balanceResult = getAmountHT(headTellerBalance.getTotalAmount(), totalAmount, type);

        transaction.setTransactionId(transactionId).setPeriod(LocalDate.now()).setStatus(TrxStatus.REVERSAL.getValue()).setType(headTellerType)
                .setTotalAmount(totalAmount).setBalance(balanceResult).setRefId(trxIdKasBesar).setReason(trxKasBesar.getReason());
        trxHeadTellerRepository.save(transaction);
        
        TrxHTAmountDetail saldoTransaction = new TrxHTAmountDetail();
        saldoTransaction.setTransactionId(transaction.getTransactionId());
        saldoTransaction.setC50Amount(balance.getC50Amount());
        saldoTransaction.setC50Count(balance.getC50Count());
        saldoTransaction.setC100Amount(balance.getC100Amount());
        saldoTransaction.setC100Count(balance.getC100Count());
        saldoTransaction.setC200Amount(balance.getC200Amount());
        saldoTransaction.setC200Count(balance.getC200Count());
        saldoTransaction.setC500Amount(balance.getC500Amount());
        saldoTransaction.setC500Count(balance.getC500Count());
        saldoTransaction.setC1KAmount(balance.getC1KAmount());
        saldoTransaction.setC1KCount(balance.getC1KCount());
        saldoTransaction.setP1KAmount(balance.getP1KAmount());
        saldoTransaction.setP1KCount(balance.getP1KCount());
        saldoTransaction.setP2KAmount(balance.getP2KAmount());
        saldoTransaction.setP2KCount(balance.getP2KCount());
        saldoTransaction.setP5KAmount(balance.getP5KAmount());
        saldoTransaction.setP5KCount(balance.getP5KCount());
        saldoTransaction.setP10KAmount(balance.getP10KAmount());
        saldoTransaction.setP10KCount(balance.getP10KCount());
        saldoTransaction.setP20KAmount(balance.getP20KAmount());
        saldoTransaction.setP20KCount(balance.getP20KCount());
        saldoTransaction.setP50KAmount(balance.getP50KAmount());
        saldoTransaction.setP50KCount(balance.getP50KCount());
        saldoTransaction.setP75KAmount(balance.getP75KAmount());
        saldoTransaction.setP75KCount(balance.getP75KCount());
        saldoTransaction.setP100KAmount(balance.getP100KAmount());
        saldoTransaction.setP100KCount(balance.getP100KCount());
        trxHTAmountDetailRepository.save(saldoTransaction);
        
        TrxHeadTeller trxSaldo = new TrxHeadTeller();
        trxSaldo.setTransactionId(getTransactionId(TrxType.SA.getCode(), period, branchId, HEAD_TELLER));
        trxSaldo.setBranchId(branchId);
        trxSaldo.setPeriod(period);
        trxSaldo.setType(TrxType.SA.getCode());
        trxSaldo.setStatus(SUCCESS.getValue());
        trxSaldo.setInputer(trxKasBesar.getInputer());
        trxSaldo.setTotalAmount(balanceResult);
        trxSaldo.setBalance(balanceResult);
        trxSaldo.setCreateDateTime(LocalDateTime.now());
        trxSaldo.setUpdateDateTime(LocalDateTime.now());
        PICModel pic = enrichPICName(trxSaldo.getInputer(), trxSaldo.getTellerId(), officerImpl, officerNonProsperaRepository);
        trxSaldo.setInputerName(pic.getInputerName());
        trxHeadTellerRepository.save(trxSaldo);
        
        TrxHTAmountDetail saldo = new TrxHTAmountDetail();
        saldo.setTransactionId(trxSaldo.getTransactionId());
        saldo.setC50Amount(getAmountHT(headTellerBalance.getC50Amount(), balance.getC50Amount(), type));
        saldo.setC50Count(getCountHT(headTellerBalance.getC50Count(), balance.getC50Count(), type));
        saldo.setC100Amount(getAmountHT(headTellerBalance.getC100Amount(), balance.getC100Amount(), type));
        saldo.setC100Count(getCountHT(headTellerBalance.getC100Count(), balance.getC100Count(), type));
        saldo.setC200Amount(getAmountHT(headTellerBalance.getC200Amount(), balance.getC200Amount(), type));
        saldo.setC200Count(getCountHT(headTellerBalance.getC200Count(), balance.getC200Count(), type));
        saldo.setC500Amount(getAmountHT(headTellerBalance.getC500Amount(), balance.getC500Amount(), type));
        saldo.setC500Count(getCountHT(headTellerBalance.getC500Count(), balance.getC500Count(), type));
        saldo.setC1KAmount(getAmountHT(headTellerBalance.getC1KAmount(), balance.getC1KAmount(), type));
        saldo.setC1KCount(getCountHT(headTellerBalance.getC1KCount(), balance.getC1KCount(), type));
        saldo.setP1KAmount(getAmountHT(headTellerBalance.getP1KAmount(), balance.getP1KAmount(), type));
        saldo.setP1KCount(getCountHT(headTellerBalance.getP1KCount(), balance.getP1KCount(), type));
        saldo.setP2KAmount(getAmountHT(headTellerBalance.getP2KAmount(), balance.getP2KAmount(), type));
        saldo.setP2KCount(getCountHT(headTellerBalance.getP2KCount(), balance.getP2KCount(), type));
        saldo.setP5KAmount(getAmountHT(headTellerBalance.getP5KAmount(), balance.getP5KAmount(), type));
        saldo.setP5KCount(getCountHT(headTellerBalance.getP5KCount(), balance.getP5KCount(), type));
        saldo.setP10KAmount(getAmountHT(headTellerBalance.getP10KAmount(), balance.getP10KAmount(), type));
        saldo.setP10KCount(getCountHT(headTellerBalance.getP10KCount(), balance.getP10KCount(), type));
        saldo.setP20KAmount(getAmountHT(headTellerBalance.getP20KAmount(), balance.getP20KAmount(), type));
        saldo.setP20KCount(getCountHT(headTellerBalance.getP20KCount(), balance.getP20KCount(), type));
        saldo.setP50KAmount(getAmountHT(headTellerBalance.getP50KAmount(), balance.getP50KAmount(), type));
        saldo.setP50KCount(getCountHT(headTellerBalance.getP50KCount(), balance.getP50KCount(), type));
        saldo.setP75KAmount(getAmountHT(headTellerBalance.getP75KAmount(), balance.getP75KAmount(), type));
        saldo.setP75KCount(getCountHT(headTellerBalance.getP75KCount(), balance.getP75KCount(), type));
        saldo.setP100KAmount(getAmountHT(headTellerBalance.getP100KAmount(), balance.getP100KAmount(), type));
        saldo.setP100KCount(getCountHT(headTellerBalance.getP100KCount(), balance.getP100KCount(), type));
        trxHTAmountDetailRepository.save(saldo);
            
        headTellerBalance.setC50Amount(getAmountHT(headTellerBalance.getC50Amount(), balance.getC50Amount(), type));
        headTellerBalance.setC50Count(getCountHT(headTellerBalance.getC50Count(), balance.getC50Count(), type));
        headTellerBalance.setC100Amount(getAmountHT(headTellerBalance.getC100Amount(), balance.getC100Amount(), type));
        headTellerBalance.setC100Count(getCountHT(headTellerBalance.getC100Count(), balance.getC100Count(), type));
        headTellerBalance.setC200Amount(getAmountHT(headTellerBalance.getC200Amount(), balance.getC200Amount(), type));
        headTellerBalance.setC200Count(getCountHT(headTellerBalance.getC200Count(), balance.getC200Count(), type));
        headTellerBalance.setC500Amount(getAmountHT(headTellerBalance.getC500Amount(), balance.getC500Amount(), type));
        headTellerBalance.setC500Count(getCountHT(headTellerBalance.getC500Count(), balance.getC500Count(), type));
        headTellerBalance.setC1KAmount(getAmountHT(headTellerBalance.getC1KAmount(), balance.getC1KAmount(), type));
        headTellerBalance.setC1KCount(getCountHT(headTellerBalance.getC1KCount(), balance.getC1KCount(), type));
        headTellerBalance.setP1KAmount(getAmountHT(headTellerBalance.getP1KAmount(), balance.getP1KAmount(), type));
        headTellerBalance.setP1KCount(getCountHT(headTellerBalance.getP1KCount(), balance.getP1KCount(), type));
        headTellerBalance.setP2KAmount(getAmountHT(headTellerBalance.getP2KAmount(), balance.getP2KAmount(), type));
        headTellerBalance.setP2KCount(getCountHT(headTellerBalance.getP2KCount(), balance.getP2KCount(), type));
        headTellerBalance.setP5KAmount(getAmountHT(headTellerBalance.getP5KAmount(), balance.getP5KAmount(), type));
        headTellerBalance.setP5KCount(getCountHT(headTellerBalance.getP5KCount(), balance.getP5KCount(), type));
        headTellerBalance.setP10KAmount(getAmountHT(headTellerBalance.getP10KAmount(), balance.getP10KAmount(), type));
        headTellerBalance.setP10KCount(getCountHT(headTellerBalance.getP10KCount(), balance.getP10KCount(), type));
        headTellerBalance.setP20KAmount(getAmountHT(headTellerBalance.getP20KAmount(), balance.getP20KAmount(), type));
        headTellerBalance.setP20KCount(getCountHT(headTellerBalance.getP20KCount(), balance.getP20KCount(), type));
        headTellerBalance.setP50KAmount(getAmountHT(headTellerBalance.getP50KAmount(), balance.getP50KAmount(), type));
        headTellerBalance.setP50KCount(getCountHT(headTellerBalance.getP50KCount(), balance.getP50KCount(), type));
        headTellerBalance.setP75KAmount(getAmountHT(headTellerBalance.getP75KAmount(), balance.getP75KAmount(), type));
        headTellerBalance.setP75KCount(getCountHT(headTellerBalance.getP75KCount(), balance.getP75KCount(), type));
        headTellerBalance.setP100KAmount(getAmountHT(headTellerBalance.getP100KAmount(), balance.getP100KAmount(), type));
        headTellerBalance.setP100KCount(getCountHT(headTellerBalance.getP100KCount(), balance.getP100KCount(), type));
        headTellerBalance.setTotalAmount(getAmountHT(headTellerBalance.getTotalAmount(), totalAmount, type));

        headTellerBalanceRepository.save(headTellerBalance);
        
        trxHeadTeller.setStatus(TrxStatus.REVERSAL.getValue());
        trxHeadTellerRepository.save(trxHeadTeller);
    }

    public KoreksiDetail getKoreksiDetail(String transactionId) {
        TransactionKasBesar transaction = trxKasBesarRepository.kasBesar(transactionId);
        Cabang cabang = cabangRepository.findByCabangId(transaction.getTrxKasBesar().getBranchId());

        KoreksiDetail koreksi = new KoreksiDetail();
        TrxAmountDetail amountDetail = new TrxAmountDetail(transaction.getTrxAmountDetail());

        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> map = mapper.convertValue(amountDetail, Map.class);

        KoreksiBalanceDetail balance = new KoreksiBalanceDetail();
        List<KoreksiBalanceDetail> balanceList = new ArrayList<>();
        
        for (var entry : map.entrySet()) {
            String key = entry.getKey();

            if (key.contains("Count") || key.contains("Amount")) {
                
                if (key.contains("Count")) {
                    balance.setId(key.substring(0, key.indexOf("Count")));
                    balance.setCount((Integer) entry.getValue());
                } else if (key.contains("Amount")) {
                    balance.setId(key.substring(0, key.indexOf("Amount")));
                    balance.setTotal((Double) entry.getValue());
                }

                if (balance.getId().contains("K")) {
                    balance.setId(balance.getId().substring(0, balance.getId().length()-1).concat(".000"));
                }

                if (key.startsWith("c")) {
                    balance.setType("coin");
                } else if (key.startsWith("p")) {
                    balance.setType("paper");
                }

                balance.setDenom(Double.parseDouble(balance.getId().substring(1).replace(".", "")));

                if (balance.getCount() != null && balance.getTotal() != null) {
                    balanceList.add(balance);
                    balance = new KoreksiBalanceDetail();
                }
            }
        }
        
        koreksi.setTransactionId(transactionId).setTotal(transaction.getTrxKasBesar().getTotalAmount()).setBranchId(transaction.getTrxKasBesar().getBranchId())
                .setBranchName(cabang.getCabangDesc()).setReason(transaction.getTrxKasBesar().getReason()).setRejectReason(transaction.getTrxKasBesar().getAdditionalInfo()).setPeriod(LocalDate.now().toString())
                .setNikVerification(transaction.getTrxKasBesar().getChecker()).setNameVerification(transaction.getTrxKasBesar().getCheckerName()).setBalanceDetail(balanceList);

        return koreksi;
    }
    
    public void insertAuditTrail(String nik, String action, String transactionId, String branchId){
        TrxAuditTrail trxAuditTrail = new TrxAuditTrail();
        trxAuditTrail.setNik(nik);
        trxAuditTrail.setAction(action);
        trxAuditTrail.setCreateDateTime(LocalDateTime.now());
        trxAuditTrail.setTransactionId(transactionId);
        trxAuditTrail.setBranchId(branchId);
        trxAuditTrailRepository.save(trxAuditTrail);
    }

    public Double getAmount(Double firstValue, Double secondValue, String type) {
        if(type.equals(RVKHT.getCode())) {
            return firstValue + secondValue;
        } else if (type.equals(RVMHT.getCode())) {
            return firstValue - secondValue;
        }
        return 0.0;
    }

    public Integer getCount(Integer firstValue, Integer secondValue, String type) {
        if(type.equals(RVKHT.getCode())) {
            return firstValue + secondValue;
        } else if (type.equals(RVMHT.getCode())) {
            return firstValue - secondValue;
        }
        return 0;
    }

    public String getTransactionId(String trxType,LocalDate date,String branchId,String process) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        String transactionNo = null;
        
        if (process.equals(KAS_BESAR)) {
            Integer lastNo = trxKasBesarRepository.countTransaction(date, trxType, branchId);
            transactionNo = lastNo != null ? String.format("%02d", lastNo + 1) : null;
        } else if (process.equals(HEAD_TELLER)) {
            Integer lastHeadTeller = trxHeadTellerRepository.getLastTrxHeadTellerTransactionId(branchId, trxType, date);
            if (lastHeadTeller != null) {
                transactionNo = String.format("%02d", lastHeadTeller + 1);
            }
        }
        if (transactionNo == null) {
            transactionNo = String.format("%02d", 1);
        }
        
        return trxType + formatter.format(date) + branchId + transactionNo;
    }

    public String getTrxType(String firstValue, String secondValue, String thirdValue, String fourthValue, String fifthValue, String type) {
        if (type.equals(RVKHT.getCode())) {
            return firstValue;
        } else if (type.equals(RVMHT.getCode())) {
            return secondValue;
        }else if (type.equals(RVCC.getCode())) {
            return thirdValue;
        }else if (type.equals(RVSAW.getCode())) {
            return fourthValue;
        }else if (type.equals(RVSAK.getCode())) {
            return fifthValue;
        }
        return null;
    }

    public Double getAmountHT(Double firstValue, Double secondValue, String type) {
        if(type.equals(RVKHT.getCode())) {
            return firstValue - secondValue;
        } else if (type.equals(RVMHT.getCode())) {
            return firstValue + secondValue;
        }
        return 0.0;
    }

    public Integer getCountHT(Integer firstValue, Integer secondValue, String type) {
        if(type.equals(RVKHT.getCode())) {
            return firstValue - secondValue;
        } else if (type.equals(RVMHT.getCode())) {
            return firstValue + secondValue;
        }
        return 0;
    }
    private static String encodeIfnotNull(String data) {
        if (data != null) {
            return StringEscapeUtils.escapeHtml4(data);
        }
        return null;
    }
    
    public CheckTransactionReversalModel checkMHTReversal(TrxHeadTeller headTeller){
        CheckTransactionReversalModel checkTransactionReversalModel = new CheckTransactionReversalModel();
        TrxKasBesar kasBesar = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeAndStatusInAndCreateDateTimeGreaterThan(headTeller.getPeriod(), headTeller.getBranchId(), TrxType.MHT.getCode(), Set.of(APPROVED.getValue(),TrxStatus.REVERSAL.getValue(), PENDING.getValue()), headTeller.getCreateDateTime());
        if (kasBesar != null){
            if (kasBesar.getStatus().equals(APPROVED.getValue()) || kasBesar.getStatus().equals(PENDING.getValue())){
                checkTransactionReversalModel.setTransactionId(kasBesar.getTransactionId());
                checkTransactionReversalModel.setStatus(kasBesar.getStatus());
                checkTransactionReversalModel.setTrxType(kasBesar.getTrxType());
                return checkTransactionReversalModel;
            }else if (kasBesar.getStatus().equals(TrxStatus.REVERSAL.getValue())){
                return null;
            }
        }
        return null;
    }
    public void approveKoreksiTellerExchange(HeadTellerApprovalReq request, TrxKasBesar kasBesar) {
        if (kasBesar.getTrxType().equals(TrxType.KHT.getCode())) {
            TrxTellerExchangeVault vault = trxTellerExchangeVaultRepository.findByRefId(kasBesar.getTransactionId());
            if (vault != null) {
                if (request.getStatus().equals(APPROVED.getValue())) {
                    vault.setStatus(TrxStatus.CANCEL.getValue());
                }
            }
        }
    }
    public boolean checkTransactionHeadTeller(TrxKasBesar trxKasBesar){
        TrxKasBesar kasBesarTransaction = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeInAndStatusAndCreateDateTimeGreaterThanOrderByCreateDateTimeAsc(trxKasBesar.getPeriod(), trxKasBesar.getBranchId(), Set.of(KHT.getCode(), MHT.getCode(), SAW.getCode(), SAK.getCode(), CC.getCode()), APPROVED.getValue(), trxKasBesar.getCreateDateTime());
        if (trxKasBesar.getTrxType().equals(KHT.getCode())){
            if (kasBesarTransaction != null){
                if (kasBesarTransaction.getTrxType().equals(SAK.getCode()) || kasBesarTransaction.getTrxType().equals(SAW.getCode()) || kasBesarTransaction.getTrxType().equals(CC.getCode())){
                    return false;
                }
                    TrxHeadTeller headTellerTransaction = trxHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeInAndStatusAndCreateDateTimeLessThanEqualAndCreateDateTimeGreaterThanEqualOrderByCreateDateTimeDesc(trxKasBesar.getPeriod(), trxKasBesar.getBranchId(), Set.of(HT2T.getCode(), T2HT.getCode()), SUCCESS.getValue(), kasBesarTransaction.getCreateDateTime(), trxKasBesar.getCreateDateTime());
                    if (headTellerTransaction != null){
                        return false;
                    }else {
                        return true;
                    }
            }else {
                TrxHeadTeller headTellerTransaction = trxHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeInAndStatusAndCreateDateTimeGreaterThanEqualOrderByCreateDateTimeDesc(trxKasBesar.getPeriod(), trxKasBesar.getBranchId(), Set.of(HT2T.getCode(), T2HT.getCode()), SUCCESS.getValue(), trxKasBesar.getCreateDateTime());
                if (headTellerTransaction != null){
                    return false;
                }    
            }
        }else {
            if (kasBesarTransaction != null){
                return false;
            }
        }
        return true;
    }
}   
