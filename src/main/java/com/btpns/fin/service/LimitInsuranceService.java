package com.btpns.fin.service;

import com.btpns.fin.constant.ResponseStatus;
import com.btpns.fin.model.CommonResponse;
import com.btpns.fin.model.LimitInsuranceModel;
import com.btpns.fin.model.Profile;
import com.btpns.fin.model.UserOfficerModel;
import com.btpns.fin.model.entity.Cabang;
import com.btpns.fin.model.entity.TrxAuditTrail;
import com.btpns.fin.model.request.LimitInsuranceRequest;
import com.btpns.fin.model.request.LimitInsuranceRequestDetail;
import com.btpns.fin.model.response.LimitInsuranceSubmitResponse;
import com.btpns.fin.repository.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import retrofit2.Response;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

import static com.btpns.fin.constant.CommonConstant.*;

@Service
public class LimitInsuranceService {
    private static final Logger logger = LoggerFactory.getLogger(LimitInsuranceService.class);

    @Autowired
    CabangRepository cabangRepository;
    @Autowired
    OfficerRepository officerRepository;
    @Autowired
    TrxAuditTrailRepository trxAuditTrailRepository;
    @Autowired
    OfficerImpl officerImpl;
    @Autowired
    private Gson gson;
    @Autowired
    private ILDAPServerRepository ldapServerRepository;
    @Value("${ldap.enable}")
    private boolean isLdapEnable;
    @Value("${client.id}")
    private String clientId;
    @Value("${client.secret}")
    private String clientSecret;
    @Value("${ldap.scope}")
    private String clientScope;
    @Value("${grant.type}")
    private String grantType;

    
    public CommonResponse<List<LimitInsuranceModel>> getListLimitInsurance(){
        CommonResponse<List<LimitInsuranceModel>> response = new CommonResponse<>();
        try {
            response.setType(GET_LIST_LIMIT_INSURANCE);
            List<LimitInsuranceModel> limitInsurance = cabangRepository.getListLimitInsurance();
            if (!limitInsurance.isEmpty()){
                response.setData(limitInsurance);
            }
        }catch (Exception e){
            logger.error("Failed to get List Limit Insurance ", e);
            response.setStatusDesc(ResponseStatus.FAILED.getValue());
            response.setStatus(ResponseStatus.FAILED.getCode());
        }
        return response;
    }

    @Transactional
    public CommonResponse<LimitInsuranceSubmitResponse> submitLimitInsurance(Profile profile, LimitInsuranceRequest request) {
        CommonResponse<LimitInsuranceSubmitResponse> response = new CommonResponse<>();
        LimitInsuranceSubmitResponse data = new LimitInsuranceSubmitResponse();
        LimitInsuranceRequestDetail requestDetail = request.getDetails();
        try {
            response.setType(SUBMIT_LIMIT_INSURANCE);
            String password = encodePassword(requestDetail.getPassword());
            UserOfficerModel officer = officerImpl.getOfficerByNikAndBranchAndRoleIdAndStatusCode(requestDetail.getUsername(), "HO", PAYMENT_OTORISATOR, 1);
            if (officer != null) {
                if (validateOtorisator(requestDetail.getUsername(), password)) {
                    if (request.getMode().equalsIgnoreCase(REQUEST_TYPE_UPDATE)) {
                        Cabang cabang = cabangRepository.findByCabangId(requestDetail.getBranchId());
                        if (cabang != null) {
                            cabang.setCabangDesc(StringUtils.isEmpty(requestDetail.getBranchName()) ? cabang.getCabangDesc() : requestDetail.getBranchName());
                            cabang.setTotalBalance(StringUtils.isEmpty(requestDetail.getLimitAsuransiLembesAmount()) ? cabang.getTotalBalance() : requestDetail.getLimitAsuransiLembesAmount());
                            cabang.setTotalBalanceHT(StringUtils.isEmpty(requestDetail.getLimitAsuransiCashBoxAmount()) ? cabang.getTotalBalanceHT() : requestDetail.getLimitAsuransiCashBoxAmount());
                            cabang.setEmail(StringUtils.isEmpty(requestDetail.getEmail()) ? cabang.getEmail() : requestDetail.getEmail());
                            cabangRepository.save(cabang);
                        } else {
                            logger.error("Failed to Submit Limit Insurance, Branch not found ");
                            response.setStatusDesc(ResponseStatus.FAILED.getValue());
                            response.setStatus(ResponseStatus.FAILED.getCode());
                        }
                        insertAuditTrail(profile.getPreferred_username(), SUBMIT_LIMIT_INSURANCE, null, data.getBranchId());
                    } else if (request.getMode().equalsIgnoreCase(REQUEST_TYPE_NEW)) {
                        Cabang cabang = cabangRepository.findByCabangId(requestDetail.getBranchId());
                        if (cabang != null) {
                            logger.error("Failed to Submit Limit Insurance, Branch Already Exist ");
                            response.setStatusDesc(ResponseStatus.FAILED.getValue());
                            response.setStatus(ResponseStatus.FAILED.getCode());
                        } else {
                            Cabang newBranch = new Cabang();
                            newBranch.setCabangId(requestDetail.getBranchId());
                            newBranch.setCabangDesc(requestDetail.getBranchName());
                            newBranch.setTotalBalance(requestDetail.getLimitAsuransiLembesAmount());
                            newBranch.setTotalBalanceHT(requestDetail.getLimitAsuransiCashBoxAmount());
                            newBranch.setEmail(requestDetail.getEmail());
                            cabangRepository.save(newBranch);
                        }
                        insertAuditTrail(profile.getPreferred_username(), SUBMIT_NEW_BRANCH, null, data.getBranchId());
                    }

                    data.setRequestId(requestDetail.getRequestId());
                    data.setBranchId(requestDetail.getBranchId());
                    response.setData(data);
                } else {
                    logger.error("Failed to Submit, failed authorized ");
                    response.setStatusDesc(ResponseStatus.FAILED_AUTH.getValue());
                    response.setStatus(ResponseStatus.FAILED_AUTH.getCode());
                }
            } else {
                logger.error("Failed to Submit, Officer Otorisator not found ");
                response.setStatusDesc(ResponseStatus.FAILED.getValue());
                response.setStatus(ResponseStatus.FAILED.getCode());
            }

        } catch (Exception e) {
            logger.error("Failed to Submit ", e);
            response.setStatusDesc(ResponseStatus.FAILED.getValue());
            response.setStatus(ResponseStatus.FAILED.getCode());
        }
        return response;
    }

    private boolean validateOtorisator(String username, String password) {
        if (isLdapEnable) {
            try {
                Response<Object> execute = ldapServerRepository.sendLDAP(clientId, clientSecret, grantType, username, password, clientScope).execute();
                if (execute.isSuccessful()) {
                    logger.info("Success verificator {}", gson.toJson(username));
                    return true;
                } else {
                    logger.warn("Failed verificator {}", gson.toJson(username));
                    return false;
                }
            }catch (Exception e){
                logger.error("Fail executing Verificator. Error: ", gson.toJson(username), e);

            }
        } else {
            logger.info("LDAP send is disable, LDAP not send for username {}", gson.toJson(username));
            return false;
        }
        return true;
    }

    private String encodePassword(String password){
        Base64.Decoder decoder = Base64.getDecoder();
        return  new String(decoder.decode(password));
    }
    public void insertAuditTrail(String nik, String action, String transactionId, String branchId){
        TrxAuditTrail trxAuditTrail = new TrxAuditTrail();
        trxAuditTrail.setNik(nik);
        trxAuditTrail.setAction(action);
        trxAuditTrail.setCreateDateTime(LocalDateTime.now());
        trxAuditTrail.setTransactionId(transactionId);
        trxAuditTrail.setBranchId(branchId);
        trxAuditTrailRepository.save(trxAuditTrail);
    }

}
