package com.btpns.fin.service;

import com.btpns.fin.configuration.AmountConfig;
import com.btpns.fin.constant.*;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.HeadTellerApprovalReq;
import com.btpns.fin.model.request.InputCashCountRequest;
import com.btpns.fin.model.response.InputTransactionResponse;
import com.btpns.fin.repository.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static com.btpns.fin.constant.CommonConstant.*;
import static com.btpns.fin.constant.TrxStatus.PENDING;
import static com.btpns.fin.helper.CommonHelper.*;

@Service
public class CashCountService {
    private static final Logger logger = LoggerFactory.getLogger(CashCountService.class);

    @Autowired
    TrxKasBesarRepository trxKasBesarRepository;

    @Autowired
    TrxAmountDetailRepository trxAmountDetailRepository;
    @Autowired
    TrxAuditTrailRepository trxAuditTrailRepository;
    @Autowired
    BranchBalanceRepository branchBalanceRepository;
    @Autowired
    CabangRepository cabangRepository;
    @Autowired
    HeadTellerBalanceRepository headTellerBalanceRepository;
    @Autowired
    TrxHeadTellerRepository trxHeadTellerRepository;
    @Autowired
    OfficerRepository officerRepository;
    @Autowired
    OfficerNonProsperaRepository officerNonProsperaRepository;
    @Autowired
    OfficerImpl officerImpl;

    @Transactional
    public InputTransactionResponse inputCashCount(String nikInputer, InputCashCountRequest request){
        InputTransactionResponse response = new InputTransactionResponse();
        response.setRequestId(request.getRequestId());
        LocalDate period = LocalDate.parse(request.getPeriod());
        TrxKasBesar cashCount = new TrxKasBesar();

        try {
            if(validationDenomAndVerification(request.getBalanceDetails(), request.getNikVerification())){
                checkBranchBalance(request.getBranchId(), branchBalanceRepository);
                checkHTBranchBalance(request.getBranchId(), headTellerBalanceRepository);
                String trxType = cashCountType(TrxType.SAW.getCode(),TrxType.SAK.getCode(),TrxType.CC.getCode(), request.getType(),period,request.getBranchId());
                PICModel pic = enrichPICName(nikInputer, request.getNikVerification(), officerImpl, officerNonProsperaRepository);
                cashCount.setKasBesar(getTransactionId(trxType,period,request.getBranchId()), period, request.getBranchId(), PENDING.getValue(),
                        trxType, nikInputer, request.getNikVerification(), request.getTotalBalance(), request.getTotalBalance(), LocalDateTime.now(), LocalDateTime.now(), request.getRequestId(), pic.getInputerName(), pic.getTellerName());
                trxKasBesarRepository.save(cashCount);
                saveTrxDetail(request.getBalanceDetails(), cashCount.getTransactionId());
                response.setTransactionId(cashCount.getTransactionId());
                response.setStatus(ResponseStatus.SUCCESS.getCode());
                response.setStatusDesc(ResponseStatus.SUCCESS.getValue());
                insertAuditTrail(nikInputer, Action.SUBMIT_CASH_COUNT.getValue(),cashCount.getTransactionId(), cashCount.getBranchId());
            }else {
                response.setRequestId(request.getRequestId());
                response.setStatus(TrxStatus.FAILED.getCode());
                response.setStatusDesc(TrxStatus.FAILED.getValue());
                logger.error("Submit Cash CountFailed Request Not Complete");
            }
        }catch (Exception e){
            logger.error("Input Cash Count Error " + e.getMessage());
            response.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
            response.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
        }
        return response;

    }

    public String getTransactionId(String trxType, LocalDate date, String branchId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMdd");
        Integer lastTransactionId = trxKasBesarRepository.countTransaction(date, trxType, branchId);
        String transactionNo = String.format("%02d",1);
        if (lastTransactionId!=null){
            lastTransactionId += 1;
            transactionNo =String.format("%02d",lastTransactionId);
        }
        return trxType + formatter.format(date) + branchId + transactionNo;
    }

    public void saveTrxDetail(List<AmountDetail> amountDetails,String transactionId) {
        TrxAmountDetail trxAmountDetail = new TrxAmountDetail();
        trxAmountDetail.setTransactionId(transactionId);

        for (AmountDetail amountDetail : amountDetails) {
            switch (amountDetail.getId()) {
                case COIN_50:
                    trxAmountDetail.setC50Amount(amountDetail.getTotal());
                    trxAmountDetail.setC50Count(amountDetail.getCount());
                    break;
                case COIN_100:
                    trxAmountDetail.setC100Amount(amountDetail.getTotal());
                    trxAmountDetail.setC100Count(amountDetail.getCount());
                    break;
                case COIN_200:
                    trxAmountDetail.setC200Amount(amountDetail.getTotal());
                    trxAmountDetail.setC200Count(amountDetail.getCount());
                    break;
                case COIN_500:
                    trxAmountDetail.setC500Amount(amountDetail.getTotal());
                    trxAmountDetail.setC500Count(amountDetail.getCount());
                    break;
                case COIN_1K:
                    trxAmountDetail.setC1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setC1KCount(amountDetail.getCount());
                    break;
                case PAPER_1K:
                    trxAmountDetail.setP1KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP1KCount(amountDetail.getCount());
                    break;
                case PAPER_2K:
                    trxAmountDetail.setP2KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP2KCount(amountDetail.getCount());
                    break;
                case PAPER_5K:
                    trxAmountDetail.setP5KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP5KCount(amountDetail.getCount());
                    break;
                case PAPER_10K:
                    trxAmountDetail.setP10KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP10KCount(amountDetail.getCount());
                    break;
                case PAPER_20K:
                    trxAmountDetail.setP20KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP20KCount(amountDetail.getCount());
                    break;
                case PAPER_50K:
                    trxAmountDetail.setP50KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP50KCount(amountDetail.getCount());
                    break;
                case PAPER_75K:
                    trxAmountDetail.setP75KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP75KCount(amountDetail.getCount());
                    break;
                case PAPER_100K:
                    trxAmountDetail.setP100KAmount(amountDetail.getTotal());
                    trxAmountDetail.setP100KCount(amountDetail.getCount());
                    break;
            }
        }
        trxAmountDetailRepository.save(trxAmountDetail);
    }

    public String cashCountType(String saldoAwal, String saldoAkhir,String cashCount, String type,LocalDate period,String branchId){
        if (type.equals(AWAL_HARI)){
            TrxKasBesar checkSaldoAwal = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeAndStatusInOrderByCreateDateTimeDesc(period,branchId,saldoAwal, Set.of(TrxStatus.SUCCESS.getValue(),TrxStatus.APPROVED.getValue()));
            if (checkSaldoAwal!=null){
                return cashCount;
            }else {
                return saldoAwal;
            }
        }else if (type.equals(AKHIR_HARI)){
            return saldoAkhir;
        }
        return null;
    }
    public void insertAuditTrail(String nik, String action, String transactionId, String branchId){
        TrxAuditTrail trxAuditTrail = new TrxAuditTrail();
        trxAuditTrail.setNik(nik);
        trxAuditTrail.setAction(action);
        trxAuditTrail.setCreateDateTime(LocalDateTime.now());
        trxAuditTrail.setTransactionId(transactionId);
        trxAuditTrail.setBranchId(branchId);
        trxAuditTrailRepository.save(trxAuditTrail);
    }
    public CashCountDetailModel getDetailCashCount(String inputerNik, String transactionId){
        Gson gson = new Gson();
        AmountConfig amountConfig = new AmountConfig();
        CashCountDetailModel detailModel = trxKasBesarRepository.cashCountDetail(transactionId,TELLER_ROLE);
        TrxAmountDetail amountDetail = trxAmountDetailRepository.findByTransactionId(transactionId);
        if (detailModel.getNameVerification() == null) {
            List<OfficerNonProspera> officerName = officerNonProsperaRepository.getOfficerName(Arrays.asList(detailModel.getNikVerification()));
            if (officerName.size() > 0) {
                detailModel.setNameVerification(officerName.get(0).getName());
            }
        }
        List<AmountDetail> amountDetails = gson.fromJson(amountConfig.amount(), new TypeToken<List<AmountDetail>>() {
        }.getType());
        for (AmountDetail detail : amountDetails) {
            switch (detail.getId()) {
                case COIN_50:
                    detail.setCount(amountDetail.getC50Count());
                    detail.setTotal(amountDetail.getC50Amount());
                    break;
                case COIN_100:
                    detail.setCount(amountDetail.getC100Count());
                    detail.setTotal(amountDetail.getC100Amount());
                    break;
                case COIN_200:
                    detail.setCount(amountDetail.getC200Count());
                    detail.setTotal(amountDetail.getC200Amount());
                    break;
                case COIN_500:
                    detail.setCount(amountDetail.getC500Count());
                    detail.setTotal(amountDetail.getC500Amount());
                    break;
                case COIN_1K:
                    detail.setCount(amountDetail.getC1KCount());
                    detail.setTotal(amountDetail.getC1KAmount());
                    break;
                case PAPER_1K:
                    detail.setCount(amountDetail.getP1KCount());
                    detail.setTotal(amountDetail.getP1KAmount());
                    break;
                case PAPER_2K:
                    detail.setCount(amountDetail.getP2KCount());
                    detail.setTotal(amountDetail.getP2KAmount());
                    break;
                case PAPER_5K:
                    detail.setCount(amountDetail.getP5KCount());
                    detail.setTotal(amountDetail.getP5KAmount());
                    break;
                case PAPER_10K:
                    detail.setCount(amountDetail.getP10KCount());
                    detail.setTotal(amountDetail.getP10KAmount());
                    break;
                case PAPER_20K:
                    detail.setCount(amountDetail.getP20KCount());
                    detail.setTotal(amountDetail.getP20KAmount());
                    break;
                case PAPER_50K:
                    detail.setCount(amountDetail.getP50KCount());
                    detail.setTotal(amountDetail.getP50KAmount());
                    break;
                case PAPER_75K:
                    detail.setCount(amountDetail.getP75KCount());
                    detail.setTotal(amountDetail.getP75KAmount());
                    break;
                case PAPER_100K:
                    detail.setCount(amountDetail.getP100KCount());
                    detail.setTotal(amountDetail.getP100KAmount());
                    break;
            }
            detailModel.setTotal(amountDetail.getTotal());
        }
        detailModel.setBalanceDetails(amountDetails);
        return detailModel;
    }

    @Transactional
    public CommonResponse<InputTransactionResponse> cashCountVerification(HeadTellerApprovalReq request, Profile profile, boolean isAdmin) {
        CommonResponse<InputTransactionResponse> response = new CommonResponse<>();
        response.setType(CommonConstant.SUBMIT_APPROVAL);
        InputTransactionResponse inputTransactionResponse = new InputTransactionResponse();
        inputTransactionResponse.setRequestId(request.getRequestId());
        LocalDate period = LocalDate.parse(request.getPeriod());

        try {
            TrxKasBesar kasBesar = trxKasBesarRepository.findByTransactionId(request.getApprovalTransactionId());
            if (!validateVerification(kasBesar.getChecker(), request.getNikVerification())) {
                inputTransactionResponse.setTransactionId(request.getApprovalTransactionId());
                inputTransactionResponse.setStatus(ResponseStatus.FAILED.getCode());
                inputTransactionResponse.setStatusDesc(ResponseStatus.FAILED.getValue());
                response.setData(inputTransactionResponse);
                response.setStatus(ResponseStatus.FAILED.getCode());
                response.setStatusDesc(ResponseStatus.FAILED.getValue());
                return response;
            }
            TrxAmountDetail detail = trxAmountDetailRepository.findByTransactionId(kasBesar.getTransactionId());
            if (kasBesar.getStatus().equals(TrxStatus.APPROVED.getValue()) || kasBesar.getStatus().equals(TrxStatus.REJECTED.getValue())){
                inputTransactionResponse.setTransactionId(request.getApprovalTransactionId());
                inputTransactionResponse.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
                inputTransactionResponse.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
                response.setData(inputTransactionResponse);
                response.setStatus(ResponseStatus.FAILED.getCode());
                response.setStatusDesc(ResponseStatus.FAILED.getValue());
                return response;
            }
            BranchBalance branchBalance = branchBalanceRepository.findByBranchId(request.getBranchId());

            if (request.getStatus().equals(TrxStatus.APPROVED.getValue())) {
                if (branchBalance == null) {
                    if (request.getApprovalTransactionId().startsWith(TrxType.SAW.getCode())) {
                        BranchBalance newBranch = new BranchBalance();
                        newBranch.setAmount(detail);
                        newBranch.setBranchId(request.getBranchId());
                        newBranch.setCreateDateTime(LocalDateTime.now());
                        newBranch.setUpdateDateTime(LocalDateTime.now());
                        branchBalanceRepository.save(newBranch);
                    }
                }else {
                    if (!compareCashCount(detail, branchBalance)) {
                        branchBalance.setAmount(detail);
                        branchBalance.setUpdateDateTime(LocalDateTime.now());
                        branchBalanceRepository.save(branchBalance);
                        kasBesar.setAdditionalData(BALANCE_OVERRIDE);
                        kasBesar.setAdditionalInfo("Terdapat penyesuaian saldo vault");
                    }
                }
                if (request.getApprovalTransactionId().startsWith(TrxType.SAK.getCode())) {
                    checkExistingSaldoAkhir(period, request.getBranchId(), trxKasBesarRepository, trxAmountDetailRepository);
                }
                kasBesar.setStatus(TrxStatus.APPROVED.getValue());
                insertAuditTrail(profile.getPreferred_username(),Action.APPROVE_CASH_COUNT.getValue(), kasBesar.getTransactionId(), kasBesar.getBranchId());
            }else if (request.getStatus().equals(TrxStatus.REJECTED.getValue())){
                kasBesar.setStatus(TrxStatus.REJECTED.getValue());
                kasBesar.setReason(encodeIfnotNull(request.getReason()));
            }
            inputTransactionResponse.setTransactionId(request.getApprovalTransactionId());
            inputTransactionResponse.setStatusDesc(ResponseStatus.SUCCESS.getValue());
            inputTransactionResponse.setStatus(ResponseStatus.SUCCESS.getCode());
            if (isAdmin){
                insertAuditTrail(profile.getPreferred_username(),Action.REJECT_CASH_COUNT.getValue().concat(ACTION_ADMIN), kasBesar.getTransactionId(), kasBesar.getBranchId());
            }else {
                insertAuditTrail(profile.getPreferred_username(),Action.REJECT_CASH_COUNT.getValue(), kasBesar.getTransactionId(), kasBesar.getBranchId());
            }
        } catch (Exception e) {
            inputTransactionResponse.setTransactionId(request.getApprovalTransactionId());
            inputTransactionResponse.setStatusDesc(ResponseStatus.GENERAL_ERROR.getValue());
            inputTransactionResponse.setStatus(ResponseStatus.GENERAL_ERROR.getCode());
            response.setStatusDesc(ResponseStatus.FAILED.getValue());
            response.setStatus(ResponseStatus.FAILED.getCode());
            logger.error("Error verification Cash Count " + e);
        }
        response.setData(inputTransactionResponse);
        return response;

    }

    public CommonResponse<PendingCashCount> getPendingCashCount(String nik, String branchId){
        CommonResponse<PendingCashCount> response = new CommonResponse<>();
        PendingCashCount cashCount = new PendingCashCount();
        LocalDate period = LocalDate.now();
        TrxKasBesar trxKasBesar = trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeInAndStatusOrderByCreateDateTimeDesc(period,branchId, Set.of(TrxType.SAW.getCode(),TrxType.SAK.getCode(),TrxType.CC.getCode()),TrxStatus.PENDING.getValue());
        if (trxKasBesar != null){
            cashCount.setExists(true);
            cashCount.setLastTransactionId(trxKasBesar.getTransactionId());
        }else {
            cashCount.setExists(false);
        }
        cashCount.setPeriod(period);
        cashCount.setBranchId(branchId);
        response.setType(GET_PENDING_CASH_COUNT);
        response.setStatus(TrxStatus.SUCCESS.getCode());
        response.setStatusDesc(TrxStatus.SUCCESS.getValue());
        response.setData(cashCount);
        return response;
    }

    public boolean compareCashCount(TrxAmountDetail detail, BranchBalance balance){
        return detail.getC50Count().equals(balance.getC50Count()) && detail.getC50Amount().equals(balance.getC50Amount())
                && detail.getC100Count().equals(balance.getC100Count()) && detail.getC100Amount().equals(balance.getC100Amount())
                && detail.getC200Count().equals(balance.getC200Count()) && detail.getC200Amount().equals(balance.getC200Amount())
                && detail.getC500Count().equals(balance.getC500Count()) && detail.getC500Amount().equals(balance.getC500Amount())
                && detail.getC1KCount().equals(balance.getC1KCount()) && detail.getC1KAmount().equals(balance.getC1KAmount())
                && detail.getP1KCount().equals(balance.getP1KCount()) && detail.getP1KAmount().equals(balance.getP1KAmount())
                && detail.getP2KCount().equals(balance.getP2KCount()) && detail.getP2KAmount().equals(balance.getP2KAmount())
                && detail.getP5KCount().equals(balance.getP5KCount()) && detail.getP5KAmount().equals(balance.getP5KAmount())
                && detail.getP10KCount().equals(balance.getP10KCount()) && detail.getP10KAmount().equals(balance.getP10KAmount())
                && detail.getP20KCount().equals(balance.getP20KCount()) && detail.getP20KAmount().equals(balance.getP20KAmount())
                && detail.getP50KCount().equals(balance.getP50KCount()) && detail.getP50KAmount().equals(balance.getP50KAmount())
                && detail.getP75KCount().equals(balance.getP75KCount()) && detail.getP75KAmount().equals(balance.getP75KAmount())
                && detail.getP100KCount().equals(balance.getP100KCount()) && detail.getP100KAmount().equals(balance.getP100KAmount())
                ;
    }

    public CommonResponse<PendingAkhirHari> checkPendingAkhirHari(String nik, String branch){
        CommonResponse<PendingAkhirHari> response = new CommonResponse<>();
        PendingAkhirHari pendingAkhirHari = new PendingAkhirHari();
        LocalDate period = LocalDate.now();
        HeadTellerBalance headTellerBalance = headTellerBalanceRepository.findByBranchId(branch);
        if (checkPendingMHT(branch,period)){
            pendingAkhirHari.setStatusAkhirHari(ResponseStatus.AKHIRHARI_MHT_PENDING.getCode());
            pendingAkhirHari.setReason(ResponseStatus.AKHIRHARI_MHT_PENDING.getValue());
        }
        else if (checkPendingHT2V(branch,period)){
            pendingAkhirHari.setStatusAkhirHari(ResponseStatus.AKHIRHARI_HT2V_PENDING.getCode());
            pendingAkhirHari.setReason(ResponseStatus.AKHIRHARI_HT2V_PENDING.getValue());
        }else if (!headTellerBalance.getTotalAmount().equals(0.0)){
            pendingAkhirHari.setStatusAkhirHari(ResponseStatus.AKHIRHARI_BALANCE_HT_NOT_ZERO.getCode());
            pendingAkhirHari.setReason(ResponseStatus.AKHIRHARI_BALANCE_HT_NOT_ZERO.getValue());
        }else {
            pendingAkhirHari.setStatusAkhirHari(ResponseStatus.SUCCESS.getCode());
            pendingAkhirHari.setReason(ResponseStatus.SUCCESS.getValue());
        }
        response.setData(pendingAkhirHari);
        return response;
    }

    public boolean checkPendingHT2V(String branchId, LocalDate period){
        return trxHeadTellerRepository.findTopByPeriodAndBranchIdAndTypeAndStatusOrderByCreateDateTimeDesc(period,branchId,TrxType.HT2V.getCode(), PENDING.getValue()) != null;
    }
    public boolean checkPendingMHT(String branchId, LocalDate period){
        return trxKasBesarRepository.findTopByPeriodAndBranchIdAndTrxTypeAndStatusOrderByCreateDateTimeDesc(period,branchId,TrxType.MHT.getCode(), PENDING.getValue()) != null;
    }
    private static String encodeIfnotNull(String data) {
        if (data != null) {
            return StringEscapeUtils.escapeHtml4(data);
        }
        return null;
    }

    public boolean existInInterval(String nik, int interval, String branch, String type) {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusSeconds(interval);
        return trxKasBesarRepository.checkRequestIntervalCashCount(startDate, endDate, branch, nik, type) > 0;
    }
}
